// RBAC Utils Exports
export * from './validation';

// Additional utility functions for RBAC feature
import type { Role, Permission, PermissionMatrix } from '@/types/rbac';

/**
 * Sort roles by level (highest first)
 */
export function sortRolesByLevel(roles: Role[]): Role[] {
  return [...roles].sort((a, b) => b.level - a.level);
}

/**
 * Sort permissions by resource and action
 */
export function sortPermissionsByResource(permissions: Permission[]): Permission[] {
  return [...permissions].sort((a, b) => {
    if (a.resource !== b.resource) {
      return a.resource.localeCompare(b.resource);
    }
    return a.action.localeCompare(b.action);
  });
}

/**
 * Group permissions by resource
 */
export function groupPermissionsByResource(permissions: Permission[]): Record<string, Permission[]> {
  return permissions.reduce((groups, permission) => {
    const resource = permission.resource;
    if (!groups[resource]) {
      groups[resource] = [];
    }
    groups[resource].push(permission);
    return groups;
  }, {} as Record<string, Permission[]>);
}

/**
 * Get role display color based on level
 */
export function getRoleColor(level: number): string {
  if (level >= 9) return 'red';
  if (level >= 7) return 'orange';
  if (level >= 5) return 'yellow';
  if (level >= 3) return 'blue';
  return 'gray';
}

/**
 * Get permission action color
 */
export function getPermissionActionColor(action: string): string {
  switch (action) {
    case 'create': return 'green';
    case 'read': return 'blue';
    case 'update': return 'yellow';
    case 'delete': return 'red';
    case 'manage': return 'purple';
    default: return 'gray';
  }
}

/**
 * Calculate permission coverage for a role
 */
export function calculatePermissionCoverage(
  role: Role, 
  allPermissions: Permission[], 
  assignments: Record<string, Record<string, boolean>>
): number {
  if (allPermissions.length === 0) return 0;
  
  const assignedCount = allPermissions.filter(
    permission => assignments[role.id]?.[permission.id]
  ).length;
  
  return Math.round((assignedCount / allPermissions.length) * 100);
}

/**
 * Get role hierarchy path
 */
export function getRoleHierarchy(roles: Role[]): Role[] {
  return sortRolesByLevel(roles);
}

/**
 * Check if role is higher level than another
 */
export function isHigherLevelRole(role1: Role, role2: Role): boolean {
  return role1.level > role2.level;
}

/**
 * Get common permissions between roles
 */
export function getCommonPermissions(
  roleIds: string[],
  assignments: Record<string, Record<string, boolean>>,
  allPermissions: Permission[]
): Permission[] {
  if (roleIds.length === 0) return [];
  
  return allPermissions.filter(permission => 
    roleIds.every(roleId => assignments[roleId]?.[permission.id])
  );
}

/**
 * Get unique permissions for a role (not shared with others)
 */
export function getUniquePermissions(
  targetRoleId: string,
  allRoleIds: string[],
  assignments: Record<string, Record<string, boolean>>,
  allPermissions: Permission[]
): Permission[] {
  const otherRoleIds = allRoleIds.filter(id => id !== targetRoleId);
  
  return allPermissions.filter(permission => {
    const targetHasPermission = assignments[targetRoleId]?.[permission.id];
    const othersHavePermission = otherRoleIds.some(roleId => 
      assignments[roleId]?.[permission.id]
    );
    
    return targetHasPermission && !othersHavePermission;
  });
}

/**
 * Format role level display
 */
export function formatRoleLevel(level: number): string {
  const levelNames = {
    10: 'System Administrator',
    9: 'Super Administrator', 
    8: 'Administrator',
    7: 'Senior Manager',
    6: 'Manager',
    5: 'Team Lead',
    4: 'Senior User',
    3: 'Regular User',
    2: 'Limited User',
    1: 'Guest User',
    0: 'Restricted'
  };
  
  return levelNames[level as keyof typeof levelNames] || `Level ${level}`;
}

/**
 * Format permission action display
 */
export function formatPermissionAction(action: string): string {
  const actionNames = {
    'create': 'Create',
    'read': 'View',
    'update': 'Edit', 
    'delete': 'Delete',
    'manage': 'Manage',
    'list': 'List',
    'export': 'Export',
    'import': 'Import'
  };
  
  return actionNames[action as keyof typeof actionNames] || 
    action.charAt(0).toUpperCase() + action.slice(1);
}

/**
 * Generate role summary text
 */
export function generateRoleSummary(
  role: Role,
  permissionCount: number,
  userCount: number = 0
): string {
  const levelName = formatRoleLevel(role.level);
  return `${levelName} with ${permissionCount} permissions and ${userCount} users`;
}

/**
 * Generate permission summary text
 */
export function generatePermissionSummary(
  permission: Permission,
  roleCount: number = 0
): string {
  const actionName = formatPermissionAction(permission.action);
  return `${actionName} ${permission.resource} (assigned to ${roleCount} roles)`;
}
