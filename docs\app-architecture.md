# Architecture de l'Application - KYA Dashboards

## Structure des Dossiers

### Vue d'ensemble

```
src/
├── app/                          # Next.js App Router (minimal)
│   ├── (auth)/                   # Route group pour l'authentification
│   │   ├── login/
│   │   │   └── page.tsx          # Appelle LoginFeature
│   │   ├── register/
│   │   │   └── page.tsx          # Appelle RegisterFeature
│   │   └── layout.tsx            # Layout auth minimal
│   ├── (dashboard)/              # Route group pour les dashboards
│   │   ├── dashboard/
│   │   │   └── page.tsx          # Appelle DashboardFeature
│   │   ├── admin/
│   │   │   ├── users/
│   │   │   │   └── page.tsx      # Appelle UsersManagementFeature
│   │   │   ├── entities/
│   │   │   │   └── page.tsx      # Appelle EntitiesManagementFeature
│   │   │   └── rbac/
│   │   │       └── page.tsx      # Appelle RBACManagementFeature
│   │   ├── team/
│   │   │   ├── [teamId]/
│   │   │   │   └── page.tsx      # Appelle TeamDashboardFeature
│   │   │   └── data-entry/
│   │   │       └── page.tsx      # Appelle DataEntryFeature
│   │   └── layout.tsx            # Layout avec navigation
│   ├── globals.css
│   ├── layout.tsx                # Root layout avec providers
│   └── page.tsx                  # Page d'accueil
├── components/                   # Composants UI réutilisables uniquement
│   ├── ui/                       # Composants shadcn/ui
│   │   ├── button.tsx
│   │   ├── card.tsx
│   │   ├── form.tsx
│   │   ├── input.tsx
│   │   ├── table.tsx
│   │   └── ...
│   └── providers/                # Providers globaux
│       ├── auth-provider.tsx
│       ├── query-provider.tsx
│       └── theme-provider.tsx
├── features/                     # Fonctionnalités métier complètes
│   ├── auth/
│   │   ├── components/
│   │   │   ├── login-form.tsx
│   │   │   ├── register-form.tsx
│   │   │   └── auth-guard.tsx
│   │   ├── hooks/
│   │   │   ├── use-auth.ts
│   │   │   └── use-login.ts
│   │   ├── services/
│   │   │   ├── auth-service.ts
│   │   │   └── session-service.ts
│   │   ├── types/
│   │   │   └── index.ts
│   │   ├── utils/
│   │   │   └── validation.ts
│   │   └── actions.ts
│   ├── entities/
│   │   ├── components/
│   │   │   ├── entity-tree.tsx
│   │   │   ├── entity-form.tsx
│   │   │   ├── entity-card.tsx
│   │   │   └── entity-selector.tsx
│   │   ├── hooks/
│   │   │   ├── use-entities.ts
│   │   │   ├── use-entity-tree.ts
│   │   │   └── use-entity-mutations.ts
│   │   ├── services/
│   │   │   ├── entity-service.ts
│   │   │   └── hierarchy-service.ts
│   │   ├── types/
│   │   │   └── index.ts
│   │   ├── utils/
│   │   │   ├── tree-utils.ts
│   │   │   └── validation.ts
│   │   └── actions.ts
│   ├── users/
│   │   ├── components/
│   │   │   ├── user-list.tsx
│   │   │   ├── user-form.tsx
│   │   │   └── user-profile.tsx
│   │   ├── hooks/
│   │   │   ├── use-users.ts
│   │   │   └── use-user-mutations.ts
│   │   ├── services/
│   │   │   └── user-service.ts
│   │   ├── types/
│   │   │   └── index.ts
│   │   ├── utils/
│   │   │   └── validation.ts
│   │   └── actions.ts
│   ├── rbac/
│   │   ├── components/
│   │   │   ├── role-list.tsx
│   │   │   ├── role-form.tsx
│   │   │   ├── permission-matrix.tsx
│   │   │   └── user-roles.tsx
│   │   ├── hooks/
│   │   │   ├── use-roles.ts
│   │   │   ├── use-permissions.ts
│   │   │   └── use-rbac.ts
│   │   ├── services/
│   │   │   ├── rbac-service.ts
│   │   │   └── permission-service.ts
│   │   ├── types/
│   │   │   └── index.ts
│   │   ├── utils/
│   │   │   ├── rbac-utils.ts
│   │   │   └── validation.ts
│   │   └── actions.ts
│   ├── dashboard/
│   │   ├── components/
│   │   │   ├── director-dashboard.tsx
│   │   │   ├── manager-dashboard.tsx
│   │   │   ├── team-dashboard.tsx
│   │   │   ├── stats-card.tsx
│   │   │   ├── chart-wrapper.tsx
│   │   │   └── filters.tsx
│   │   ├── hooks/
│   │   │   ├── use-dashboard-stats.ts
│   │   │   └── use-dashboard-filters.ts
│   │   ├── services/
│   │   │   ├── dashboard-service.ts
│   │   │   └── stats-service.ts
│   │   ├── types/
│   │   │   └── index.ts
│   │   ├── utils/
│   │   │   └── chart-utils.ts
│   │   └── actions.ts
│   └── data-entry/
│       ├── components/
│       │   ├── data-entry-form.tsx
│       │   ├── data-history.tsx
│       │   └── validation-status.tsx
│       ├── hooks/
│       │   ├── use-data-entry.ts
│       │   └── use-data-validation.ts
│       ├── services/
│       │   ├── data-entry-service.ts
│       │   └── validation-service.ts
│       ├── types/
│       │   └── index.ts
│       ├── utils/
│       │   └── validation.ts
│       └── actions.ts
├── utils/                        # Utilitaires et services partagés
│   ├── supabase/                 # Configuration Supabase (existant)
│   │   ├── client.ts
│   │   ├── server.ts
│   │   └── types.ts
│   ├── rbac/
│   │   ├── guards.ts
│   │   ├── middleware.ts
│   │   └── utils.ts
│   └── validations/
│       ├── schemas.ts
│       └── common.ts
├── lib/                          # Utilitaires shadcn/ui
│   └── utils.ts                  # Fonction cn() pour classes
├── hooks/                        # Hooks globaux partagés
│   ├── use-current-user.ts
│   └── use-permissions.ts
└── types/                        # Types TypeScript globaux
    ├── auth.ts
    ├── entities.ts
    ├── users.ts
    ├── rbac.ts
    └── dashboard.ts
```

## Architecture par Fonctionnalités

### Principe

Chaque fonctionnalité métier (`features/`) suit une structure cohérente :

```
features/[feature]/
├── components/           # Composants spécifiques à la fonctionnalité
│   ├── [feature]-list.tsx
│   ├── [feature]-form.tsx
│   └── [feature]-card.tsx
├── hooks/               # Hooks personnalisés
│   ├── use-[feature].ts
│   └── use-[feature]-mutations.ts
├── services/            # Services métier (logique complexe)
│   └── [feature]-service.ts
├── types/               # Types TypeScript
│   └── index.ts
├── utils/               # Utilitaires spécifiques
│   ├── validation.ts
│   └── helpers.ts
└── actions.ts           # Server Actions (légers, appellent services)
```

### Exemple : Feature `entities`

```
features/entities/
├── components/
│   ├── entity-tree.tsx          # Arbre hiérarchique
│   ├── entity-form.tsx          # Formulaire CRUD
│   ├── entity-card.tsx          # Carte d'affichage
│   └── entity-selector.tsx      # Sélecteur d'entité
├── hooks/
│   ├── use-entities.ts          # Hook pour récupérer les entités
│   ├── use-entity-tree.ts       # Hook pour l'arbre hiérarchique
│   └── use-entity-mutations.ts  # Hook pour les mutations
├── services/
│   ├── entity-service.ts        # Logique métier des entités
│   └── hierarchy-service.ts     # Gestion de la hiérarchie
├── types/
│   └── index.ts                 # Types Entity, EntityTree, etc.
├── utils/
│   ├── tree-utils.ts            # Utilitaires pour l'arbre
│   └── validation.ts            # Schémas de validation
└── actions.ts                   # Server Actions (légers, appellent services)
```

### Principe des Services

Les services contiennent la logique métier complexe et sont appelés par les Server Actions :

```typescript
// features/entities/services/entity-service.ts
export class EntityService {
  static async createEntity(data: CreateEntityData) {
    // Validation métier
    // Logique de création
    // Gestion des relations
    return entity;
  }

  static async getEntityHierarchy(entityId: string) {
    // Logique complexe de récupération hiérarchique
    return hierarchy;
  }
}

// features/entities/actions.ts
'use server';
export async function createEntityAction(data: CreateEntityData) {
  await requireRBACPermission('entities.create');
  return await EntityService.createEntity(data);
}
```

## Providers et Contexte Global

### AuthProvider

```typescript
// components/providers/auth-provider.tsx
'use client';

import { createContext, useContext, useEffect, useState } from 'react';
import { User } from '@supabase/supabase-js';
import { createClient } from '@/utils/supabase/client';

interface AuthContextType {
  user: User | null;
  userProfile: UserProfile | null;
  userRoles: UserRole[];
  loading: boolean;
  signOut: () => Promise<void>;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [userRoles, setUserRoles] = useState<UserRole[]>([]);
  const [loading, setLoading] = useState(true);

  const supabase = createClient();

  useEffect(() => {
    // Récupération initiale de l'utilisateur
    const getUser = async () => {
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        setUser(user);
        await loadUserProfile(user.id);
      }
      setLoading(false);
    };

    // Écoute des changements d'authentification
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (session?.user) {
          setUser(session.user);
          await loadUserProfile(session.user.id);
        } else {
          setUser(null);
          setUserProfile(null);
          setUserRoles([]);
        }
        setLoading(false);
      }
    );

    getUser();

    return () => subscription.unsubscribe();
  }, []);

  const loadUserProfile = async (userId: string) => {
    // Charger le profil utilisateur et ses rôles
    const profile = await UserService.getUserProfile(userId);
    const roles = await RBACService.getUserRoles(userId);
    
    setUserProfile(profile);
    setUserRoles(roles);
  };

  const signOut = async () => {
    await supabase.auth.signOut();
  };

  const refreshUser = async () => {
    if (user) {
      await loadUserProfile(user.id);
    }
  };

  return (
    <AuthContext.Provider value={{
      user,
      userProfile,
      userRoles,
      loading,
      signOut,
      refreshUser
    }}>
      {children}
    </AuthContext.Provider>
  );
}

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
```

### Root Layout avec Providers

```typescript
// app/layout.tsx
import { AuthProvider } from '@/components/providers/auth-provider';
import { QueryProvider } from '@/components/providers/query-provider';
import { ThemeProvider } from '@/components/providers/theme-provider';

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="fr">
      <body>
        <ThemeProvider>
          <QueryProvider>
            <AuthProvider>
              {children}
            </AuthProvider>
          </QueryProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
```

## Dashboards par Rôle

### Structure des Dashboards

```
app/(dashboard)/dashboard/
├── page.tsx                     # Dashboard principal (routage par rôle)
├── loading.tsx
├── error.tsx
└── [Composants dans features/dashboard/components/]
```

### Pages App Minimales

```typescript
// app/(dashboard)/dashboard/page.tsx
import { DashboardFeature } from '@/features/dashboard/components/dashboard-feature';

export default function DashboardPage() {
  return <DashboardFeature />;
}

// features/dashboard/components/dashboard-feature.tsx
'use client';

import { useAuth } from '@/components/providers/auth-provider';
import { DirectorDashboard } from './director-dashboard';
import { ManagerDashboard } from './manager-dashboard';
import { TeamDashboard } from './team-dashboard';

export function DashboardFeature() {
  const { userRoles, loading } = useAuth();

  if (loading) return <DashboardSkeleton />;

  const primaryRole = userRoles[0]?.role.name;

  switch (primaryRole) {
    case 'DIRECTEUR':
      return <DirectorDashboard />;
    case 'MANAGER_DIRECTION':
    case 'MANAGER_EQUIPE':
      return <ManagerDashboard />;
    default:
      return <TeamDashboard />;
  }
}
```

### Dashboards Spécifiques

#### 1. Dashboard Directeur (`DIRECTEUR`)
- **Vue globale** : Toutes les directions et équipes
- **KPIs consolidés** : Métriques agrégées
- **Comparaisons** : Performance entre directions
- **Tendances** : Évolution temporelle

```typescript
// components/dashboard/director-dashboard.tsx
interface DirectorDashboardProps {
  entities: Entity[];
  stats: GlobalStats;
  trends: TrendData[];
}
```

#### 2. Dashboard Manager Direction (`MANAGER_DIRECTION`)
- **Vue direction** : Sa direction et équipes sous-jacentes
- **Performance équipes** : Comparaison des équipes
- **Alertes** : Équipes en difficulté
- **Objectifs** : Suivi des objectifs de direction

#### 3. Dashboard Manager Équipe (`MANAGER_EQUIPE`)
- **Vue équipe** : Son équipe uniquement
- **Détail des données** : Données granulaires
- **Validation** : Données à valider
- **Saisie** : Accès aux formulaires de saisie

#### 4. Dashboard Saisie (`SAISIE_DONNEES`)
- **Formulaires** : Saisie des données journalières
- **Historique** : Ses saisies précédentes
- **Statut** : Validation des données
- **Aide** : Guide de saisie

## Navigation et Layout

### Layout Principal

```typescript
// app/(dashboard)/layout.tsx
export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="flex h-screen">
      <Sidebar />
      <div className="flex-1 flex flex-col">
        <Header />
        <main className="flex-1 overflow-auto p-6">
          <Breadcrumb />
          {children}
        </main>
      </div>
    </div>
  );
}
```

### Navigation Adaptative

La navigation s'adapte selon le rôle de l'utilisateur :

```typescript
// components/layout/sidebar.tsx
const getNavigationItems = (userRole: UserRole, userEntity: Entity) => {
  const baseItems = [
    { href: '/dashboard', label: 'Tableau de bord', icon: BarChart3 }
  ];

  switch (userRole) {
    case 'DIRECTEUR':
      return [
        ...baseItems,
        { href: '/dashboard/global', label: 'Vue globale', icon: Globe },
        { href: '/admin/entities', label: 'Gestion entités', icon: Building },
        { href: '/admin/users', label: 'Utilisateurs', icon: Users },
      ];
    
    case 'MANAGER_EQUIPE':
      return [
        ...baseItems,
        { href: `/team/${userEntity.id}`, label: 'Mon équipe', icon: Users },
        { href: '/team/data-entry', label: 'Saisie données', icon: Edit },
      ];
    
    // ... autres rôles
  }
};
```

## Gestion RBAC

### Guards de Route

```typescript
// utils/rbac/guards.ts
import { redirect } from 'next/navigation';
import { createClient } from '@/utils/supabase/server';
import { RBACService } from '@/features/rbac/services/rbac-service';

export async function requireRBACPermission(
  permission: string,
  entityId?: string
) {
  const supabase = createClient();
  const { data: { user } } = await supabase.auth.getUser();
  
  if (!user) redirect('/login');

  const hasPermission = await RBACService.checkUserPermission(
    user.id,
    permission,
    entityId
  );

  if (!hasPermission) {
    throw new Error('Accès non autorisé');
  }

  return user;
}
```

### Middleware de Protection

```typescript
// middleware.ts
import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';
import { RBACService } from '@/features/rbac/services/rbac-service';

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Routes publiques
  if (pathname.startsWith('/login') || pathname.startsWith('/register')) {
    return NextResponse.next();
  }

  // Vérification authentification
  const supabase = createClient();
  const { data: { user } } = await supabase.auth.getUser();
  
  if (!user) {
    return NextResponse.redirect(new URL('/login', request.url));
  }

  // Vérification RBAC par route
  const requiredPermission = getRequiredRBACPermission(pathname);
  if (requiredPermission) {
    const hasPermission = await RBACService.checkUserPermission(
      user.id,
      requiredPermission
    );
    if (!hasPermission) {
      return NextResponse.redirect(new URL('/unauthorized', request.url));
    }
  }

  return NextResponse.next();
}

export const config = {
  matcher: ['/((?!api|_next/static|_next/image|favicon.ico).*)'],
};
```

## Server Actions (Sans API Routes)

### Structure des Actions avec Services

```typescript
// features/entities/actions.ts
'use server';

import { requireRBACPermission } from '@/utils/rbac/guards';
import { EntityService } from './services/entity-service';
import { revalidatePath } from 'next/cache';

export async function createEntityAction(data: CreateEntityData) {
  // Vérification RBAC
  await requireRBACPermission('entities.create');
  
  // Délégation au service
  const result = await EntityService.createEntity(data);
  
  // Revalidation du cache
  revalidatePath('/admin/entities');
  
  return result;
}

export async function updateEntityAction(id: string, data: UpdateEntityData) {
  await requireRBACPermission('entities.update');
  
  const result = await EntityService.updateEntity(id, data);
  
  revalidatePath('/admin/entities');
  revalidatePath(`/admin/entities/${id}`);
  
  return result;
}

export async function deleteEntityAction(id: string) {
  await requireRBACPermission('entities.delete');
  
  const result = await EntityService.deleteEntity(id);
  
  revalidatePath('/admin/entities');
  
  return result;
}
```

### Services avec Logique Métier

```typescript
// features/entities/services/entity-service.ts
import { createClient } from '@/utils/supabase/server';
import { entitySchema } from '../utils/validation';
import { AuditService } from '@/utils/services/audit-service';

export class EntityService {
  static async createEntity(data: CreateEntityData) {
    // Validation
    const validatedData = entitySchema.parse(data);
    
    const supabase = createClient();
    
    // Création de l'entité
    const { data: entity, error } = await supabase
      .from('entities')
      .insert(validatedData)
      .select()
      .single();

    if (error) throw error;

    // Audit trail
    await AuditService.log('entity.created', entity.id, validatedData);

    return { success: true, entity };
  }

  static async getEntityHierarchy(entityId: string) {
    const supabase = createClient();
    
    // Requête récursive pour la hiérarchie
    const { data, error } = await supabase.rpc('get_entity_hierarchy', {
      entity_id: entityId
    });

    if (error) throw error;

    return data;
  }

  static async updateEntity(id: string, data: UpdateEntityData) {
    const validatedData = entitySchema.partial().parse(data);
    
    const supabase = createClient();
    
    const { data: entity, error } = await supabase
      .from('entities')
      .update(validatedData)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;

    await AuditService.log('entity.updated', id, validatedData);

    return { success: true, entity };
  }

  static async deleteEntity(id: string) {
    const supabase = createClient();
    
    const { error } = await supabase
      .from('entities')
      .delete()
      .eq('id', id);

    if (error) throw error;

    await AuditService.log('entity.deleted', id);

    return { success: true };
  }
}
```

## Patterns de Développement

### 1. Composition de Composants

```typescript
// Composant de base réutilisable
<StatsCard
  title="Tâches complétées"
  value={stats.completedTasks}
  trend={stats.tasksTrend}
  icon={CheckCircle}
/>

// Composition dans le dashboard
<DashboardGrid>
  <StatsCard {...tasksStats} />
  <StatsCard {...hoursStats} />
  <ChartCard {...trendsData} />
</DashboardGrid>
```

### 2. Hooks Personnalisés

```typescript
// hooks/use-dashboard-stats.ts
export function useDashboardStats(entityId?: string) {
  return useQuery({
    queryKey: ['dashboard-stats', entityId],
    queryFn: () => fetchDashboardStats(entityId),
    refetchInterval: 5 * 60 * 1000, // 5 minutes
  });
}
```

### 3. Validation avec Zod

```typescript
// lib/validations/entities.ts
export const entitySchema = z.object({
  name: z.string().min(1, 'Le nom est requis'),
  type: z.enum(['DIRECTION', 'EQUIPE']),
  parentId: z.string().uuid().optional(),
  managerId: z.string().uuid().optional(),
});
```

## Performance et Optimisation

### 1. Lazy Loading des Composants

```typescript
const DirectorDashboard = lazy(() => import('./director-dashboard'));
const ManagerDashboard = lazy(() => import('./manager-dashboard'));

// Chargement conditionnel selon le rôle
<Suspense fallback={<DashboardSkeleton />}>
  {userRole === 'DIRECTEUR' && <DirectorDashboard />}
  {userRole === 'MANAGER_EQUIPE' && <ManagerDashboard />}
</Suspense>
```

### 2. Mise en Cache

```typescript
// Mise en cache des données fréquemment utilisées
export const getEntitiesTree = cache(async (userId: string) => {
  return await fetchEntitiesTree(userId);
});
```

### 3. Optimisation des Requêtes

```typescript
// Requêtes optimisées avec joins
const getDashboardData = async (entityId: string) => {
  return await supabase
    .from('entities')
    .select(`
      *,
      manager:persons(*),
      children:entities(*),
      stats:entity_stats(*)
    `)
    .eq('id', entityId)
    .single();
};
```

---

Cette architecture offre une base solide, extensible et maintenable pour votre application KYA Dashboards.