-- Script pour corriger les permissions manquantes de l'admin
-- Ce script ajoute les permissions manquantes et les assigne au rôle SUPER_ADMIN

-- 1. Ajouter les permissions manquantes
INSERT INTO permissions (name, display_name, resource, action, is_system_permission) VALUES
('admin.rbac.manage', 'Gérer les rôles et permissions (Admin)', 'admin_rbac', 'manage', true),
('admin.users.read', 'Consulter les utilisateurs (Admin)', 'admin_users', 'read', true),
('admin.users.manage', 'G<PERSON>rer les utilisateurs (Admin)', 'admin_users', 'manage', true),
('admin.entities.manage', 'Gérer les entités (Admin)', 'admin_entities', 'manage', true)
ON CONFLICT (name) DO NOTHING;

-- 2. Assigner toutes les permissions au SUPER_ADMIN (y compris les nouvelles)
INSERT INTO roles_permissions (role_id, permission_id)
SELECT r.id, p.id 
FROM roles r, permissions p 
WHERE r.name = 'SUPER_ADMIN'
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- 3. Assigner les nouvelles permissions au DIRECTOR aussi
INSERT INTO roles_permissions (role_id, permission_id)
SELECT
    (SELECT id FROM roles WHERE name = 'DIRECTOR'),
    p.id
FROM permissions p
WHERE p.name IN (
    'admin.users.read', 'admin.users.manage',
    'admin.entities.manage',
    'admin.rbac.manage'
)
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- 4. Vérification : Afficher toutes les permissions du SUPER_ADMIN
SELECT 
    r.name as role_name, 
    p.name as permission_name,
    p.display_name as permission_display_name
FROM roles_permissions rp
JOIN roles r ON rp.role_id = r.id
JOIN permissions p ON rp.permission_id = p.id
WHERE r.name = 'SUPER_ADMIN'
ORDER BY p.name;

-- 5. Vérification : Afficher toutes les permissions du DIRECTOR
SELECT 
    r.name as role_name, 
    p.name as permission_name,
    p.display_name as permission_display_name
FROM roles_permissions rp
JOIN roles r ON rp.role_id = r.id
JOIN permissions p ON rp.permission_id = p.id
WHERE r.name = 'DIRECTOR'
ORDER BY p.name;
