'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Loader2 } from 'lucide-react';
import { useAuth } from '../hooks/use-auth';
import type { AuthGuardProps } from '@/types/auth';

export function AuthGuard({ 
  children, 
  requiredPermissions = [], 
  requiredRoles = [], 
  fallback, 
  redirectTo = '/auth/login' 
}: AuthGuardProps) {
  const router = useRouter();
  const { user, isLoading, isAuthenticated } = useAuth();
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    const checkAuth = async () => {
      // Wait for auth to finish loading
      if (isLoading) {
        return;
      }

      setIsChecking(false);

      // If not authenticated, redirect to login
      if (!isAuthenticated || !user) {
        router.push(redirectTo);
        return;
      }

      // TODO: Implement permission and role checks when RBAC is fully implemented
      // For now, just check authentication
      if (requiredPermissions.length > 0 || requiredRoles.length > 0) {
        console.warn('Permission and role checks not yet implemented');
        // In the future, this will check user permissions and roles
        // and redirect or show fallback if user doesn't have required access
      }
    };

    checkAuth();
  }, [isLoading, isAuthenticated, user, requiredPermissions, requiredRoles, router, redirectTo]);

  // Show loading state
  if (isLoading || isChecking) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="flex flex-col items-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin" />
          <p className="text-sm text-muted-foreground">Vérification de l'authentification...</p>
        </div>
      </div>
    );
  }

  // If not authenticated, don't render children (redirect will happen)
  if (!isAuthenticated || !user) {
    return fallback || null;
  }

  // TODO: Add permission/role checks here when RBAC is implemented
  // For now, if authenticated, render children
  return <>{children}</>;
}

/**
 * Higher-order component for protecting pages
 */
export function withAuthGuard<P extends object>(
  Component: React.ComponentType<P>,
  guardProps?: Omit<AuthGuardProps, 'children'>
) {
  return function AuthGuardedComponent(props: P) {
    return (
      <AuthGuard {...guardProps}>
        <Component {...props} />
      </AuthGuard>
    );
  };
}

/**
 * Component for protecting specific UI elements
 */
export function ProtectedElement({ 
  children, 
  requiredPermissions = [], 
  requiredRoles = [],
  fallback = null 
}: {
  children: React.ReactNode;
  requiredPermissions?: string[];
  requiredRoles?: string[];
  fallback?: React.ReactNode;
}) {
  const { user, isAuthenticated } = useAuth();

  // If not authenticated, don't show element
  if (!isAuthenticated || !user) {
    return <>{fallback}</>;
  }

  // TODO: Implement permission and role checks when RBAC is fully implemented
  // For now, just check authentication
  if (requiredPermissions.length > 0 || requiredRoles.length > 0) {
    console.warn('Permission and role checks not yet implemented');
    // In the future, this will check user permissions and roles
    // and show fallback if user doesn't have required access
  }

  return <>{children}</>;
}

/**
 * Hook for conditional rendering based on permissions
 */
export function useAuthGuard(
  requiredPermissions: string[] = [],
  requiredRoles: string[] = []
) {
  const { user, isAuthenticated, isLoading } = useAuth();

  // TODO: Implement actual permission and role checks
  const hasAccess = isAuthenticated && user !== null;
  const isCheckingAccess = isLoading;

  return {
    hasAccess,
    isCheckingAccess,
    user,
    isAuthenticated,
  };
}
