# Concept de Redesign : Dashboard des Installations

## 1. Philosophie du Design

L'objectif est de créer une interface qui soit à la fois **moderne, épurée et intuitive**. Nous voulons que les utilisateurs puissent saisir les informations clés en un coup d'œil, tout en ayant une expérience visuelle agréable et professionnelle. Les principes directeurs sont :

-   **Clar<PERSON> avant tout :** Hiérarchiser l'information pour guider l'œil de l'utilisateur.
-   **Cohérence visuelle :** Utiliser une palette de couleurs et une typographie harmonieuses.
-   **Minimalisme fonctionnel :** Réduire le bruit visuel en ne gardant que les éléments essentiels.
-   **Espacement généreux :** Utiliser les espaces blancs pour aérer l'interface et améliorer la lisibilité.

## 2. Agencement Général - Onglet "Aperçu"

L'agencement actuel est fonctionnel mais peut être optimisé pour une meilleure lecture. Nous proposons une nouvelle structure en trois niveaux hiérarchiques.

```mermaid
graph TD
    subgraph "Nouvel Agencement de l'Aperçu"
        A["Filtres Globaux"]
        --> B{KPIs Principaux - 4 cartes}
        --> C{Graphiques Clés - 2 cartes}
        --> D["Carte Interactive & Données Détaillées"]
    end

    style A fill:#f2f2f2,stroke:#333,stroke-width:2px
    style B fill:#e6f3ff,stroke:#007bff,stroke-width:2px
    style C fill:#e6f9f5,stroke:#1ca18c,stroke-width:2px
    style D fill:#fff9e6,stroke:#ffc107,stroke-width:2px
```

**Description de l'agencement :**

1.  **Filtres Globaux :** Positionnés en haut, ils restent accessibles mais sont visuellement plus légers.
2.  **KPIs Principaux :** Une rangée de 4 `KpiCard` reste en haut pour une vision synthétique immédiate.
3.  **Graphiques Clés :** Juste en dessous, deux `ChartContainer` affichent les répartitions les plus importantes (par type et par client).
4.  **Carte Interactive et Données Détaillées :** La carte prendra plus de place et sera potentiellement juxtaposée à un tableau de données pour un contexte plus riche.

## 3. Style des Composants

### 3.1. `KpiCard`

Le nouveau design sera plus sobre et mettra l'accent sur la typographie.

-   **Fond et Bordure :** Un fond blanc pur avec une ombre portée subtile (`box-shadow`) au survol, et une fine bordure colorée (2px à gauche) pour indiquer le statut (succès, erreur, info).
-   **Icône :** L'icône sera plus grande et placée dans un cercle coloré pour un meilleur impact visuel.
-   **Typographie :**
    -   `Titre` : Plus petit, en majuscules et avec un espacement des lettres (letter-spacing).
    -   `Valeur` : Grande et audacieuse pour attirer l'œil.
    -   `Sous-titre` : En gris clair pour une information secondaire.

### 3.2. `ChartContainer`

L'objectif est de rendre les graphiques plus lisibles et moins chargés.

-   **Header :** Le titre sera plus grand et le sous-titre plus discret. Les actions (exporter, etc.) seront regroupées sous une icône "trois points" plus discrète.
-   **Fond du Graphique :** Pas de fond coloré, on utilise la grille pour la structure.
-   **Info-bulles (Tooltips) :** Design personnalisé pour correspondre à la nouvelle palette de couleurs, avec des informations claires et concises.

## 4. Palette de Couleurs et Typographie

### 4.1. Palette de Couleurs

Nous proposons une palette moderne et professionnelle, avec des couleurs primaires douces et des accents vifs pour les statuts.

-   **Primaire (Bleu Profond) :** `#1A237E` - Pour les titres principaux, les boutons actifs et les éléments de navigation.
-   **Secondaire (Gris Froid) :** `#455A64` - Pour le texte du corps et les sous-titres.
-   **Accent (Turquoise) :** `#00ACC1` - Pour les liens, les icônes et les graphiques.
-   **Fond :** `#F8F9FA` - Un gris très clair pour éviter le blanc pur et réduire la fatigue oculaire.
-   **Statuts :**
    -   `Succès (Vert)`: `#2E7D32`
    -   `Avertissement (Orange)`: `#FF8F00`
    -   `Erreur (Rouge)`: `#C62828`

### 4.2. Typographie

Nous utiliserons une seule famille de polices de caractères pour la cohérence, disponible via Google Fonts.

-   **Police :** **Inter**
    -   **Titres :** `Inter Bold` (ex: 24px pour les titres de page, 18px pour les titres de carte).
    -   **Corps de texte :** `Inter Regular` (16px).
    -   **Labels et sous-titres :** `Inter Medium` (14px).

Ce concept de design servira de base pour la phase d'implémentation, en fournissant une direction claire pour moderniser l'interface du tableau de bord.