'use client';


import {
  Construction,
  TrendingUp,
  <PERSON><PERSON><PERSON><PERSON>gle,
  Map<PERSON>in,
  BarChart3,
  <PERSON><PERSON><PERSON>
} from 'lucide-react';
import { KpiCard } from '@/components/dashboard/kpi-card';
import { ChartContainer } from '@/components/dashboard/chart-container';
import { <PERSON><PERSON><PERSON> as RechartsPieChart, Pie, Cell, ResponsiveContainer, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, LineChart, Line } from 'recharts';
import { InstallationMap } from '../installation-map';
import { useInstallationKpis } from '../../hooks/use-installation-kpis';
import { useInstallationCharts } from '../../hooks/use-installation-charts';
import { Installation } from '../../types';

interface ApercuTabProps {
  installations: Installation[];
}

export function ApercuTab({ installations }: ApercuTabProps) {
  const { kpis, isLoading: kpisLoading } = useInstallationKpis(installations);
  const { chartData, isLoading: chartsLoading } = useInstallationCharts(installations);

  const isLoading = kpisLoading || chartsLoading;

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-kya-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* KPI Cards Techniques */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
        <KpiCard
          title="Toutes les Installations"
          value={kpis.total_installations}
          subtitle="Nombre total de projets"
          icon={<Construction />}
          variant="default"
        />
        <KpiCard
          title="Installations Actives"
          value={kpis.active_installations}
          subtitle="En cours de réalisation"
          icon={<TrendingUp />}
          variant="info"
        />

        <KpiCard
          title="Progression Moyenne"
          value={`${kpis.average_progress}%`}
          subtitle="Toutes installations actives"
          icon={<TrendingUp />}
          variant="success"
          // trend={{
          //   value: 5.2,
          //   isPositive: true
          // }}
        />

        <KpiCard
          title="En Retard"
          value={kpis.overdue_installations}
          subtitle="Dépassent la date prévue"
          icon={<AlertTriangle />}
          variant={kpis.overdue_installations > 0 ? "error" : "success"}
        />
      </div>

      {/* Graphiques de Répartition */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <ChartContainer
          title="Répartition par Type de Produit"
          subtitle="Distribution des installations par type"
        >
          <ResponsiveContainer width="100%" height={300}>
            <RechartsPieChart>
              <Pie
                data={chartData.productTypeDistribution}
                cx="50%"
                cy="50%"
                outerRadius={80}
                dataKey="value"
                label={({ name, percentage }) => `${name}: ${percentage}%`}
              >
                {chartData.productTypeDistribution.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip />
            </RechartsPieChart>
          </ResponsiveContainer>
        </ChartContainer>

        <ChartContainer
          title="Répartition par Type de Client"
          subtitle="Distribution des installations par type de client"
        >
          <ResponsiveContainer width="100%" height={300}>
            <BarChart data={chartData.clientDistribution}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" angle={-45} textAnchor="end" height={80} />
              <YAxis />
              <Tooltip />
              <Bar dataKey="value" fill="#1ca18c" />
            </BarChart>
          </ResponsiveContainer>
        </ChartContainer>
      </div>


      {/* Carte Interactive */}
      <div className="border rounded-lg overflow-hidden bg-card">
        <div className="p-3 border-b bg-muted/10">
          <h3 className="font-medium text-sm">Localisation des Installations</h3>
          <p className="text-xs text-muted-foreground">Vue géographique avec statuts</p>
        </div>
        <InstallationMap
          installations={installations}
          height="400px"
          noWrapper={true}
        />
      </div>
    </div>
  );
}
