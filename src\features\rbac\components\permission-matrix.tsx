'use client';

import { useState, useEffect, useTransition } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Shield, Search, Filter, Save, RotateCcw } from 'lucide-react';
import type { Permission } from '@/types/rbac';
import { getPermissionMatrixAction, updateRolePermissionsAction } from '../actions';
import { toast } from 'sonner';

interface PermissionMatrixProps {
  selectedRoleId: string;
}

interface PermissionMatrixData {
  permissions: Permission[];
  rolePermissions: string[];
}

export function PermissionMatrix({ selectedRoleId }: PermissionMatrixProps) {
  const [data, setData] = useState<PermissionMatrixData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [resourceFilter, setResourceFilter] = useState<string>('all');
  const [isPending, startTransition] = useTransition();
  const [currentPermissions, setCurrentPermissions] = useState<Set<string>>(new Set());
  const [originalPermissions, setOriginalPermissions] = useState<Set<string>>(new Set());

  const hasChanges = JSON.stringify(Array.from(currentPermissions).sort()) !== JSON.stringify(Array.from(originalPermissions).sort());

  const fetchPermissions = async () => {
    if (!selectedRoleId) return;
    setIsLoading(true);
    const result = await getPermissionMatrixAction();
    if (result.data) {
      const rolePermissions = result.data.assignments.get(selectedRoleId) || new Set();
      setData({ permissions: result.data.permissions, rolePermissions: Array.from(rolePermissions) });
      const initialPerms = new Set(rolePermissions);
      setCurrentPermissions(initialPerms);
      setOriginalPermissions(new Set(initialPerms));
    } else {
      toast.error('Failed to fetch permissions.');
    }
    setIsLoading(false);
  };

  useEffect(() => {
    fetchPermissions();
  }, [selectedRoleId]);

  const handlePermissionToggle = (permissionId: string) => {
    const newPermissions = new Set(currentPermissions);
    if (newPermissions.has(permissionId)) {
      newPermissions.delete(permissionId);
    } else {
      newPermissions.add(permissionId);
    }
    setCurrentPermissions(newPermissions);
  };

  const handleSaveChanges = () => {
    startTransition(async () => {
      const result = await updateRolePermissionsAction(selectedRoleId, Array.from(currentPermissions));
      if (result.error) {
        toast.error(`Error: ${result.error}`);
      } else {
        toast.success('Permissions updated successfully.');
        await fetchPermissions();
      }
    });
  };

  const handleResetChanges = () => {
    setCurrentPermissions(new Set(originalPermissions));
  };

  const getUniqueResources = () => {
    if (!data) return [];
    return Array.from(new Set(data.permissions.map(p => p.resource))).sort();
  };
  
  const filteredPermissions = data?.permissions.filter(p =>
    (p.name.toLowerCase().includes(searchTerm.toLowerCase()))
    && (resourceFilter === 'all' || p.resource === resourceFilter)
  ) || [];

  if (isLoading) return <Card><CardHeader><CardTitle>Loading permissions...</CardTitle></CardHeader></Card>;
  if (!data) return <Card><CardHeader><CardTitle>No permission data.</CardTitle></CardHeader></Card>;

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2"><Shield className="h-5 w-5" />Permission Matrix</CardTitle>
            <CardDescription>Manage permissions for the selected role.</CardDescription>
          </div>
          {hasChanges && (
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" onClick={handleResetChanges} disabled={isPending}>
                <RotateCcw className="h-4 w-4 mr-2" />Cancel
              </Button>
              <Button size="sm" onClick={handleSaveChanges} disabled={isPending}>
                {isPending ? "Saving..." : <><Save className="h-4 w-4 mr-2" />Save Changes</>}
              </Button>
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex items-center space-x-2">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input placeholder="Search permissions..." value={searchTerm} onChange={(e) => setSearchTerm(e.target.value)} className="pl-8" />
            </div>
            <Select value={resourceFilter} onValueChange={setResourceFilter}>
              <SelectTrigger className="w-48"><SelectValue placeholder="Filter by resource" /></SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Resources</SelectItem>
                {getUniqueResources().map(r => <SelectItem key={r} value={r}>{r}</SelectItem>)}
              </SelectContent>
            </Select>
          </div>
          <div className="border rounded-md overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-80">Permission</TableHead>
                  <TableHead className="text-center">Assigned</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredPermissions.map(permission => (
                  <TableRow key={permission.id}>
                    <TableCell>
                      <div className="font-medium">{permission.name}</div>
                      <div className="text-sm text-muted-foreground">{permission.description}</div>
                    </TableCell>
                    <TableCell className="text-center">
                      <Checkbox
                        checked={currentPermissions.has(permission.id)}
                        onCheckedChange={() => handlePermissionToggle(permission.id)}
                        disabled={isPending}
                      />
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
