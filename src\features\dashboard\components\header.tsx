'use client';

import { useAuth } from '@/components/providers/auth-provider';
import { Button } from '@/components/ui/button';
import { signOut } from '@/features/auth/actions';
import { PanelLeftOpen, PanelLeftClose } from 'lucide-react';

interface HeaderProps {
    isSecondarySidebarOpen: boolean;
    toggleSecondarySidebar: () => void;
}

export function Header({ isSecondarySidebarOpen, toggleSecondarySidebar }: HeaderProps) {
  const { user } = useAuth();

  const handleSignOut = async () => {
    await signOut();
  };

  return (
    <header className="bg-white border-b h-16 flex items-center px-6 justify-between">
      <div className="flex items-center gap-4">
        <Button variant="ghost" size="icon" onClick={toggleSecondarySidebar}>
            {isSecondarySidebarOpen ? <PanelLeftClose /> : <PanelLeftOpen />}
        </Button>
        <h1 className="font-semibold">Dashboard</h1>
      </div>
      <div className="flex items-center gap-4">
        <span className="text-sm text-gray-600">
          Bon<PERSON>r, {user?.email}
        </span>
        <form action={handleSignOut}>
          <Button variant="outline" size="sm">Déconnexion</Button>
        </form>
      </div>
    </header>
  );
}