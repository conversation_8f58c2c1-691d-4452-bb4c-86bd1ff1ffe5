/* KYA Dashboard Improvements - Ergonomie et Esthétique */

/* Gradients KYA personnalisés */
.gradient-kya-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 50%, #1e40af 100%);
}

.gradient-kya-secondary {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 50%, #b45309 100%);
}

.gradient-kya-success {
  background: linear-gradient(135deg, #10b981 0%, #059669 50%, #047857 100%);
}

.gradient-kya-warning {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 50%, #b45309 100%);
}

.gradient-kya-danger {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 50%, #b91c1c 100%);
}

/* Animations personnalisées */
@keyframes kya-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes kya-bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

@keyframes kya-slide-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes kya-scale-in {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Classes utilitaires pour les animations */
.animate-kya-pulse {
  animation: kya-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-kya-bounce {
  animation: kya-bounce 1s infinite;
}

.animate-kya-slide-in {
  animation: kya-slide-in 0.3s ease-out;
}

.animate-kya-scale-in {
  animation: kya-scale-in 0.2s ease-out;
}

/* Améliorations des composants */
.kya-card-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.kya-card-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Filtres compacts */
.filters-compact {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(248, 250, 252, 0.6) 100%);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(226, 232, 240, 0.5);
}

/* Tabs modernes */
.kya-tabs-modern .tabs-trigger {
  position: relative;
  overflow: hidden;
}

.kya-tabs-modern .tabs-trigger::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.kya-tabs-modern .tabs-trigger:hover::before {
  left: 100%;
}

/* Optimisation de l'espace */
.space-optimized {
  gap: 0.75rem;
}

.space-optimized > * + * {
  margin-top: 0.75rem;
}

/* Header compact */
.kya-header-compact {
  padding: 0.75rem 0;
  border-bottom: 1px solid rgba(226, 232, 240, 0.5);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.7) 100%);
  backdrop-filter: blur(8px);
}

/* KPI Cards améliorées */
.kya-kpi-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.8) 100%);
  border: 1px solid rgba(226, 232, 240, 0.5);
  backdrop-filter: blur(8px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.kya-kpi-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 8px 25px -5px rgba(59, 130, 246, 0.15);
  border-color: rgba(59, 130, 246, 0.3);
}

/* Responsive improvements */
@media (max-width: 768px) {
  .kya-header-compact {
    padding: 0.5rem 0;
  }
  
  .space-optimized {
    gap: 0.5rem;
  }
  
  .space-optimized > * + * {
    margin-top: 0.5rem;
  }
}

/* Scrollbar personnalisée */
.kya-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.kya-scrollbar::-webkit-scrollbar-track {
  background: rgba(248, 250, 252, 0.5);
  border-radius: 3px;
}

.kya-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border-radius: 3px;
}

.kya-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #1d4ed8, #1e40af);
}

/* Focus states améliorés */
.kya-focus:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
  border-radius: 0.375rem;
}

/* Loading states */
.kya-loading {
  background: linear-gradient(90deg, #f1f5f9 25%, #e2e8f0 50%, #f1f5f9 75%);
  background-size: 200% 100%;
  animation: kya-loading 1.5s infinite;
}

@keyframes kya-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
