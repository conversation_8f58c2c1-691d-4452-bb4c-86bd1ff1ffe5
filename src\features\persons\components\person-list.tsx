'use client';

import { useState } from 'react';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  Search, 
  Plus, 
  MoreHorizontal, 
  Eye, 
  Edit, 
  Trash2,
  Filter,
  SortAsc,
  SortDesc,
  Users,
  UserCheck,
  UserX,
  Mail
} from 'lucide-react';
import { usePersons } from '../hooks/use-persons';
import { usePersonMutations } from '../hooks/use-person-mutations';
import { PersonForm } from './person-form';
import { PersonCard } from './person-card';
import type { PersonWithEntities, PersonFilters, PersonSortOptions } from '../types';

interface PersonListProps {
  onPersonSelect?: (person: PersonWithEntities) => void;
  selectedPersonId?: string;
}

export function PersonList({ onPersonSelect, selectedPersonId }: PersonListProps) {
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [selectedPerson, setSelectedPerson] = useState<PersonWithEntities | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [showFilters, setShowFilters] = useState(false);

  const {
    persons,
    total,
    page,
    limit,
    isLoading,
    error,
    filters,
    sort,
    stats,
    isLoadingStats,
    setFilters,
    setSort,
    setPage,
    setLimit,
    refresh
  } = usePersons();

  const { deletePerson, isDeleting } = usePersonMutations();

  // Handle search
  const handleSearch = (value: string) => {
    setSearchTerm(value);
    setFilters({ ...filters, search: value || undefined });
  };

  // Handle sort
  const handleSort = (field: PersonSortOptions['field']) => {
    const newDirection = sort.field === field && sort.direction === 'asc' ? 'desc' : 'asc';
    setSort({ field, direction: newDirection });
  };

  // Handle filter changes
  const handleFilterChange = (key: keyof PersonFilters, value: any) => {
    setFilters({ ...filters, [key]: value });
  };

  // Handle actions
  const handleView = (person: PersonWithEntities) => {
    setSelectedPerson(person);
    setShowDetailsModal(true);
    onPersonSelect?.(person);
  };

  const handleEdit = (person: PersonWithEntities) => {
    setSelectedPerson(person);
    setShowEditModal(true);
  };

  const handleDelete = async (person: PersonWithEntities) => {
    if (confirm(`Êtes-vous sûr de vouloir supprimer ${person.fullName} ?`)) {
      const result = await deletePerson(person.id);
      if (result.success) {
        refresh();
      } else {
        alert(result.error || 'Erreur lors de la suppression');
      }
    }
  };

  const handleCreate = () => {
    setSelectedPerson(null);
    setShowCreateModal(true);
  };

  const handleModalClose = () => {
    setShowCreateModal(false);
    setShowEditModal(false);
    setShowDetailsModal(false);
    setSelectedPerson(null);
    refresh();
  };

  // Stats cards
  const statsCards = [
    {
      title: 'Total',
      value: stats?.total || 0,
      icon: Users,
      color: 'text-blue-600'
    },
    {
      title: 'Actifs',
      value: stats?.active || 0,
      icon: UserCheck,
      color: 'text-green-600'
    },
    {
      title: 'Inactifs',
      value: stats?.inactive || 0,
      icon: UserX,
      color: 'text-red-600'
    },
    {
      title: 'Sans email',
      value: stats?.withoutEmail || 0,
      icon: Mail,
      color: 'text-orange-600'
    }
  ];

  if (error) {
    return (
      <div className="p-4 text-center">
        <p className="text-red-600">{error}</p>
        <Button onClick={refresh} className="mt-2">
          Réessayer
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {statsCards.map((stat) => (
          <Card key={stat.title}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
              <stat.icon className={`h-4 w-4 ${stat.color}`} />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {isLoadingStats ? <Skeleton className="h-8 w-16" /> : stat.value}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Header with search and actions */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex-1 flex gap-2">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Rechercher par nom, email, matricule..."
              value={searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
              className="pl-10"
            />
          </div>
          <Button
            variant="outline"
            onClick={() => setShowFilters(!showFilters)}
            className="shrink-0"
          >
            <Filter className="h-4 w-4 mr-2" />
            Filtres
          </Button>
        </div>
        <Button onClick={handleCreate}>
          <Plus className="h-4 w-4 mr-2" />
          Nouvelle personne
        </Button>
      </div>

      {/* Filters */}
      {showFilters && (
        <Card>
          <CardContent className="pt-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="text-sm font-medium mb-2 block">Statut</label>
                <Select
                  value={filters.isActive === undefined ? 'all' : filters.isActive ? 'active' : 'inactive'}
                  onValueChange={(value) => 
                    handleFilterChange('isActive', value === 'all' ? undefined : value === 'active')
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tous</SelectItem>
                    <SelectItem value="active">Actifs</SelectItem>
                    <SelectItem value="inactive">Inactifs</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <label className="text-sm font-medium mb-2 block">Email</label>
                <Select
                  value={filters.hasEmail === undefined ? 'all' : filters.hasEmail ? 'with' : 'without'}
                  onValueChange={(value) => 
                    handleFilterChange('hasEmail', value === 'all' ? undefined : value === 'with')
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tous</SelectItem>
                    <SelectItem value="with">Avec email</SelectItem>
                    <SelectItem value="without">Sans email</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <label className="text-sm font-medium mb-2 block">Poste</label>
                <Input
                  placeholder="Filtrer par poste..."
                  value={filters.position || ''}
                  onChange={(e) => handleFilterChange('position', e.target.value || undefined)}
                />
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Table */}
      <Card>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>
                <Button
                  variant="ghost"
                  onClick={() => handleSort('lastName')}
                  className="h-auto p-0 font-semibold"
                >
                  Nom
                  {sort.field === 'lastName' && (
                    sort.direction === 'asc' ? <SortAsc className="ml-2 h-4 w-4" /> : <SortDesc className="ml-2 h-4 w-4" />
                  )}
                </Button>
              </TableHead>
              <TableHead>
                <Button
                  variant="ghost"
                  onClick={() => handleSort('email')}
                  className="h-auto p-0 font-semibold"
                >
                  Email
                  {sort.field === 'email' && (
                    sort.direction === 'asc' ? <SortAsc className="ml-2 h-4 w-4" /> : <SortDesc className="ml-2 h-4 w-4" />
                  )}
                </Button>
              </TableHead>
              <TableHead>
                <Button
                  variant="ghost"
                  onClick={() => handleSort('employeeId')}
                  className="h-auto p-0 font-semibold"
                >
                  Matricule
                  {sort.field === 'employeeId' && (
                    sort.direction === 'asc' ? <SortAsc className="ml-2 h-4 w-4" /> : <SortDesc className="ml-2 h-4 w-4" />
                  )}
                </Button>
              </TableHead>
              <TableHead>
                <Button
                  variant="ghost"
                  onClick={() => handleSort('position')}
                  className="h-auto p-0 font-semibold"
                >
                  Poste
                  {sort.field === 'position' && (
                    sort.direction === 'asc' ? <SortAsc className="ml-2 h-4 w-4" /> : <SortDesc className="ml-2 h-4 w-4" />
                  )}
                </Button>
              </TableHead>
              <TableHead>Entités</TableHead>
              <TableHead>Statut</TableHead>
              <TableHead className="w-[100px]">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              Array.from({ length: limit }).map((_, index) => (
                <TableRow key={index}>
                  <TableCell><Skeleton className="h-4 w-32" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-48" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-24" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                </TableRow>
              ))
            ) : persons.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                  Aucune personne trouvée
                </TableCell>
              </TableRow>
            ) : (
              persons.map((person) => (
                <TableRow 
                  key={person.id}
                  className={selectedPersonId === person.id ? 'bg-muted' : ''}
                >
                  <TableCell className="font-medium">
                    {person.fullName}
                  </TableCell>
                  <TableCell>{person.email || '-'}</TableCell>
                  <TableCell>{person.employeeId || '-'}</TableCell>
                  <TableCell>{person.position || '-'}</TableCell>
                  <TableCell>
                    <div className="flex flex-wrap gap-1">
                      {person.entities?.slice(0, 2).map((assignment) => (
                        <Badge key={assignment.id} variant="secondary" className="text-xs">
                          {assignment.entity?.name}
                          {assignment.isPrimary && ' (Principal)'}
                        </Badge>
                      ))}
                      {person.entities && person.entities.length > 2 && (
                        <Badge variant="outline" className="text-xs">
                          +{person.entities.length - 2}
                        </Badge>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant={person.isActive ? 'default' : 'secondary'}>
                      {person.isActive ? 'Actif' : 'Inactif'}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleView(person)}>
                          <Eye className="mr-2 h-4 w-4" />
                          Voir
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleEdit(person)}>
                          <Edit className="mr-2 h-4 w-4" />
                          Modifier
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          onClick={() => handleDelete(person)}
                          className="text-red-600"
                          disabled={isDeleting}
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          Supprimer
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </Card>

      {/* Pagination */}
      {total > limit && (
        <div className="flex items-center justify-between">
          <p className="text-sm text-muted-foreground">
            Affichage de {((page - 1) * limit) + 1} à {Math.min(page * limit, total)} sur {total} personnes
          </p>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(page - 1)}
              disabled={page === 1}
            >
              Précédent
            </Button>
            <span className="text-sm">
              Page {page} sur {Math.ceil(total / limit)}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(page + 1)}
              disabled={page >= Math.ceil(total / limit)}
            >
              Suivant
            </Button>
          </div>
        </div>
      )}

      {/* Modals */}
      {showCreateModal && (
        <PersonForm
          onClose={handleModalClose}
          onSuccess={handleModalClose}
        />
      )}

      {showEditModal && selectedPerson && (
        <PersonForm
          person={selectedPerson}
          onClose={handleModalClose}
          onSuccess={handleModalClose}
        />
      )}

      {showDetailsModal && selectedPerson && (
        <PersonCard
          person={selectedPerson}
          onClose={() => setShowDetailsModal(false)}
          onEdit={() => {
            setShowDetailsModal(false);
            handleEdit(selectedPerson);
          }}
        />
      )}
    </div>
  );
}
