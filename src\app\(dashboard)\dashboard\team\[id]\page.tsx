// Dynamic Team Page - KYA Dashboards
import { Suspense } from 'react';
import { notFound } from 'next/navigation';
import { getEntityWithMappingAction } from '@/features/entities/actions';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import type { Metadata } from 'next';

// Team dashboard component
function TeamDashboard({ entity }: { entity: any }) {
  const hasFeatureMapping = entity.featureMapping;
  const featurePath = hasFeatureMapping
    ? `${entity.featureMapping.featurePath}/${entity.id}`
    : `/dashboard/team/${entity.id}`;

  return (
    <div className="space-y-6">
      {/* Entity Information Card */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h2 className="text-xl font-semibold">{entity.name}</h2>
              <p className="text-muted-foreground">
                {entity.type} • Code: {entity.code}
              </p>
            </div>
            <Badge variant="outline">
              {entity.type}
            </Badge>
          </div>
          
          {entity.description && (
            <p className="text-sm text-muted-foreground mb-4">
              {entity.description}
            </p>
          )}

          {hasFeatureMapping && (
            <div className="mt-4 p-4 bg-blue-50 rounded-lg">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium text-blue-900">
                    Module disponible: {entity.featureMapping.displayName}
                  </h3>
                  <p className="text-sm text-blue-700">
                    Cette entité a accès au module {entity.featureMapping.businessModule}
                  </p>
                </div>
                <Link href={featurePath}>
                  <Button>
                    Accéder au module
                  </Button>
                </Link>
              </div>
            </div>
          )}

          {!hasFeatureMapping && (
            <div className="mt-4 p-4 bg-gray-50 rounded-lg">
              <p className="text-sm text-gray-600">
                Cette entité n'a pas de module spécifique configuré.
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Team Members Card (placeholder) */}
      <Card>
        <CardContent className="pt-6">
          <h3 className="text-lg font-semibold mb-4">Membres de l'équipe</h3>
          <div className="text-center py-8 text-muted-foreground">
            <p>🚧 La liste des membres sera affichée ici</p>
          </div>
        </CardContent>
      </Card>

      {/* Team Statistics Card (placeholder) */}
      <Card>
        <CardContent className="pt-6">
          <h3 className="text-lg font-semibold mb-4">Statistiques</h3>
          <div className="text-center py-8 text-muted-foreground">
            <p>📊 Les statistiques de l'équipe seront affichées ici</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

// Loading component
function DashboardLoading() {
  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <div className="h-8 bg-gray-200 rounded w-1/3 animate-pulse"></div>
        <div className="h-4 bg-gray-200 rounded w-1/2 animate-pulse"></div>
      </div>
      {[...Array(3)].map((_, i) => (
        <Card key={i}>
          <CardContent className="pt-6">
            <div className="animate-pulse space-y-4">
              <div className="h-6 bg-gray-200 rounded w-1/4"></div>
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              <div className="h-32 bg-gray-200 rounded w-full"></div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

interface PageProps {
  params: {
    id: string;
  };
}

export default async function TeamPage({ params }: PageProps) {
  const { id } = params;

  const result = await getEntityWithMappingAction(id);

  if (!result.data) {
    notFound();
  }

  const entityWithMapping = result.data;

  return (
    <div className="container mx-auto py-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">
          {entityWithMapping.name}
        </h1>
        <p className="text-muted-foreground">
          Dashboard pour {entityWithMapping.type.toLowerCase()} {entityWithMapping.name}
        </p>
      </div>
      
      <Suspense fallback={<DashboardLoading />}>
        <TeamDashboard entity={entityWithMapping} />
      </Suspense>
    </div>
  );
}

export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const { id } = params;
  
  try {
    const result = await getEntityWithMappingAction(id);

    if (!result.data) {
      return {
        title: 'Entité non trouvée | KYA Dashboards',
        description: 'L\'entité demandée n\'existe pas.',
      };
    }

    return {
      title: `${result.data.name} | KYA Dashboards`,
      description: `Dashboard pour ${result.data.type.toLowerCase()} ${result.data.name}`,
    };
  } catch (error) {
    return {
      title: 'Erreur | KYA Dashboards',
      description: 'Une erreur est survenue lors du chargement de la page.',
    };
  }
}
