import { create } from 'zustand';
import { RBACService } from '../services/rbac-service';

type PermissionStore = {
  permissions: string[];
  isLoading: boolean;
  fetchPermissions: (userId: string) => Promise<void>;
  hasPermission: (permission: string) => boolean;
  clearPermissions: () => void;
};

export const usePermissionStore = create<PermissionStore>((set, get) => ({
  permissions: [],
  isLoading: false,
  fetchPermissions: async (userId: string) => {
    set({ isLoading: true });
    try {
      const permissions = await RBACService.getUserPermissions(userId);
      set({ permissions, isLoading: false });
    } catch (error) {
      console.error('Failed to fetch permissions', error);
      set({ permissions: [], isLoading: false });
    }
  },
  hasPermission: (permission: string) => {
    const { permissions } = get();
    return permissions.includes(permission);
  },
  clearPermissions: () => set({ permissions: [] }),
}));