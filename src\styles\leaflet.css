/* Leaflet CSS overrides for KYA Dashboard */
@import 'leaflet/dist/leaflet.css';

/* Custom marker styles */
.custom-marker {
  background: transparent !important;
  border: none !important;
}

/* Cluster styles */
.marker-cluster-small {
  background-color: rgba(28, 161, 140, 0.6);
}

.marker-cluster-small div {
  background-color: rgba(28, 161, 140, 0.8);
}

.marker-cluster-medium {
  background-color: rgba(249, 157, 50, 0.6);
}

.marker-cluster-medium div {
  background-color: rgba(249, 157, 50, 0.8);
}

.marker-cluster-large {
  background-color: rgba(232, 231, 72, 0.6);
}

.marker-cluster-large div {
  background-color: rgba(232, 231, 72, 0.8);
}

/* Base cluster styles */
.marker-cluster {
  background-clip: padding-box;
  border-radius: 20px;
}

.marker-cluster div {
  width: 30px;
  height: 30px;
  margin-left: 5px;
  margin-top: 5px;
  text-align: center;
  border-radius: 15px;
  font: 12px "Helvetica Neue", Arial, Helvetica, sans-serif;
  color: white;
  font-weight: bold;
  line-height: 30px;
}

.marker-cluster span {
  line-height: 30px;
}

/* Popup styles */
.leaflet-popup-content-wrapper {
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.leaflet-popup-tip {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Control styles */
.leaflet-control-zoom {
  border: none !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
}

.leaflet-control-zoom a {
  border-radius: 6px !important;
  border: 1px solid #e5e7eb !important;
  background-color: white !important;
  color: #374151 !important;
}

.leaflet-control-zoom a:hover {
  background-color: #f9fafb !important;
  border-color: #d1d5db !important;
}
