'use client';

import { useEffect, useState } from 'react';
import { getNavigationHierarchyAction } from '@/features/entities/actions';
import type { EntityNavigationItem } from '@/types/entities';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger
} from "@/components/ui/accordion"
import Link from 'next/link';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import * as Icons from 'lucide-react';

export function SecondarySidebar() {
  const [navigationItems, setNavigationItems] = useState<EntityNavigationItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    async function loadNavigationHierarchy() {
      setIsLoading(true);
      try {
        // Get navigation hierarchy with feature mappings
        const result = await getNavigationHierarchyAction();
        if (result.data) {
          setNavigationItems(result.data);
        }
      } catch (error) {
        console.error('Error loading navigation hierarchy:', error);
      }
      setIsLoading(false);
    }
    loadNavigationHierarchy();
  }, []);

  // Helper function to get icon component
  const getIconComponent = (iconName?: string) => {
    if (!iconName) return null;
    const IconComponent = (Icons as any)[iconName];
    return IconComponent ? <IconComponent className="h-4 w-4" /> : null;
  };

  // Helper function to render navigation item
  const renderNavigationItem = (item: EntityNavigationItem) => {
    const hasFeatureMapping = item.businessModule && item.featurePath;
    const navigationPath = hasFeatureMapping
      ? `${item.featurePath}/${item.id}`
      : `/dashboard/team/${item.id}`;

    return (
      <Link
        href={navigationPath}
        className="flex items-center justify-between p-2 text-sm rounded hover:bg-gray-100 text-muted-foreground group"
      >
        <div className="flex items-center gap-2">
          {getIconComponent(item.icon)}
          <span>{item.name}</span>
        </div>
        {hasFeatureMapping && (
          <Badge variant="secondary" className="text-xs opacity-0 group-hover:opacity-100 transition-opacity">
            {item.displayName}
          </Badge>
        )}
      </Link>
    );
  };

  return (
    <div className="w-56 border-r bg-white">
        <div className="p-4">
            <h3 className="font-semibold text-lg">Entités</h3>
        </div>
        <div className="px-2">
            {isLoading ? (
              <div className="p-2 space-y-2">
                <Skeleton className="h-8 w-full" />
                <Skeleton className="h-8 w-full" />
                <Skeleton className="h-8 w-full" />
              </div>
            ) : (
              <Accordion type="multiple" className="w-full">
                {navigationItems.map(direction => (
                  <AccordionItem value={direction.id} key={direction.id}>
                    <AccordionTrigger className="text-sm font-medium hover:bg-gray-100 px-2 rounded-md">
                      <div className="flex items-center gap-2">
                        {getIconComponent(direction.icon)}
                        <span>{direction.name}</span>
                        {direction.businessModule && (
                          <Badge variant="outline" className="text-xs ml-auto">
                            {direction.displayName}
                          </Badge>
                        )}
                      </div>
                    </AccordionTrigger>
                    <AccordionContent>
                      <ul className="pl-4 mt-1 space-y-1">
                        {direction.children.map((team: EntityNavigationItem) => (
                          <li key={team.id}>
                            {renderNavigationItem(team)}
                          </li>
                        ))}
                      </ul>
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
            )}
        </div>
    </div>
  );
}