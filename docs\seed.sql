-- KYA Dashboards - Initial Data Seed
-- This script initializes the database with essential RBAC data.
-- It's designed to be idempotent, so it can be run safely multiple times.

-- 1. System Roles
-------------------------------------------------------------------------------
-- These are the core roles for the application.
-- level: Higher numbers have more privilege.
-- is_system_role: Prevents deletion from the UI.
INSERT INTO roles (name, display_name, description, level, is_system_role) VALUES
('SUPER_ADMIN', 'Super Administrateur', 'Accès complet et illimité à tout le système.', 100, true),
('DIRECTOR', 'Directeur', 'Accès à tous les dashboards et aux paramètres globaux.', 90, true),
('MANAGER', 'Manager', 'Gestion des entités (équipes/directions) et de leurs membres.', 50, true),
('MEMBER', 'Membre', 'Accès à la saisie de données et à la consultation de son équipe.', 10, true)
ON CONFLICT (name) DO NOTHING;

-- 2. System Permissions
-------------------------------------------------------------------------------
-- Permissions are defined based on `resource.action`.
-- This provides a granular way to control access.

-- Admin Access
INSERT INTO permissions (name, display_name, resource, action, is_system_permission) VALUES
('admin.access', 'Accéder à l''administration', 'admin', 'access', true)
ON CONFLICT (name) DO NOTHING;

-- User Management
INSERT INTO permissions (name, display_name, resource, action, is_system_permission) VALUES
('users.create', 'Créer des utilisateurs', 'users', 'create', true),
('users.read', 'Consulter les utilisateurs', 'users', 'read', true),
('users.update', 'Modifier les utilisateurs', 'users', 'update', true),
('users.delete', 'Supprimer les utilisateurs', 'users', 'delete', true),
('admin.users.read', 'Consulter les utilisateurs (Admin)', 'admin_users', 'read', true),
('admin.users.manage', 'Gérer les utilisateurs (Admin)', 'admin_users', 'manage', true)
ON CONFLICT (name) DO NOTHING;

-- RBAC Management
INSERT INTO permissions (name, display_name, resource, action, is_system_permission) VALUES
('roles.manage', 'Gérer les rôles et permissions', 'roles', 'manage', true),
('admin.rbac.manage', 'Gérer les rôles et permissions (Admin)', 'admin_rbac', 'manage', true)
ON CONFLICT (name) DO NOTHING;

-- Entity Management
INSERT INTO permissions (name, display_name, resource, action, is_system_permission) VALUES
('entities.create', 'Créer des entités', 'entities', 'create', true),
('entities.read', 'Consulter les entités', 'entities', 'read', true),
('entities.update', 'Modifier les entités', 'entities', 'update', true),
('entities.delete', 'Supprimer les entités', 'entities', 'delete', true),
('admin.entities.manage', 'Gérer les entités (Admin)', 'admin_entities', 'manage', true)
ON CONFLICT (name) DO NOTHING;

-- Dashboard Access
INSERT INTO permissions (name, display_name, resource, action, is_system_permission) VALUES
('dashboard.view.all', 'Voir tous les dashboards', 'dashboard', 'view_all', true),
('dashboard.view.team', 'Voir le dashboard de son équipe', 'dashboard', 'view_team', true)
ON CONFLICT (name) DO NOTHING;

-- Data Entry
INSERT INTO permissions (name, display_name, resource, action, is_system_permission) VALUES
('data.entry', 'Saisir les données', 'data_entry', 'create', true),
('data.validate', 'Valider les données', 'data_entry', 'validate', true)
ON CONFLICT (name) DO NOTHING;


-- 3. Role-Permission Assignments
-------------------------------------------------------------------------------
-- Connects roles to their allowed permissions.

-- SUPER_ADMIN: Has all permissions
INSERT INTO roles_permissions (role_id, permission_id)
SELECT r.id, p.id FROM roles r, permissions p WHERE r.name = 'SUPER_ADMIN'
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- DIRECTOR: Admin access, can manage users/entities, and view all dashboards
INSERT INTO roles_permissions (role_id, permission_id)
SELECT
    (SELECT id FROM roles WHERE name = 'DIRECTOR'),
    p.id
FROM permissions p
WHERE p.name IN (
    'admin.access',
    'users.create', 'users.read', 'users.update',
    'admin.users.read', 'admin.users.manage',
    'entities.read', 'admin.entities.manage',
    'admin.rbac.manage',
    'dashboard.view.all'
)
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- MANAGER: Can manage their assigned entities and validate data
INSERT INTO roles_permissions (role_id, permission_id)
SELECT
    (SELECT id FROM roles WHERE name = 'MANAGER'),
    p.id
FROM permissions p
WHERE p.name IN (
    'entities.read', 'entities.update', -- For their own entities
    'users.read', -- To see team members
    'dashboard.view.team',
    'data.entry', 'data.validate'
)
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- MEMBER: Can only perform data entry and view their team's dashboard
INSERT INTO roles_permissions (role_id, permission_id)
SELECT
    (SELECT id FROM roles WHERE name = 'MEMBER'),
    p.id
FROM permissions p
WHERE p.name IN (
    'dashboard.view.team',
    'data.entry'
)
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- Final check
SELECT r.name as role_name, p.name as permission_name
FROM roles_permissions rp
JOIN roles r ON rp.role_id = r.id
JOIN permissions p ON rp.permission_id = p.id
ORDER BY r.level DESC, r.name, p.name;
