// Organizational Entities Types
export type EntityType = 'DIRECTION' | 'EQUIPE' | 'SOUS_EQUIPE' | 'DEPARTEMENT';

export interface Entity {
  id: string;
  name: string;
  type: EntityType;
  parentId?: string;
  managerId?: string;
  description?: string;
  code: string;
  isActive: boolean;
  metadata: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
  
  // Relations (populated when needed)
  parent?: Entity;
  manager?: Person;
  children: Entity[];
  members?: EntityPerson[];
}

export interface Person {
  id: string;
  firstName: string;
  lastName: string;
  email?: string;
  phone?: string;
  employeeId?: string;
  position?: string;
  hireDate?: Date;
  isActive: boolean;
  metadata: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
  
  // Computed properties
  fullName: string;
  displayName: string;
  
  // Relations (populated when needed)
  entities?: EntityPerson[];
  authUser?: import('./auth').AuthUser;
}

export interface EntityPerson {
  id: string;
  entityId: string;
  personId: string;
  roleInEntity?: string;
  startDate: Date;
  endDate?: Date;
  isPrimary: boolean;
  createdAt: Date;
  updatedAt: Date;
  
  // Relations (populated when needed)
  entity?: Entity;
  person?: Person;
}

// Hierarchy and Navigation Types
export interface EntityHierarchy {
  entity: Entity;
  level: number;
  path: string[];
  children: EntityHierarchy[];
  hasChildren: boolean;
  isExpanded?: boolean;
}

export interface EntityTreeNode {
  id: string;
  name: string;
  type: EntityType;
  code: string;
  level: number;
  parentId?: string;
  managerId?: string;
  isActive: boolean;
  children: EntityTreeNode[];
  hasChildren: boolean;
  memberCount: number;
}

// Form and Input Types
export interface CreateEntityData {
  name: string;
  type: EntityType;
  parentId?: string;
  managerId?: string;
  description?: string;
  code: string;
  metadata?: Record<string, any>;
}

export interface UpdateEntityData {
  name?: string;
  parentId?: string;
  managerId?: string;
  description?: string;
  code?: string;
  isActive?: boolean;
  metadata?: Record<string, any>;
}

export interface CreatePersonData {
  firstName: string;
  lastName: string;
  email?: string;
  phone?: string;
  employeeId?: string;
  position?: string;
  hireDate?: Date;
  metadata?: Record<string, any>;
}

export interface UpdatePersonData {
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  employeeId?: string;
  position?: string;
  hireDate?: Date;
  isActive?: boolean;
  metadata?: Record<string, any>;
}

export interface AssignPersonToEntityData {
  entityId: string;
  personId: string;
  roleInEntity?: string;
  startDate?: Date;
  endDate?: Date;
  isPrimary?: boolean;
}

// Query and Filter Types
export interface EntityFilters {
  type?: EntityType[];
  parentId?: string;
  managerId?: string;
  isActive?: boolean;
  searchTerm?: string;
  hasMembers?: boolean;
}

export interface PersonFilters {
  entityId?: string;
  isActive?: boolean;
  searchTerm?: string;
  position?: string;
  hireDate?: {
    from?: Date;
    to?: Date;
  };
}

// Statistics and Analytics Types
export interface EntityStats {
  totalEntities: number;
  entitiesByType: Record<EntityType, number>;
  totalPersons: number;
  activePersons: number;
  entitiesWithoutManager: number;
  averageMembersPerEntity: number;
}

// Entity-Feature Mapping Types
export type BusinessModule = 'installations' | 'maintenance' | 'commercial' | 'projects' | 'support';

export interface EntityFeatureMapping {
  entityCode: string;
  businessModule: BusinessModule;
  featurePath: string;
  displayName: string;
  icon?: string;
  permissions?: string[];
  metadata?: Record<string, any>;
}

export interface EntityNavigationItem {
  id: string;
  name: string;
  code: string;
  type: EntityType;
  businessModule?: BusinessModule;
  featurePath?: string;
  displayName?: string;
  icon?: string;
  children: EntityNavigationItem[];
}

export interface EntityMembershipStats {
  entityId: string;
  entityName: string;
  totalMembers: number;
  activeMembers: number;
  membersByRole: Record<string, number>;
  averageTenure: number;
}
