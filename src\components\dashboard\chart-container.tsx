import { ReactNode, useState } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { 
  MoreHorizontal, 
  Download, 
  Maximize2, 
  RefreshCw,
  TrendingUp,
  TrendingDown,
  Minus
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

export interface ChartData {
  labels: string[];
  datasets: Array<{
    label: string;
    data: number[];
    backgroundColor?: string | string[];
    borderColor?: string | string[];
    borderWidth?: number;
  }>;
}

export interface ChartMetrics {
  total?: number;
  change?: {
    value: number;
    percentage: number;
    isPositive: boolean;
    period: string;
  };
  peak?: {
    value: number;
    label: string;
  };
  average?: number;
}

interface ChartContainerProps {
  title: string;
  subtitle?: string;
  children: ReactNode;
  data?: ChartData;
  metrics?: ChartMetrics;
  isLoading?: boolean;
  error?: string;
  className?: string;
  height?: string;
  showActions?: boolean;
  showMetrics?: boolean;
  onRefresh?: () => void;
  onExport?: () => void;
  onFullscreen?: () => void;
}

export function ChartContainer({
  title,
  subtitle,
  children,
  data,
  metrics,
  isLoading = false,
  error,
  className,
  height = '300px',
  showActions = true,
  showMetrics = true,
  onRefresh,
  onExport,
  onFullscreen,
}: ChartContainerProps) {
  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleRefresh = async () => {
    if (!onRefresh) return;
    
    setIsRefreshing(true);
    try {
      await onRefresh();
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleExport = () => {
    if (onExport) {
      onExport();
    } else if (data) {
      // Export par défaut en CSV
      const csvContent = [
        data.labels.join(','),
        ...data.datasets.map(dataset => 
          `${dataset.label},${dataset.data.join(',')}`
        )
      ].join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${title.toLowerCase().replace(/\s+/g, '-')}.csv`;
      a.click();
      window.URL.revokeObjectURL(url);
    }
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <div className="h-6 bg-gray-200 rounded animate-pulse w-48"></div>
              {subtitle && <div className="h-4 bg-gray-200 rounded animate-pulse w-32"></div>}
            </div>
            {showActions && (
              <div className="h-8 w-8 bg-gray-200 rounded animate-pulse"></div>
            )}
          </div>
        </CardHeader>
        <CardContent>
          <div 
            className="bg-gray-200 rounded animate-pulse"
            style={{ height }}
          ></div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={cn('border-red-200', className)}>
        <CardHeader>
          <CardTitle className="text-red-600">{title}</CardTitle>
          {subtitle && <p className="text-sm text-red-500">{subtitle}</p>}
        </CardHeader>
        <CardContent>
          <div 
            className="flex items-center justify-center bg-red-50 rounded-lg text-red-600"
            style={{ height }}
          >
            <div className="text-center">
              <p className="font-medium">Erreur de chargement</p>
              <p className="text-sm mt-1">{error}</p>
              {onRefresh && (
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="mt-3"
                  onClick={handleRefresh}
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Réessayer
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="space-y-1">
            <CardTitle className="text-lg font-bold text-foreground">{title}</CardTitle>
            {subtitle && (
              <p className="text-xs text-muted-foreground">{subtitle}</p>
            )}
          </div>
          
          {showActions && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="h-8 w-8 text-muted-foreground">
                  <MoreHorizontal className="h-5 w-5" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                {onRefresh && (
                  <DropdownMenuItem onClick={handleRefresh} disabled={isRefreshing}>
                    <RefreshCw className={cn(
                      'h-4 w-4 mr-2',
                      isRefreshing && 'animate-spin'
                    )} />
                    <span>Actualiser</span>
                  </DropdownMenuItem>
                )}
                <DropdownMenuItem onClick={handleExport}>
                  <Download className="h-4 w-4 mr-2" />
                  <span>Exporter</span>
                </DropdownMenuItem>
                {onFullscreen && (
                  <>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={onFullscreen}>
                      <Maximize2 className="h-4 w-4 mr-2" />
                      <span>Plein écran</span>
                    </DropdownMenuItem>
                  </>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>
      </CardHeader>
      
      <CardContent>
        <div style={{ height }} className="relative">
          {children}
        </div>
      </CardContent>
    </Card>
  );
}

// Composant pour afficher un graphique vide avec message
interface EmptyChartProps {
  message?: string;
  height?: string;
  className?: string;
}

export function EmptyChart({ 
  message = 'Aucune donnée disponible', 
  height = '300px',
  className 
}: EmptyChartProps) {
  return (
    <div 
      className={cn(
        'flex items-center justify-center bg-gray-50 rounded-lg text-gray-500',
        className
      )}
      style={{ height }}
    >
      <div className="text-center">
        <div className="text-4xl mb-2">📊</div>
        <p className="font-medium">{message}</p>
        <p className="text-sm mt-1">Les données apparaîtront ici une fois disponibles</p>
      </div>
    </div>
  );
}

// Hook pour gérer les données de graphique
export function useChartData<T>(
  data: T[],
  config: {
    labelKey: keyof T;
    valueKey: keyof T;
    groupBy?: keyof T;
  }
) {
  const chartData: ChartData = {
    labels: [],
    datasets: []
  };

  if (config.groupBy) {
    // Grouper les données
    const grouped = data.reduce((acc, item) => {
      const group = String(item[config.groupBy!]);
      if (!acc[group]) acc[group] = [];
      acc[group].push(item);
      return acc;
    }, {} as Record<string, T[]>);

    chartData.labels = Object.keys(grouped);
    chartData.datasets = [{
      label: 'Données',
      data: Object.values(grouped).map(group => 
        group.reduce((sum, item) => sum + Number(item[config.valueKey]), 0)
      ),
      backgroundColor: '#1ca18c',
      borderColor: '#1ca18c',
    }];
  } else {
    // Données simples
    chartData.labels = data.map(item => String(item[config.labelKey]));
    chartData.datasets = [{
      label: 'Données',
      data: data.map(item => Number(item[config.valueKey])),
      backgroundColor: '#1ca18c',
      borderColor: '#1ca18c',
    }];
  }

  return chartData;
}
