import { createClient } from '@/utils/supabase/server';
import { AuditService } from '@/utils/services/audit-service';
import type { 
  AuthUser, 
  UserProfile,
  ProfileUpdateRequest
} from '@/types/auth';

/**
 * AuthService provides methods for handling authentication and user profile logic.
 * This class should be used exclusively on the server-side, primarily within Server Actions.
 * It strictly uses the server-side Supabase client.
 */
export class AuthService {

  /**
   * Retrieves the current user from the server-side context.
   * This is a safe way to get the user in Server Components and Server Actions.
   */
  static async getCurrentUser(): Promise<AuthUser | null> {
    try {
      const supabase = await createClient();
      const { data: { user }, error } = await supabase.auth.getUser();

      if (error || !user) {
        return null;
      }
      
      const { data: authUser, error: dbError } = await supabase
        .from('auth_users')
        .select('*')
        .eq('supabase_id', user.id)
        .single();

      if (dbError) {
        console.warn(`User with supabase_id ${user.id} not found in our db.`, dbError.message);
        return null;
      }

      return authUser;
    } catch (error) {
      console.error('Error in getCurrentUser:', error);
      return null;
    }
  }

  /**
   * Retrieves the user profile from the database.
   * Should be called from a server context.
   */
  static async getUserProfile(userId: string): Promise<UserProfile | null> {
    try {
      const supabase = await createClient();
      const { data: profile, error } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          console.log(`No profile found for user ${userId}, a default could be created.`);
          return null;
        }
        throw error;
      }

      return profile;
    } catch (error) {
      console.error('Error in getUserProfile:', error);
      return null;
    }
  }

  /**
   * Updates a user's profile in the database.
   * Should be called from a server context (e.g., a Server Action).
   */
  static async updateUserProfile(userId: string, profileData: Partial<ProfileUpdateRequest>): Promise<UserProfile> {
    try {
      const supabase = await createClient();
      
      const { displayName, avatarUrl, bio, preferences, notificationSettings, dashboardConfig } = profileData;
      const dataToUpdate = {
        display_name: displayName,
        avatar_url: avatarUrl,
        bio,
        preferences,
        notification_settings: notificationSettings,
        dashboard_config: dashboardConfig,
        updated_at: new Date().toISOString(),
      };

      Object.keys(dataToUpdate).forEach(key => dataToUpdate[key as keyof typeof dataToUpdate] === undefined && delete dataToUpdate[key as keyof typeof dataToUpdate]);

      const { data: profile, error } = await supabase
        .from('user_profiles')
        .update(dataToUpdate)
        .eq('id', userId)
        .select()
        .single();

      if (error) {
        throw new Error(`Profile update failed: ${error.message}`);
      }

      // Correctly call the static AuditService.log method
      await AuditService.log('UPDATE_PROFILE', 'PROFILE', userId, {
        userId: userId,
        newValues: profileData
      });

      return profile;
    } catch (error) {
      console.error('Error in updateUserProfile:', error);
      throw error;
    }
  }
}
