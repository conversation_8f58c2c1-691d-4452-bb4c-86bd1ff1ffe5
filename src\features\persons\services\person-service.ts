import { createClient } from '@/utils/supabase/server';
import { keysToCamel, keysToSnake } from '@/utils/case-converter';
import { AuditService } from '@/utils/services/audit-service';
import type { 
  Person, 
  PersonWithEntities, 
  CreatePersonData, 
  UpdatePersonData,
  PersonFilters,
  PersonSortOptions,
  PersonsResponse,
  PersonStatsResponse,
  CreatePersonEntityData,
  UpdatePersonEntityData,
  PersonEntity
} from '../types';

/**
 * PersonService provides static methods for managing persons and their entity assignments.
 * This class is intended for server-side use only.
 */
export class PersonService {
  /**
   * Retrieves a paginated list of persons with their entity assignments.
   */
  static async getPersons(
    filters: PersonFilters = {},
    sort: PersonSortOptions = { field: 'lastName', direction: 'asc' },
    page: number = 1,
    limit: number = 20
  ): Promise<PersonsResponse> {
    const supabase = await createClient();
    
    let query = supabase
      .from('persons')
      .select(`
        *,
        entities_persons!person_id (
          id,
          entity_id,
          role_in_entity,
          start_date,
          end_date,
          is_primary,
          created_at,
          updated_at,
          entities (
            id,
            name,
            type,
            code
          )
        )
      `, { count: 'exact' });

    // Apply filters
    if (filters.search) {
      query = query.or(`first_name.ilike.%${filters.search}%,last_name.ilike.%${filters.search}%,email.ilike.%${filters.search}%,employee_id.ilike.%${filters.search}%`);
    }
    
    if (filters.isActive !== undefined) {
      query = query.eq('is_active', filters.isActive);
    }
    
    if (filters.position) {
      query = query.ilike('position', `%${filters.position}%`);
    }
    
    if (filters.hasEmail !== undefined) {
      if (filters.hasEmail) {
        query = query.not('email', 'is', null);
      } else {
        query = query.is('email', null);
      }
    }
    
    if (filters.hiredAfter) {
      query = query.gte('hire_date', filters.hiredAfter.toISOString());
    }
    
    if (filters.hiredBefore) {
      query = query.lte('hire_date', filters.hiredBefore.toISOString());
    }

    // Apply sorting - convert camelCase field to snake_case
    const fieldMapping: Record<string, string> = {
      'firstName': 'first_name',
      'lastName': 'last_name',
      'email': 'email',
      'employeeId': 'employee_id',
      'position': 'position',
      'hireDate': 'hire_date',
      'isActive': 'is_active',
      'createdAt': 'created_at',
      'updatedAt': 'updated_at'
    };
    const sortField = fieldMapping[sort.field] || sort.field;
    query = query.order(sortField, { ascending: sort.direction === 'asc' });

    // Apply pagination
    const from = (page - 1) * limit;
    const to = from + limit - 1;
    query = query.range(from, to);

    const { data, error, count } = await query;

    if (error) {
      console.error('Error fetching persons:', error);
      throw new Error('Failed to fetch persons.');
    }

    // Transform data
    const transformedData: PersonWithEntities[] = (data || []).map(person => {
      const camelPerson = keysToCamel(person) as any;
      
      // Transform entities_persons to entities
      const entities = (camelPerson.entitiesPersons || []).map((ep: any) => ({
        ...ep,
        entity: ep.entities
      }));
      
      // Find primary entity
      const primaryEntityAssignment = entities.find((e: any) => e.isPrimary);
      const primaryEntity = primaryEntityAssignment?.entity;
      
      return {
        ...camelPerson,
        fullName: `${camelPerson.firstName} ${camelPerson.lastName}`,
        displayName: camelPerson.firstName + ' ' + camelPerson.lastName,
        entities,
        primaryEntity,
        entitiesPersons: undefined // Remove the raw relation
      };
    });

    return {
      data: transformedData,
      total: count || 0,
      page,
      limit
    };
  }

  /**
   * Retrieves a single person by ID with their entity assignments.
   */
  static async getPersonById(id: string): Promise<PersonWithEntities | null> {
    const supabase = await createClient();
    
    const { data, error } = await supabase
      .from('persons')
      .select(`
        *,
        entities_persons!person_id (
          id,
          entity_id,
          role_in_entity,
          start_date,
          end_date,
          is_primary,
          created_at,
          updated_at,
          entities (
            id,
            name,
            type,
            code
          )
        )
      `)
      .eq('id', id)
      .single();

    if (error) {
      console.error(`Error fetching person ${id}:`, error);
      return null;
    }

    const camelPerson = keysToCamel(data) as any;
    
    // Transform entities_persons to entities
    const entities = (camelPerson.entitiesPersons || []).map((ep: any) => ({
      ...ep,
      entity: ep.entities
    }));
    
    // Find primary entity
    const primaryEntityAssignment = entities.find((e: any) => e.isPrimary);
    const primaryEntity = primaryEntityAssignment?.entity;

    return {
      ...camelPerson,
      fullName: `${camelPerson.firstName} ${camelPerson.lastName}`,
      displayName: camelPerson.firstName + ' ' + camelPerson.lastName,
      entities,
      primaryEntity,
      entitiesPersons: undefined // Remove the raw relation
    };
  }

  /**
   * Creates a new person with optional entity assignments.
   */
  static async createPerson(
    personData: CreatePersonData,
    createdBy: string
  ): Promise<PersonWithEntities> {
    const supabase = await createClient();

    // Separate person data from entities data
    const { entities, ...personOnlyData } = personData;

    // Start transaction
    const { data: person, error: personError } = await supabase
      .from('persons')
      .insert(keysToSnake(personOnlyData))
      .select()
      .single();

    if (personError) {
      throw new Error(`Failed to create person: ${personError.message}`);
    }

    // Create entity assignments if provided
    if (entities && entities.length > 0) {
      const entityAssignments = entities.map(entity => ({
        person_id: person.id,
        entity_id: entity.entityId,
        role_in_entity: entity.roleInEntity,
        start_date: entity.startDate || new Date(),
        is_primary: entity.isPrimary || false
      }));

      const { error: entitiesError } = await supabase
        .from('entities_persons')
        .insert(entityAssignments);

      if (entitiesError) {
        // Rollback: delete the created person
        await supabase.from('persons').delete().eq('id', person.id);
        throw new Error(`Failed to assign entities: ${entitiesError.message}`);
      }
    }

    // Log audit
    await AuditService.log('CREATE', 'PERSON', person.id, {
      userId: createdBy,
      newValues: person,
    });

    // Return the created person with entities
    const createdPerson = await this.getPersonById(person.id);
    if (!createdPerson) {
      throw new Error('Failed to fetch created person.');
    }

    return createdPerson;
  }

  /**
   * Updates an existing person.
   */
  static async updatePerson(
    id: string,
    personData: UpdatePersonData,
    updatedBy: string
  ): Promise<PersonWithEntities> {
    const supabase = await createClient();
    
    // Get current person for audit
    const currentPerson = await this.getPersonById(id);
    if (!currentPerson) {
      throw new Error('Person not found.');
    }

    const { data: updatedPerson, error } = await supabase
      .from('persons')
      .update(keysToSnake(personData))
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw new Error(`Failed to update person: ${error.message}`);
    }

    // Log audit
    await AuditService.log('UPDATE', 'PERSON', id, {
      userId: updatedBy,
      oldValues: currentPerson,
      newValues: updatedPerson,
    });

    // Return updated person with entities
    const result = await this.getPersonById(id);
    if (!result) {
      throw new Error('Failed to fetch updated person.');
    }

    return result;
  }

  /**
   * Soft deletes a person (sets is_active to false).
   */
  static async deletePerson(id: string, deletedBy: string): Promise<void> {
    const supabase = await createClient();
    
    // Get current person for audit
    const currentPerson = await this.getPersonById(id);
    if (!currentPerson) {
      throw new Error('Person not found.');
    }

    const { error } = await supabase
      .from('persons')
      .update({ is_active: false })
      .eq('id', id);

    if (error) {
      throw new Error(`Failed to delete person: ${error.message}`);
    }

    // Log audit
    await AuditService.log('DELETE', 'PERSON', id, {
      userId: deletedBy,
      oldValues: currentPerson,
    });
  }

  /**
   * Gets statistics about persons.
   */
  static async getPersonStats(): Promise<PersonStatsResponse> {
    const supabase = await createClient();

    const [totalResult, activeResult, inactiveResult, noEmailResult, recentHiresResult] = await Promise.all([
      supabase.from('persons').select('id', { count: 'exact', head: true }),
      supabase.from('persons').select('id', { count: 'exact', head: true }).eq('is_active', true),
      supabase.from('persons').select('id', { count: 'exact', head: true }).eq('is_active', false),
      supabase.from('persons').select('id', { count: 'exact', head: true }).is('email', null),
      supabase.from('persons').select('id', { count: 'exact', head: true })
        .gte('hire_date', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString())
    ]);

    return {
      total: totalResult.count || 0,
      active: activeResult.count || 0,
      inactive: inactiveResult.count || 0,
      withoutEmail: noEmailResult.count || 0,
      recentHires: recentHiresResult.count || 0,
    };
  }

  /**
   * Assigns a person to an entity.
   */
  static async assignPersonToEntity(
    personId: string,
    entityData: CreatePersonEntityData,
    assignedBy: string
  ): Promise<PersonEntity> {
    const supabase = await createClient();

    // Check if assignment already exists and is active
    const { data: existingAssignment } = await supabase
      .from('entities_persons')
      .select('*')
      .eq('person_id', personId)
      .eq('entity_id', entityData.entityId)
      .is('end_date', null)
      .single();

    if (existingAssignment) {
      throw new Error('Person is already assigned to this entity.');
    }

    // If this is marked as primary, remove primary flag from other assignments
    if (entityData.isPrimary) {
      await supabase
        .from('entities_persons')
        .update({ is_primary: false })
        .eq('person_id', personId);
    }

    const assignmentData = {
      person_id: personId,
      entity_id: entityData.entityId,
      role_in_entity: entityData.roleInEntity,
      start_date: entityData.startDate || new Date(),
      is_primary: entityData.isPrimary || false
    };

    const { data: assignment, error } = await supabase
      .from('entities_persons')
      .insert(assignmentData)
      .select(`
        *,
        entities (
          id,
          name,
          type,
          code
        )
      `)
      .single();

    if (error) {
      throw new Error(`Failed to assign person to entity: ${error.message}`);
    }

    // Log audit
    await AuditService.log('CREATE', 'PERSON_ENTITY', assignment.id, {
      userId: assignedBy,
      newValues: assignment,
    });

    const camelAssignment = keysToCamel(assignment) as any;
    return {
      ...camelAssignment,
      entity: camelAssignment.entities
    };
  }

  /**
   * Updates a person's entity assignment.
   */
  static async updatePersonEntityAssignment(
    assignmentId: string,
    entityData: UpdatePersonEntityData,
    updatedBy: string
  ): Promise<PersonEntity> {
    const supabase = await createClient();

    // Get current assignment for audit
    const { data: currentAssignment } = await supabase
      .from('entities_persons')
      .select('*')
      .eq('id', assignmentId)
      .single();

    if (!currentAssignment) {
      throw new Error('Assignment not found.');
    }

    // If this is marked as primary, remove primary flag from other assignments for this person
    if (entityData.isPrimary) {
      await supabase
        .from('entities_persons')
        .update({ is_primary: false })
        .eq('person_id', currentAssignment.person_id)
        .neq('id', assignmentId);
    }

    const updateData = keysToSnake(entityData);
    delete updateData.id; // Remove id from update data

    const { data: updatedAssignment, error } = await supabase
      .from('entities_persons')
      .update(updateData)
      .eq('id', assignmentId)
      .select(`
        *,
        entities (
          id,
          name,
          type,
          code
        )
      `)
      .single();

    if (error) {
      throw new Error(`Failed to update assignment: ${error.message}`);
    }

    // Log audit
    await AuditService.log('UPDATE', 'PERSON_ENTITY', assignmentId, {
      userId: updatedBy,
      oldValues: currentAssignment,
      newValues: updatedAssignment,
    });

    const camelAssignment = keysToCamel(updatedAssignment) as any;
    return {
      ...camelAssignment,
      entity: camelAssignment.entities
    };
  }

  /**
   * Removes a person from an entity (sets end_date).
   */
  static async removePersonFromEntity(
    assignmentId: string,
    removedBy: string
  ): Promise<void> {
    const supabase = await createClient();

    // Get current assignment for audit
    const { data: currentAssignment } = await supabase
      .from('entities_persons')
      .select('*')
      .eq('id', assignmentId)
      .single();

    if (!currentAssignment) {
      throw new Error('Assignment not found.');
    }

    const { error } = await supabase
      .from('entities_persons')
      .update({ end_date: new Date().toISOString() })
      .eq('id', assignmentId);

    if (error) {
      throw new Error(`Failed to remove person from entity: ${error.message}`);
    }

    // Log audit
    await AuditService.log('DELETE', 'PERSON_ENTITY', assignmentId, {
      userId: removedBy,
      oldValues: currentAssignment,
    });
  }

  /**
   * Gets persons by entity ID.
   */
  static async getPersonsByEntity(entityId: string): Promise<PersonWithEntities[]> {
    const supabase = await createClient();

    const { data, error } = await supabase
      .from('entities_persons')
      .select(`
        *,
        persons (
          *
        )
      `)
      .eq('entity_id', entityId)
      .is('end_date', null)
      .order('is_primary', { ascending: false })
      .order('start_date', { ascending: true });

    if (error) {
      throw new Error(`Failed to fetch persons for entity: ${error.message}`);
    }

    return (data || []).map((assignment: any) => {
      const person = keysToCamel(assignment.persons);
      return {
        ...person,
        fullName: `${person.firstName} ${person.lastName}`,
        displayName: person.firstName + ' ' + person.lastName,
        entities: [keysToCamel(assignment)],
        primaryEntity: assignment.is_primary ? { id: entityId } : undefined
      };
    });
  }
}
