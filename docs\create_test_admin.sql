-- Script pour créer un utilisateur admin de test
-- Re<PERSON>lacez les valeurs par vos propres données

-- 1. Insérer un utilisateur dans auth_users (simulant un utilisateur Supabase)
-- Remplacez 'your-supabase-uuid' par un UUID réel de votre utilisateur Supabase
INSERT INTO auth_users (id, supabase_id, email, is_active) VALUES
('550e8400-e29b-41d4-a716-************', 'your-supabase-uuid-here', '<EMAIL>', true)
ON CONFLICT (supabase_id) DO UPDATE SET
  email = EXCLUDED.email,
  is_active = EXCLUDED.is_active;

-- 2. Créer un profil pour cet utilisateur
INSERT INTO user_profiles (auth_user_id, display_name) VALUES
('550e8400-e29b-41d4-a716-************', 'Admin Test')
ON CONFLICT (auth_user_id) DO UPDATE SET
  display_name = EXCLUDED.display_name;

-- 3. Assign<PERSON> le rôle SUPER_ADMIN
INSERT INTO user_roles (user_id, role_id, is_active) VALUES
('550e8400-e29b-41d4-a716-************', (SELECT id FROM roles WHERE name = 'SUPER_ADMIN'), true)
ON CONFLICT (user_id, role_id) DO UPDATE SET
  is_active = EXCLUDED.is_active;

-- 4. Vérification
SELECT 
    au.email,
    up.display_name,
    r.name as role_name,
    ur.is_active
FROM auth_users au
LEFT JOIN user_profiles up ON au.id = up.auth_user_id
LEFT JOIN user_roles ur ON au.id = ur.user_id
LEFT JOIN roles r ON ur.role_id = r.id
WHERE au.email = '<EMAIL>';
