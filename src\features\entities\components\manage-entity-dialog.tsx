'use client';

import { useState } from 'react';
import { toast } from 'sonner';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { EntityForm } from './entity-form';
import { createEntityAction, updateEntityAction } from '../actions';
import type { EntitySchema } from '../schemas';
import type { Entity } from '@/types/entities';

interface ManageEntityDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  entities: Pick<Entity, 'id' | 'name'>[];
  entity?: Entity; // For editing
}

export function ManageEntityDialog({ isOpen, onClose, onSuccess, entities, entity }: ManageEntityDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (values: EntitySchema) => {
    setIsSubmitting(true);
    
    const action = entity
      ? updateEntityAction(entity.id, values)
      : createEntityAction(values);
      
    const result = await action;

    if (result.error) {
      toast.error('Erreur', { description: result.error });
    } else {
      toast.success('Succès', {
        description: `Entité ${entity ? 'mise à jour' : 'créée'} avec succès.`
      });
      onSuccess();
      onClose();
    }
    
    setIsSubmitting(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{entity ? "Modifier l'entité" : 'Créer une nouvelle entité'}</DialogTitle>
          <DialogDescription>
            Remplissez les informations ci-dessous pour {entity ? 'mettre à jour' : 'créer'} une entité.
          </DialogDescription>
        </DialogHeader>
        <EntityForm
          onSubmit={handleSubmit}
          isSubmitting={isSubmitting}
          entities={entities}
          defaultValues={entity}
        />
      </DialogContent>
    </Dialog>
  );
}