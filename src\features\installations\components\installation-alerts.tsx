// Installation Alerts & Decisions Tab - KYA Dashboards
'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  AlertTriangle, 
  Clock, 
  CheckCircle, 
  XCircle,
  Bell,
  Calendar,
  MapPin,
  User,
  Zap
} from 'lucide-react';

import type { InstallationKPIs } from '../types';
import { InstallationStatusBadge, ProductTypeBadge } from './installation-dashboard';

interface InstallationAlertsProps {
  kpis?: InstallationKPIs;
}

export function InstallationAlerts({ kpis }: InstallationAlertsProps) {
  if (!kpis) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <Card key={i}>
            <CardContent className="pt-6">
              <div className="animate-pulse space-y-2">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  const { overdue_installations, stats } = kpis;

  // Generate alerts based on data
  const criticalAlerts = overdue_installations.map(installation => ({
    id: installation.id,
    type: 'CRITICAL' as const,
    title: `Installation Overdue: ${installation.name}`,
    description: `${installation.client?.name} - ${Math.ceil((new Date().getTime() - new Date(installation.planned_end_date!).getTime()) / (1000 * 60 * 60 * 24))} days overdue`,
    installation,
    actions: ['Contact Client', 'Reschedule', 'Escalate']
  }));

  const importantAlerts = [
    ...(stats.active_installations > 10 ? [{
      id: 'high-workload',
      type: 'IMPORTANT' as const,
      title: 'High Workload Alert',
      description: `${stats.active_installations} active installations may require additional resources`,
      actions: ['Review Capacity', 'Assign Resources']
    }] : []),
    ...(stats.average_progress < 50 ? [{
      id: 'low-progress',
      type: 'IMPORTANT' as const,
      title: 'Low Average Progress',
      description: `Average progress is ${Math.round(stats.average_progress)}% - below target`,
      actions: ['Review Bottlenecks', 'Team Meeting']
    }] : [])
  ];

  // TODO: Replace with real API call to fetch notifications
  const notifications: any[] = [];

  // TODO: Replace with real API call to fetch decisions
  const decisions: any[] = [];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Alertes et Décisions</h2>
          <p className="text-muted-foreground">
            Problèmes critiques et décisions en attente nécessitant une attention
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant="destructive">
            {criticalAlerts.length} Critique{criticalAlerts.length > 1 ? 's' : ''}
          </Badge>
          <Badge variant="secondary">
            {importantAlerts.length} Important{importantAlerts.length > 1 ? 's' : ''}
          </Badge>
        </div>
      </div>

      {/* Critical Alerts */}
      {criticalAlerts.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2 text-red-600">
              <AlertTriangle className="h-5 w-5" />
              <span>Alertes Critiques ({criticalAlerts.length})</span>
            </CardTitle>
            <CardDescription>
              Problèmes nécessitant une attention immédiate
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {criticalAlerts.map((alert) => (
              <Alert key={alert.id} className="border-red-200 bg-red-50">
                <AlertTriangle className="h-4 w-4 text-red-600" />
                <AlertDescription>
                  <div className="flex items-start justify-between">
                    <div className="space-y-2">
                      <h4 className="font-medium text-red-800">{alert.title}</h4>
                      <p className="text-sm text-red-700">{alert.description}</p>
                      <div className="flex items-center space-x-4 text-xs text-red-600">
                        <span className="flex items-center">
                          <MapPin className="h-3 w-3 mr-1" />
                          {alert.installation?.site_location}
                        </span>
                        <span className="flex items-center">
                          <User className="h-3 w-3 mr-1" />
                          {alert.installation?.team_leader?.firstName} {alert.installation?.team_leader?.lastName}
                        </span>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {alert.actions.map((action) => (
                        <Button key={action} variant="outline" size="sm">
                          {action === 'Contact Client' ? 'Contacter Client' :
                           action === 'Reschedule' ? 'Reprogrammer' :
                           action === 'Escalate' ? 'Escalader' :
                           action === 'Review Capacity' ? 'Réviser Capacité' :
                           action === 'Assign Resources' ? 'Assigner Ressources' :
                           action === 'Review Bottlenecks' ? 'Réviser Goulots' :
                           action === 'Team Meeting' ? 'Réunion Équipe' :
                           action}
                        </Button>
                      ))}
                    </div>
                  </div>
                </AlertDescription>
              </Alert>
            ))}
          </CardContent>
        </Card>
      )}

      {/* Important Alerts */}
      {importantAlerts.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2 text-orange-600">
              <Clock className="h-5 w-5" />
              <span>Alertes Importantes ({importantAlerts.length})</span>
            </CardTitle>
            <CardDescription>
              Problèmes nécessitant une attention dans les 24 heures
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {importantAlerts.map((alert) => (
              <Alert key={alert.id} className="border-orange-200 bg-orange-50">
                <Clock className="h-4 w-4 text-orange-600" />
                <AlertDescription>
                  <div className="flex items-start justify-between">
                    <div className="space-y-1">
                      <h4 className="font-medium text-orange-800">{alert.title}</h4>
                      <p className="text-sm text-orange-700">{alert.description}</p>
                    </div>
                    <div className="flex items-center space-x-2">
                      {alert.actions.map((action) => (
                        <Button key={action} variant="outline" size="sm">
                          {action}
                        </Button>
                      ))}
                    </div>
                  </div>
                </AlertDescription>
              </Alert>
            ))}
          </CardContent>
        </Card>
      )}

      {/* Pending Decisions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <CheckCircle className="h-5 w-5" />
            <span>Pending Decisions ({decisions.length})</span>
          </CardTitle>
          <CardDescription>
            Decisions requiring management approval
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {decisions.map((decision) => (
            <div key={decision.id} className="p-4 border rounded-lg">
              <div className="flex items-start justify-between">
                <div className="space-y-2">
                  <h4 className="font-medium">{decision.title}</h4>
                  <p className="text-sm text-muted-foreground">{decision.description}</p>
                  <div className="flex items-center space-x-4 text-xs">
                    <Badge variant="outline">
                      Impact: {decision.impact}
                    </Badge>
                    <span className="flex items-center text-red-600">
                      <Clock className="h-3 w-3 mr-1" />
                      Deadline: {decision.deadline}
                    </span>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  {decision.actions.map((action: string) => (
                    <Button 
                      key={action} 
                      variant={action === 'Approve' ? 'default' : 'outline'} 
                      size="sm"
                    >
                      {action}
                    </Button>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* System Notifications */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Bell className="h-5 w-5" />
            <span>System Notifications ({notifications.length})</span>
          </CardTitle>
          <CardDescription>
            Recent system alerts and updates
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-3">
          {notifications.map((notification) => (
            <div key={notification.id} className="flex items-start space-x-3 p-3 rounded-lg bg-gray-50">
              <div className="flex-shrink-0">
                {notification.type === 'INFO' ? (
                  <Bell className="h-4 w-4 text-blue-500" />
                ) : (
                  <AlertTriangle className="h-4 w-4 text-orange-500" />
                )}
              </div>
              <div className="flex-1 space-y-1">
                <h5 className="text-sm font-medium">{notification.title}</h5>
                <p className="text-xs text-muted-foreground">{notification.description}</p>
              </div>
              <div className="text-xs text-muted-foreground">
                {notification.time}
              </div>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>
            Common actions for alert management
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button variant="outline" className="h-20 flex-col">
              <Calendar className="h-6 w-6 mb-2" />
              <span className="text-xs">Schedule Meeting</span>
            </Button>
            <Button variant="outline" className="h-20 flex-col">
              <User className="h-6 w-6 mb-2" />
              <span className="text-xs">Contact Client</span>
            </Button>
            <Button variant="outline" className="h-20 flex-col">
              <Zap className="h-6 w-6 mb-2" />
              <span className="text-xs">Escalate Issue</span>
            </Button>
            <Button variant="outline" className="h-20 flex-col">
              <CheckCircle className="h-6 w-6 mb-2" />
              <span className="text-xs">Mark Resolved</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
