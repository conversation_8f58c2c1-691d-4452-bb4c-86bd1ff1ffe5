import { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { Calendar, X, CalendarDays } from 'lucide-react';
import { format, subDays, subWeeks, subMonths, startOfDay, endOfDay } from 'date-fns';
import { fr } from 'date-fns/locale';

export interface DateRange {
  from: Date;
  to: Date;
  label?: string;
}

interface DateFilterProps {
  value?: DateRange;
  onChange: (range: DateRange | undefined) => void;
  className?: string;
  placeholder?: string;
  showPresets?: boolean;
  showClear?: boolean;
  disabled?: boolean;
}

const datePresets = [
  {
    label: "Aujourd'hui",
    getValue: () => ({
      from: startOfDay(new Date()),
      to: endOfDay(new Date()),
      label: "Aujourd'hui"
    })
  },
  {
    label: "7 derniers jours",
    getValue: () => ({
      from: startOfDay(subDays(new Date(), 6)),
      to: endOfDay(new Date()),
      label: "7 derniers jours"
    })
  },
  {
    label: "30 derniers jours",
    getValue: () => ({
      from: startOfDay(subDays(new Date(), 29)),
      to: endOfDay(new Date()),
      label: "30 derniers jours"
    })
  },
  {
    label: "Cette semaine",
    getValue: () => {
      const now = new Date();
      const startOfWeek = subDays(now, now.getDay());
      return {
        from: startOfDay(startOfWeek),
        to: endOfDay(now),
        label: "Cette semaine"
      };
    }
  },
  {
    label: "Ce mois",
    getValue: () => {
      const now = new Date();
      const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
      return {
        from: startOfDay(startOfMonth),
        to: endOfDay(now),
        label: "Ce mois"
      };
    }
  },
  {
    label: "3 derniers mois",
    getValue: () => ({
      from: startOfDay(subMonths(new Date(), 3)),
      to: endOfDay(new Date()),
      label: "3 derniers mois"
    })
  }
];

export function DateFilter({
  value,
  onChange,
  className,
  placeholder = "Sélectionner une période",
  showPresets = true,
  showClear = true,
  disabled = false,
}: DateFilterProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [customFrom, setCustomFrom] = useState('');
  const [customTo, setCustomTo] = useState('');

  const formatDateRange = (range: DateRange) => {
    if (range.label) return range.label;
    
    const fromStr = format(range.from, 'dd/MM/yyyy', { locale: fr });
    const toStr = format(range.to, 'dd/MM/yyyy', { locale: fr });
    
    if (fromStr === toStr) {
      return fromStr;
    }
    
    return `${fromStr} - ${toStr}`;
  };

  const handlePresetClick = (preset: typeof datePresets[0]) => {
    const range = preset.getValue();
    onChange(range);
    setIsOpen(false);
  };

  const handleCustomApply = () => {
    if (!customFrom || !customTo) return;
    
    const fromDate = new Date(customFrom);
    const toDate = new Date(customTo);
    
    if (fromDate > toDate) return;
    
    onChange({
      from: startOfDay(fromDate),
      to: endOfDay(toDate),
      label: `${format(fromDate, 'dd/MM/yyyy')} - ${format(toDate, 'dd/MM/yyyy')}`
    });
    setIsOpen(false);
  };

  const handleClear = () => {
    onChange(undefined);
    setCustomFrom('');
    setCustomTo('');
  };

  return (
    <div className={cn('relative', className)}>
      <Button
        variant="outline"
        onClick={() => setIsOpen(!isOpen)}
        disabled={disabled}
        className={cn(
          'w-full justify-start text-left font-normal h-9',
          !value && 'text-muted-foreground'
        )}
      >
        <Calendar className="mr-2 h-4 w-4" />
        {value ? formatDateRange(value) : placeholder}
        {value && showClear && (
          <X 
            className="ml-auto h-4 w-4 hover:text-red-500" 
            onClick={(e) => {
              e.stopPropagation();
              handleClear();
            }}
          />
        )}
      </Button>

      {isOpen && (
        <Card className="absolute top-full left-0 z-50 mt-2 w-80 shadow-lg">
          <CardContent className="p-4 space-y-4">
            {/* Presets */}
            {showPresets && (
              <div className="space-y-2">
                <Label className="text-sm font-medium">Périodes prédéfinies</Label>
                <div className="grid grid-cols-2 gap-2">
                  {datePresets.map((preset) => (
                    <Button
                      key={preset.label}
                      variant="ghost"
                      size="sm"
                      className="justify-start h-auto p-2 text-xs"
                      onClick={() => handlePresetClick(preset)}
                    >
                      {preset.label}
                    </Button>
                  ))}
                </div>
              </div>
            )}

            {/* Séparateur */}
            {showPresets && <div className="border-t" />}

            {/* Sélection personnalisée */}
            <div className="space-y-3">
              <Label className="text-sm font-medium">Période personnalisée</Label>
              
              <div className="grid grid-cols-2 gap-2">
                <div className="space-y-1">
                  <Label className="text-xs text-muted-foreground">Du</Label>
                  <Input
                    type="date"
                    value={customFrom}
                    onChange={(e) => setCustomFrom(e.target.value)}
                    className="text-xs"
                  />
                </div>
                <div className="space-y-1">
                  <Label className="text-xs text-muted-foreground">Au</Label>
                  <Input
                    type="date"
                    value={customTo}
                    onChange={(e) => setCustomTo(e.target.value)}
                    className="text-xs"
                  />
                </div>
              </div>

              <div className="flex justify-between">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsOpen(false)}
                >
                  Annuler
                </Button>
                <Button
                  size="sm"
                  onClick={handleCustomApply}
                  disabled={!customFrom || !customTo}
                >
                  Appliquer
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

// Composant pour afficher les filtres de date actifs
interface ActiveDateFiltersProps {
  filters: Array<{
    id: string;
    label: string;
    range: DateRange;
  }>;
  onRemove: (id: string) => void;
  className?: string;
}

export function ActiveDateFilters({ 
  filters, 
  onRemove, 
  className 
}: ActiveDateFiltersProps) {
  if (filters.length === 0) return null;

  return (
    <div className={cn('flex flex-wrap gap-2', className)}>
      {filters.map((filter) => (
        <Badge
          key={filter.id}
          variant="secondary"
          className="flex items-center gap-1 pr-1"
        >
          <CalendarDays className="h-3 w-3" />
          <span className="text-xs">{filter.label}</span>
          <Button
            variant="ghost"
            size="sm"
            className="h-4 w-4 p-0 hover:bg-transparent"
            onClick={() => onRemove(filter.id)}
          >
            <X className="h-3 w-3" />
          </Button>
        </Badge>
      ))}
    </div>
  );
}

// Hook pour gérer les filtres de date
export function useDateFilter(initialRange?: DateRange) {
  const [dateRange, setDateRange] = useState<DateRange | undefined>(initialRange);

  const setPreset = (presetLabel: string) => {
    const preset = datePresets.find(p => p.label === presetLabel);
    if (preset) {
      setDateRange(preset.getValue());
    }
  };

  const setCustomRange = (from: Date, to: Date, label?: string) => {
    setDateRange({
      from: startOfDay(from),
      to: endOfDay(to),
      label: label || `${format(from, 'dd/MM/yyyy')} - ${format(to, 'dd/MM/yyyy')}`
    });
  };

  const clear = () => {
    setDateRange(undefined);
  };

  const isInRange = (date: Date) => {
    if (!dateRange) return true;
    return date >= dateRange.from && date <= dateRange.to;
  };

  return {
    dateRange,
    setDateRange,
    setPreset,
    setCustomRange,
    clear,
    isInRange,
  };
}

// Composant de filtre de date rapide (boutons uniquement)
interface QuickDateFilterProps {
  value?: string;
  onChange: (preset: string) => void;
  className?: string;
}

export function QuickDateFilter({ 
  value, 
  onChange, 
  className 
}: QuickDateFilterProps) {
  const quickPresets = [
    { key: 'today', label: "Aujourd'hui" },
    { key: '7days', label: '7j' },
    { key: '30days', label: '30j' },
    { key: 'month', label: 'Ce mois' },
  ];

  return (
    <div className={cn('flex gap-1', className)}>
      {quickPresets.map((preset) => (
        <Button
          key={preset.key}
          variant={value === preset.key ? 'default' : 'outline'}
          size="sm"
          onClick={() => onChange(preset.key)}
          className="text-xs px-2"
        >
          {preset.label}
        </Button>
      ))}
    </div>
  );
}
