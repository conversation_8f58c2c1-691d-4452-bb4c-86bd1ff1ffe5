// Installation Tracking Form - KYA Dashboards
'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { 
  Save, 
  Calculator, 
  AlertCircle,
  CheckCircle,
  Clock
} from 'lucide-react';

import { useInstallationTracking } from '../hooks/use-installations';
import { InstallationService } from '../services/installation-service';
import type { Installation, InstallationTrackingData, CommissioningTestStatus } from '../types';

interface InstallationTrackingFormProps {
  installation: Installation;
  onSubmit?: () => void;
}

export function InstallationTrackingForm({ installation, onSubmit }: InstallationTrackingFormProps) {
  const [activeTab, setActiveTab] = useState('progress');
  const [calculatedProgress, setCalculatedProgress] = useState(0);
  
  const { todayTracking, updateTracking, isUpdating } = useInstallationTracking(installation.id);
  
  const form = useForm<InstallationTrackingData>({
    defaultValues: todayTracking.data || {}
  });

  const { watch, setValue, handleSubmit, formState: { errors } } = form;
  const watchedValues = watch();

  // Update form when today's tracking data is loaded
  useEffect(() => {
    if (todayTracking.data) {
      Object.entries(todayTracking.data).forEach(([key, value]) => {
        if (value !== null && value !== undefined) {
          setValue(key as keyof InstallationTrackingData, value);
        }
      });
    }
  }, [todayTracking.data, setValue]);

  // Calculate global progress based on individual steps
  useEffect(() => {
    const steps = InstallationService.getProgressStepsForProduct(installation.product_type);
    const progressValues = steps.map(step => watchedValues[step as keyof InstallationTrackingData] as number || 0);
    const average = progressValues.length > 0 
      ? Math.round(progressValues.reduce((sum, val) => sum + val, 0) / progressValues.length)
      : 0;
    
    setCalculatedProgress(average);
    setValue('global_progress', average);
  }, [watchedValues, installation.product_type, setValue]);

  const onFormSubmit = async (data: InstallationTrackingData) => {
    try {
      await updateTracking.mutateAsync({ 
        data, 
        userId: 'current-user-id' // This should come from your auth context
      });
      onSubmit?.();
    } catch (error) {
      console.error('Error updating tracking:', error);
    }
  };

  const progressSteps = InstallationService.getProgressStepsForProduct(installation.product_type);

  return (
    <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-6">
      {/* Header with calculated progress */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium">Daily Progress Update</h3>
          <p className="text-sm text-muted-foreground">
            {new Date().toLocaleDateString()} - {installation.name}
          </p>
        </div>
        <div className="text-right">
          <div className="text-2xl font-bold">{calculatedProgress}%</div>
          <p className="text-xs text-muted-foreground">Calculated Progress</p>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="progress">Progress</TabsTrigger>
          <TabsTrigger value="testing">Testing</TabsTrigger>
          <TabsTrigger value="notes">Notes</TabsTrigger>
        </TabsList>

        <TabsContent value="progress" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Installation Progress</CardTitle>
              <CardDescription>
                Update completion percentage for each installation step
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Common steps for all product types */}
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="execution_file_progress">Execution File (%)</Label>
                  <Input
                    id="execution_file_progress"
                    type="number"
                    min="0"
                    max="100"
                    {...form.register('execution_file_progress', { 
                      valueAsNumber: true,
                      min: 0,
                      max: 100
                    })}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="metalwork_progress">Metalwork (%)</Label>
                  <Input
                    id="metalwork_progress"
                    type="number"
                    min="0"
                    max="100"
                    {...form.register('metalwork_progress', { 
                      valueAsNumber: true,
                      min: 0,
                      max: 100
                    })}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="excavation_progress">Excavation (%)</Label>
                  <Input
                    id="excavation_progress"
                    type="number"
                    min="0"
                    max="100"
                    {...form.register('excavation_progress', { 
                      valueAsNumber: true,
                      min: 0,
                      max: 100
                    })}
                  />
                </div>
              </div>

              {/* KYA-SoP specific steps */}
              {installation.product_type === 'KYA-SoP' && (
                <div className="space-y-4">
                  <h4 className="font-medium text-sm">Solar Installation Steps</h4>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="pv_supports_progress">PV Supports (%)</Label>
                      <Input
                        id="pv_supports_progress"
                        type="number"
                        min="0"
                        max="100"
                        {...form.register('pv_supports_progress', { 
                          valueAsNumber: true,
                          min: 0,
                          max: 100
                        })}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="modules_wiring_progress">Modules & Wiring (%)</Label>
                      <Input
                        id="modules_wiring_progress"
                        type="number"
                        min="0"
                        max="100"
                        {...form.register('modules_wiring_progress', { 
                          valueAsNumber: true,
                          min: 0,
                          max: 100
                        })}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="pv_inverter_cables_progress">PV-Inverter Cables (%)</Label>
                      <Input
                        id="pv_inverter_cables_progress"
                        type="number"
                        min="0"
                        max="100"
                        {...form.register('pv_inverter_cables_progress', { 
                          valueAsNumber: true,
                          min: 0,
                          max: 100
                        })}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="inverters_wiring_progress">Inverters & Wiring (%)</Label>
                      <Input
                        id="inverters_wiring_progress"
                        type="number"
                        min="0"
                        max="100"
                        {...form.register('inverters_wiring_progress', { 
                          valueAsNumber: true,
                          min: 0,
                          max: 100
                        })}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="batteries_wiring_progress">Batteries & Wiring (%)</Label>
                      <Input
                        id="batteries_wiring_progress"
                        type="number"
                        min="0"
                        max="100"
                        {...form.register('batteries_wiring_progress', { 
                          valueAsNumber: true,
                          min: 0,
                          max: 100
                        })}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="ac_dc_boxes_progress">AC-DC Boxes (%)</Label>
                      <Input
                        id="ac_dc_boxes_progress"
                        type="number"
                        min="0"
                        max="100"
                        {...form.register('ac_dc_boxes_progress', { 
                          valueAsNumber: true,
                          min: 0,
                          max: 100
                        })}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="load_separation_progress">Load Separation (%)</Label>
                      <Input
                        id="load_separation_progress"
                        type="number"
                        min="0"
                        max="100"
                        {...form.register('load_separation_progress', { 
                          valueAsNumber: true,
                          min: 0,
                          max: 100
                        })}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="battery_inverter_connection_progress">Battery-Inverter Connection (%)</Label>
                      <Input
                        id="battery_inverter_connection_progress"
                        type="number"
                        min="0"
                        max="100"
                        {...form.register('battery_inverter_connection_progress', { 
                          valueAsNumber: true,
                          min: 0,
                          max: 100
                        })}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="grounding_progress">Grounding (%)</Label>
                      <Input
                        id="grounding_progress"
                        type="number"
                        min="0"
                        max="100"
                        {...form.register('grounding_progress', { 
                          valueAsNumber: true,
                          min: 0,
                          max: 100
                        })}
                      />
                    </div>
                  </div>
                </div>
              )}

              {/* Lampadaire specific steps */}
              {installation.product_type === 'Lampadaire' && (
                <div className="space-y-4">
                  <h4 className="font-medium text-sm">Street Light Installation Steps</h4>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="pole_installation_progress">Pole Installation (%)</Label>
                      <Input
                        id="pole_installation_progress"
                        type="number"
                        min="0"
                        max="100"
                        {...form.register('pole_installation_progress', { 
                          valueAsNumber: true,
                          min: 0,
                          max: 100
                        })}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="lamp_installation_progress">Lamp Installation (%)</Label>
                      <Input
                        id="lamp_installation_progress"
                        type="number"
                        min="0"
                        max="100"
                        {...form.register('lamp_installation_progress', { 
                          valueAsNumber: true,
                          min: 0,
                          max: 100
                        })}
                      />
                    </div>
                  </div>
                </div>
              )}

              {/* Global Progress Display */}
              <div className="pt-4 border-t">
                <div className="flex items-center justify-between mb-2">
                  <Label>Global Progress</Label>
                  <Badge variant="outline">
                    <Calculator className="h-3 w-3 mr-1" />
                    Auto-calculated
                  </Badge>
                </div>
                <Progress value={calculatedProgress} className="h-3" />
                <p className="text-xs text-muted-foreground mt-1">
                  Calculated from {progressSteps.length} installation steps
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="testing" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Testing & Commissioning</CardTitle>
              <CardDescription>
                Record testing results and commissioning status
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="commissioning_test_status">Test Status</Label>
                  <Select
                    value={watchedValues.commissioning_test_status || ''}
                    onValueChange={(value) => setValue('commissioning_test_status', value as CommissioningTestStatus)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select test status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="NOT_STARTED">Not Started</SelectItem>
                      <SelectItem value="IN_PROGRESS">In Progress</SelectItem>
                      <SelectItem value="PASSED_WITH_RESERVES">Passed with Reserves</SelectItem>
                      <SelectItem value="PASSED_WITHOUT_RESERVES">Passed without Reserves</SelectItem>
                      <SelectItem value="FAILED">Failed</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="commissioning_test_date">Test Date</Label>
                  <Input
                    id="commissioning_test_date"
                    type="date"
                    {...form.register('commissioning_test_date')}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="commissioning_test_notes">Test Notes</Label>
                <Textarea
                  id="commissioning_test_notes"
                  placeholder="Describe test results, issues found, or reserves..."
                  {...form.register('commissioning_test_notes')}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="hours_worked">Hours Worked Today</Label>
                  <Input
                    id="hours_worked"
                    type="number"
                    step="0.5"
                    min="0"
                    max="24"
                    {...form.register('hours_worked', { valueAsNumber: true })}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="weather_conditions">Weather Conditions</Label>
                  <Input
                    id="weather_conditions"
                    placeholder="e.g., Sunny, Rainy, Cloudy"
                    {...form.register('weather_conditions')}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="notes" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Daily Notes & Comments</CardTitle>
              <CardDescription>
                Record daily observations, issues, and next actions
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="daily_comments">Daily Comments</Label>
                <Textarea
                  id="daily_comments"
                  placeholder="General comments about today's work..."
                  {...form.register('daily_comments')}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="issues_encountered">Issues Encountered</Label>
                <Textarea
                  id="issues_encountered"
                  placeholder="Describe any problems or challenges faced..."
                  {...form.register('issues_encountered')}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="next_actions">Next Actions</Label>
                <Textarea
                  id="next_actions"
                  placeholder="What needs to be done next..."
                  {...form.register('next_actions')}
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Submit Button */}
      <div className="flex items-center justify-between pt-4 border-t">
        <div className="text-sm text-muted-foreground">
          {todayTracking.data ? (
            <span className="flex items-center">
              <CheckCircle className="h-4 w-4 mr-1 text-green-500" />
              Already tracked today - updates will modify existing entry
            </span>
          ) : (
            <span className="flex items-center">
              <Clock className="h-4 w-4 mr-1 text-blue-500" />
              New tracking entry for today
            </span>
          )}
        </div>
        <Button type="submit" disabled={isUpdating}>
          {isUpdating ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Saving...
            </>
          ) : (
            <>
              <Save className="h-4 w-4 mr-2" />
              Save Progress
            </>
          )}
        </Button>
      </div>
    </form>
  );
}
