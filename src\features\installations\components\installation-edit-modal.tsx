'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Loader2, Save, Edit } from 'lucide-react';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import type { Installation } from '@/types/shared';

// Schema de validation pour la modification simple
const editInstallationSchema = z.object({
  name: z.string().min(1, 'Le nom est requis'),
  site_location: z.string().min(1, 'La localisation est requise'),
  gps_coordinates: z.string().optional(),
  status: z.enum(['PLANNING', 'IN_PROGRESS', 'COMPLETED', 'ON_HOLD']),
  planned_start_date: z.string().optional(),
  planned_end_date: z.string().optional(),
  notes: z.string().optional(),
});

type EditInstallationFormData = z.infer<typeof editInstallationSchema>;

interface InstallationEditModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  installation: Installation;
  onSave?: (data: EditInstallationFormData) => void;
}

export function InstallationEditModal({ 
  open, 
  onOpenChange, 
  installation,
  onSave 
}: InstallationEditModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<EditInstallationFormData>({
    resolver: zodResolver(editInstallationSchema),
    defaultValues: {
      name: installation.name || '',
      site_location: installation.site_location || '',
      gps_coordinates: installation.gps_coordinates || '',
      status: installation.status as any || 'PLANNING',
      planned_start_date: installation.planned_start_date ? new Date(installation.planned_start_date).toISOString().split('T')[0] : '',
      planned_end_date: installation.planned_end_date ? new Date(installation.planned_end_date).toISOString().split('T')[0] : '',
      notes: '',
    },
  });

  const onSubmit = async (data: EditInstallationFormData) => {
    setIsSubmitting(true);
    
    try {
      // TODO: Appeler l'action de modification
      console.log('Edit installation data:', data);
      
      if (onSave) {
        onSave(data);
      }
      
      // Fermer le modal
      onOpenChange(false);
      form.reset();
    } catch (error) {
      console.error('Error updating installation:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-kya-primary">
            <Edit className="h-5 w-5" />
            Modifier l'Installation
          </DialogTitle>
          <DialogDescription>
            Modifiez les informations essentielles de l'installation {installation.installation_number}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Informations de base */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nom de l'Installation</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Nom de l'installation"
                        {...field}
                        className="border-kya-primary/30 focus:border-kya-primary"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Statut</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Sélectionner un statut" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="PLANNING">Planification</SelectItem>
                        <SelectItem value="IN_PROGRESS">En cours</SelectItem>
                        <SelectItem value="COMPLETED">Terminé</SelectItem>
                        <SelectItem value="ON_HOLD">En attente</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Localisation */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="site_location"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Localisation du Site</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Adresse ou description du site"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="gps_coordinates"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Coordonnées GPS (optionnel)</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Latitude, Longitude"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Planification */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="planned_start_date"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Date de Début Prévue</FormLabel>
                    <FormControl>
                      <Input
                        type="date"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="planned_end_date"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Date de Fin Prévue</FormLabel>
                    <FormControl>
                      <Input
                        type="date"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Notes */}
            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Notes de Modification</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Raison de la modification ou notes particulières..."
                      className="min-h-[80px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Boutons d'action */}
            <div className="flex justify-end gap-4 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isSubmitting}
              >
                Annuler
              </Button>
              
              <Button
                type="submit"
                disabled={isSubmitting}
                className="gradient-kya-primary text-white hover:opacity-90"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Enregistrement...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Enregistrer
                  </>
                )}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
