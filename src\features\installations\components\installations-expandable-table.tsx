'use client';

import React, { useState } from 'react';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import {
  Edit,
  History,
  Plus,
  Calendar,
  Clock,
  CheckCircle,
  AlertTriangle,
  MapPin,
  User,
  Users,
  ChevronDown,
  ChevronRight,
  TrendingUp
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { TrackingModal } from './tracking-modal';
import { InstallationEditModal } from './installation-edit-modal';
import type { Installation } from '../types';

interface InstallationsExpandableTableProps {
  installations: Installation[];
}

export function InstallationsExpandableTable({ installations }: InstallationsExpandableTableProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [expandedRow, setExpandedRow] = useState<string | null>(null);
  const [trackingModalOpen, setTrackingModalOpen] = useState(false);
  const [editModalOpen, setEditModalOpen] = useState(false);
  const [selectedInstallation, setSelectedInstallation] = useState<Installation | null>(null);

  const handleInstallationClick = (installation: Installation) => {
    setExpandedRow(expandedRow === installation.id ? null : installation.id);
  };

  const filteredInstallations = installations.filter(installation => {
    const matchesSearch = installation.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         installation.client?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         installation.installation_number?.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesSearch;
  });

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      'PLANNING': { label: 'Planification', variant: 'secondary' as const, icon: Calendar },
      'IN_PROGRESS': { label: 'En cours', variant: 'default' as const, icon: Clock },
      'COMPLETED': { label: 'Terminé', variant: 'outline' as const, icon: CheckCircle },
      'ON_HOLD': { label: 'En attente', variant: 'destructive' as const, icon: AlertTriangle },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || {
      label: status,
      variant: 'outline' as const,
      icon: Clock
    };

    const Icon = config.icon;

    return (
      <Badge variant={config.variant} className="flex items-center gap-1 capitalize">
        <Icon className="h-3 w-3" />
        {config.label.toLowerCase()}
      </Badge>
    );
  };

  const getProgressColor = (progress: number) => {
    if (progress >= 80) return 'bg-green-500';
    if (progress >= 50) return 'bg-blue-500';
    if (progress >= 25) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  return (
    <div>
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="flex items-center gap-2 text-lg font-semibold">
            <TrendingUp className="h-5 w-5 text-kya-primary" />
            Installations ({filteredInstallations.length})
          </h3>
        </div>
        <div className="flex items-center gap-4">
          <Input
            placeholder="Rechercher une installation..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="max-w-sm"
          />
        </div>
      </div>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="text-left p-3 font-medium text-xs uppercase tracking-wide text-muted-foreground">
                  Installation
                </th>
                <th className="text-left p-3 font-medium text-xs uppercase tracking-wide text-muted-foreground">
                  Client
                </th>
                <th className="text-left p-3 font-medium text-xs uppercase tracking-wide text-muted-foreground">
                  Type
                </th>
                <th className="text-left p-3 font-medium text-xs uppercase tracking-wide text-muted-foreground">
                  Statut
                </th>
                <th className="text-left p-3 font-medium text-xs uppercase tracking-wide text-muted-foreground">
                  Progression
                </th>
                <th className="text-right p-3 font-medium text-xs uppercase tracking-wide text-muted-foreground">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody>
              {filteredInstallations.map((installation) => {
                const progress = installation.global_progress || 0;
                
                return (
                  <React.Fragment key={installation.id}>
                    <tr
                      className="border-b hover:bg-muted/50 transition-colors cursor-pointer"
                      onClick={() => handleInstallationClick(installation)}
                    >
                      <td className="p-4">
                        <div className="flex items-center gap-2">
                          {expandedRow === installation.id ? (
                            <ChevronDown className="h-4 w-4 text-muted-foreground" />
                          ) : (
                            <ChevronRight className="h-4 w-4 text-muted-foreground" />
                          )}
                          <div>
                            <p className="font-medium text-foreground">
                              {installation.name}
                            </p>
                            <p className="text-xs text-muted-foreground">
                              {installation.installation_number}
                            </p>
                          </div>
                        </div>
                      </td>
                      <td className="p-4">
                        <p className="font-medium">
                          {installation.client?.name || 'Non défini'}
                        </p>
                      </td>
                      <td className="p-4">
                        <Badge variant="outline">
                          {installation.product_type}
                        </Badge>
                      </td>
                      <td className="p-4">
                        {getStatusBadge(installation.status)}
                      </td>
                      <td className="p-4">
                        <div className="flex items-center space-x-2">
                          <div className="flex-1 bg-muted rounded-full h-1.5 max-w-[80px]">
                            <div
                              className={`h-1.5 rounded-full transition-all duration-500 ${getProgressColor(progress)}`}
                              style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
                            ></div>
                          </div>
                          <span className="font-medium min-w-[35px] text-xs">
                            {Math.round(progress)}%
                          </span>
                        </div>
                      </td>
                      <td className="p-4 text-right">
                        <div className="flex items-center justify-end space-x-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-7 px-2 text-xs"
                            onClick={(e) => {
                              e.stopPropagation();
                              setSelectedInstallation(installation);
                              setTrackingModalOpen(true);
                            }}
                          >
                            <Plus className="h-3 w-3 mr-1" />
                            Nouveau Suivi
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-7 px-2 text-xs"
                            onClick={(e) => {
                              e.stopPropagation();
                              window.location.href = `/saisie/installations/${installation.id}/tracking/history`;
                            }}
                          >
                            <History className="h-3 w-3 mr-1" />
                            Historique
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-7 px-2 text-xs"
                            onClick={(e) => {
                              e.stopPropagation();
                              setSelectedInstallation(installation);
                              setEditModalOpen(true);
                            }}
                          >
                            <Edit className="h-3 w-3 mr-1" />
                            Modifier
                          </Button>
                        </div>
                      </td>
                    </tr>
                    {expandedRow === installation.id && (
                      <tr>
                        <td colSpan={6} className="p-0">
                          <div className="bg-muted/30 border-t">
                            <div className="p-4 space-y-4">
                              {/* Informations détaillées de l'installation */}
                              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div className="space-y-2">
                                  <h4 className="font-semibold text-sm text-foreground">Informations Générales</h4>
                                  <div className="space-y-1 text-xs">
                                    <div><span className="font-medium">Numéro:</span> {installation.installation_number || 'Non défini'}</div>
                                    <div><span className="font-medium">Localisation:</span> {installation.site_location}</div>
                                    <div><span className="font-medium">Coordonnées GPS:</span> {installation.gps_coordinates || 'Non définies'}</div>
                                    <div><span className="font-medium">Projet:</span> {installation.project?.name || 'Non défini'}</div>
                                  </div>
                                </div>

                                <div className="space-y-2">
                                  <h4 className="font-semibold text-sm text-foreground">Planification</h4>
                                  <div className="space-y-1 text-xs">
                                    <div><span className="font-medium">Début prévu:</span> {installation.planned_start_date ? format(new Date(installation.planned_start_date), 'dd/MM/yyyy', { locale: fr }) : 'Non défini'}</div>
                                    <div><span className="font-medium">Fin prévue:</span> {installation.planned_end_date ? format(new Date(installation.planned_end_date), 'dd/MM/yyyy', { locale: fr }) : 'Non définie'}</div>
                                    <div><span className="font-medium">Statut:</span> {installation.status}</div>
                                  </div>
                                </div>

                                {/* Commentaire général de l'installation */}
                                <div className="space-y-4">
                                  {installation.comments && (
                                    <div>
                                      <h5 className="font-semibold text-sm text-foreground mb-2">Commentaire</h5>
                                      <div className="p-3 bg-muted/50 rounded-lg text-sm text-muted-foreground">
                                        {installation.comments}
                                      </div>
                                    </div>
                                  )}
                                </div>
                              </div>
                            </div>
                          </div>
                        </td>
                      </tr>
                    )}
                  </React.Fragment>
                );
              })}
            </tbody>
          </table>
        </div>

        {/* Modals */}
        {selectedInstallation && (
          <>
            <TrackingModal
              open={trackingModalOpen}
              onOpenChange={setTrackingModalOpen}
              installationId={selectedInstallation.id}
              productType={selectedInstallation.product_type}
            />

            <InstallationEditModal
              open={editModalOpen}
              onOpenChange={setEditModalOpen}
              installation={selectedInstallation}
              onSave={(data) => {
                console.log('Installation updated:', data);
                // TODO: Rafraîchir les données
              }}
            />
          </>
        )}
    </div>
  );
}
