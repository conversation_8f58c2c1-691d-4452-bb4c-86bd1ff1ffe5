'use client';

import { useState } from 'react';
import { Check, ChevronsUpDown, User, User<PERSON><PERSON><PERSON>, Users, Loader2 } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';
import { useActivePersons } from '@/hooks/use-persons';
import type { Person } from '@/types/shared';

interface TeamSelectorProps {
  leaderId?: string;
  memberIds?: string[];
  onLeaderChange: (leaderId: string | undefined) => void;
  onMembersChange: (memberIds: string[]) => void;
  disabled?: boolean;
}

export function TeamSelector({ leaderId, memberIds = [], onLeader<PERSON>hange, onMembersChange, disabled }: TeamSelectorProps) {
  const [leaderOpen, setLeaderOpen] = useState(false);
  const [membersOpen, setMembersOpen] = useState(false);

  // Récupérer les personnes actives depuis l'API
  const { data: persons = [], isLoading, error } = useActivePersons();

  const selectedLeader = persons.find(person => person.id === leaderId);
  const selectedMembers = persons.filter(person => memberIds.includes(person.id));
  const availableMembers = persons.filter(person => person.id !== leaderId);

  const getPersonDisplayName = (person: Person) => {
    return `${person.first_name} ${person.last_name}`;
  };

  const getPersonDetails = (person: Person) => {
    const details = [];
    if (person.position) details.push(person.position);
    if (person.employee_id) details.push(`#${person.employee_id}`);
    return details.join(' • ');
  };

  const handleMemberToggle = (personId: string) => {
    const newMemberIds = memberIds.includes(personId)
      ? memberIds.filter(id => id !== personId)
      : [...memberIds, personId];
    onMembersChange(newMemberIds);
  };

  // Gestion des erreurs de chargement
  if (error) {
    return (
      <div className="text-center py-4">
        <p className="text-sm text-red-600">Erreur lors du chargement des personnes</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Chef d'équipe */}
      <div className="space-y-2">
        <label className="text-sm font-medium">Chef d'Équipe</label>
        <Popover open={leaderOpen} onOpenChange={setLeaderOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              role="combobox"
              aria-expanded={leaderOpen}
              className="w-full justify-between bg-white"
              disabled={disabled}
            >
              {selectedLeader ? (
                <div className="flex items-center gap-2">
                  <UserCheck className="h-4 w-4 text-blue-600" />
                  <span>{getPersonDisplayName(selectedLeader)}</span>
                  <Badge variant="secondary" className="bg-blue-100 text-blue-700">
                    Chef
                  </Badge>
                </div>
              ) : (
                <div className="flex items-center gap-2 text-muted-foreground">
                  <User className="h-4 w-4" />
                  <span>Sélectionner un chef d'équipe</span>
                </div>
              )}
              <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-full p-0">
            <Command>
              <CommandInput placeholder="Rechercher une personne..." />
              <CommandList>
                <CommandEmpty>Aucune personne trouvée.</CommandEmpty>
                <CommandGroup>
                  {isLoading ? (
                    <CommandItem disabled>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Chargement...
                    </CommandItem>
                  ) : (
                    persons.map((person) => (
                    <CommandItem
                      key={person.id}
                      value={`${person.first_name} ${person.last_name} ${person.employee_id}`}
                      onSelect={() => {
                        onLeaderChange(person.id === leaderId ? undefined : person.id);
                        setLeaderOpen(false);
                      }}
                    >
                      <div className="flex items-center gap-2 flex-1">
                        <UserCheck className="h-4 w-4 text-blue-600" />
                        <div className="flex-1">
                          <div className="font-medium">{getPersonDisplayName(person)}</div>
                          <div className="text-sm text-muted-foreground">
                            {getPersonDetails(person)}
                          </div>
                        </div>
                        <Check
                          className={cn(
                            "h-4 w-4",
                            leaderId === person.id ? "opacity-100" : "opacity-0"
                          )}
                        />
                      </div>
                    </CommandItem>
                  ))
                  )}
                </CommandGroup>
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>
      </div>

      <Separator />

      {/* Membres de l'équipe */}
      <div className="space-y-2">
        <label className="text-sm font-medium">Membres de l'Équipe</label>
        <Popover open={membersOpen} onOpenChange={setMembersOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              role="combobox"
              aria-expanded={membersOpen}
              className="w-full justify-between bg-white min-h-[40px]"
              disabled={disabled}
            >
              {selectedMembers.length > 0 ? (
                <div className="flex items-center gap-2 flex-wrap">
                  <Users className="h-4 w-4 text-green-600" />
                  <span className="text-sm">
                    {selectedMembers.length} membre{selectedMembers.length > 1 ? 's' : ''} sélectionné{selectedMembers.length > 1 ? 's' : ''}
                  </span>
                </div>
              ) : (
                <div className="flex items-center gap-2 text-muted-foreground">
                  <Users className="h-4 w-4" />
                  <span>Sélectionner les membres</span>
                </div>
              )}
              <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-full p-0">
            <Command>
              <CommandInput placeholder="Rechercher des membres..." />
              <CommandList>
                <CommandEmpty>Aucune personne trouvée.</CommandEmpty>
                <CommandGroup>
                  {isLoading ? (
                    <CommandItem disabled>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Chargement...
                    </CommandItem>
                  ) : (
                    availableMembers.map((person) => (
                    <CommandItem
                      key={person.id}
                      value={`${person.first_name} ${person.last_name} ${person.employee_id}`}
                      onSelect={() => handleMemberToggle(person.id)}
                    >
                      <div className="flex items-center gap-2 flex-1">
                        <User className="h-4 w-4 text-green-600" />
                        <div className="flex-1">
                          <div className="font-medium">{getPersonDisplayName(person)}</div>
                          <div className="text-sm text-muted-foreground">
                            {getPersonDetails(person)}
                          </div>
                        </div>
                        <Check
                          className={cn(
                            "h-4 w-4",
                            memberIds.includes(person.id) ? "opacity-100" : "opacity-0"
                          )}
                        />
                      </div>
                    </CommandItem>
                  ))
                  )}
                </CommandGroup>
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>

        {/* Display selected members */}
        {selectedMembers.length > 0 && (
          <div className="flex flex-wrap gap-2 mt-2">
            {selectedMembers.map((member) => (
              <Badge
                key={member.id}
                variant="secondary"
                className="bg-green-100 text-green-700 cursor-pointer hover:bg-green-200"
                onClick={() => handleMemberToggle(member.id)}
              >
                {getPersonDisplayName(member)}
                <span className="ml-1 text-xs">×</span>
              </Badge>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
