'use client';

import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Calendar, Loader2, Save, TrendingUp } from 'lucide-react';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { InstallationService } from '../services/installation-service';
import { useAuth } from '@/components/providers/auth-provider';

// Schema de validation basé sur la vraie structure DB
const trackingSchema = z.object({
  global_progress: z.number().min(0).max(100),

  // KYA-SoP specific fields
  metalwork_progress: z.number().min(0).max(100).optional(),
  excavation_progress: z.number().min(0).max(100).optional(),
  pv_supports_progress: z.number().min(0).max(100).optional(),
  modules_wiring_progress: z.number().min(0).max(100).optional(),
  pv_inverter_cables_progress: z.number().min(0).max(100).optional(),
  inverters_wiring_progress: z.number().min(0).max(100).optional(),
  batteries_wiring_progress: z.number().min(0).max(100).optional(),
  ac_dc_boxes_progress: z.number().min(0).max(100).optional(),
  load_separation_progress: z.number().min(0).max(100).optional(),
  battery_inverter_connection_progress: z.number().min(0).max(100).optional(),
  grounding_progress: z.number().min(0).max(100).optional(),

  // Lampadaire specific fields
  pole_installation_progress: z.number().min(0).max(100).optional(),
  lamp_installation_progress: z.number().min(0).max(100).optional(),

  // Common fields
  hours_worked: z.number().min(0).optional(),
  weather_conditions: z.string().optional(),
  daily_comments: z.string().optional(),
  issues_encountered: z.string().optional(),
  next_actions: z.string().optional(),
});

type TrackingFormData = z.infer<typeof trackingSchema>;

interface TrackingModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  installationId: string;
  productType: 'KYA-SoP' | 'Lampadaire';
}

export function TrackingModal({
  open,
  onOpenChange,
  installationId,
  productType
}: TrackingModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { user } = useAuth();
  const [latestTracking, setLatestTracking] = useState<any>(null);

  const form = useForm<TrackingFormData>({
    resolver: zodResolver(trackingSchema),
    defaultValues: {
      global_progress: 0,
      metalwork_progress: 0,
      excavation_progress: 0,
      pv_supports_progress: 0,
      modules_wiring_progress: 0,
      pv_inverter_cables_progress: 0,
      inverters_wiring_progress: 0,
      batteries_wiring_progress: 0,
      ac_dc_boxes_progress: 0,
      load_separation_progress: 0,
      battery_inverter_connection_progress: 0,
      grounding_progress: 0,
      pole_installation_progress: 0,
      lamp_installation_progress: 0,
      hours_worked: 0,
      weather_conditions: '',
      daily_comments: '',
      issues_encountered: '',
      next_actions: '',
    },
  });

  // Fonction pour calculer la progression globale automatiquement
  const calculateGlobalProgress = React.useCallback(() => {
    const values = form.getValues();
    let progressFields: number[] = [];

    if (productType === 'KYA-SoP') {
      progressFields = [
        values.metalwork_progress || 0,
        values.excavation_progress || 0,
        values.pv_supports_progress || 0,
        values.modules_wiring_progress || 0,
        values.pv_inverter_cables_progress || 0,
        values.inverters_wiring_progress || 0,
        values.batteries_wiring_progress || 0,
        values.ac_dc_boxes_progress || 0,
        values.load_separation_progress || 0,
        values.battery_inverter_connection_progress || 0,
        values.grounding_progress || 0,
      ];
    } else if (productType === 'Lampadaire') {
      progressFields = [
        values.metalwork_progress || 0,
        values.excavation_progress || 0,
        values.pole_installation_progress || 0,
        values.lamp_installation_progress || 0,
      ];
    }

    const average = progressFields.length > 0
      ? Math.round(progressFields.reduce((sum, val) => sum + val, 0) / progressFields.length)
      : 0;

    return average;
  }, [form, productType]);

  // Calculer la progression globale en temps réel pour l'affichage
  const currentGlobalProgress = React.useMemo(() => {
    return calculateGlobalProgress();
  }, [calculateGlobalProgress, form.watch()]);

  // Charger les dernières données de suivi quand le modal s'ouvre
  useEffect(() => {
    if (open && installationId) {
      loadLatestTracking();
    }
  }, [open, installationId]);

  const loadLatestTracking = async () => {
    try {
      // D'abord essayer de récupérer les données d'aujourd'hui
      let tracking = await InstallationService.getTodayTracking(installationId);

      // Si pas de données aujourd'hui, récupérer les dernières données disponibles
      if (!tracking) {
        const trackingHistory = await InstallationService.getTrackingHistory(installationId);
        if (trackingHistory && trackingHistory.length > 0) {
          tracking = trackingHistory[0]; // Le plus récent
        }
      }

      if (tracking) {
        setLatestTracking(tracking);
        // Précharger les données dans le formulaire
        form.reset({
          global_progress: tracking.global_progress || 0,
          metalwork_progress: tracking.metalwork_progress || 0,
          excavation_progress: tracking.excavation_progress || 0,
          pv_supports_progress: tracking.pv_supports_progress || 0,
          modules_wiring_progress: tracking.modules_wiring_progress || 0,
          pv_inverter_cables_progress: tracking.pv_inverter_cables_progress || 0,
          inverters_wiring_progress: tracking.inverters_wiring_progress || 0,
          batteries_wiring_progress: tracking.batteries_wiring_progress || 0,
          ac_dc_boxes_progress: tracking.ac_dc_boxes_progress || 0,
          load_separation_progress: tracking.load_separation_progress || 0,
          battery_inverter_connection_progress: tracking.battery_inverter_connection_progress || 0,
          grounding_progress: tracking.grounding_progress || 0,
          pole_installation_progress: tracking.pole_installation_progress || 0,
          lamp_installation_progress: tracking.lamp_installation_progress || 0,
          hours_worked: tracking.hours_worked || 0,
          weather_conditions: tracking.weather_conditions || '',
          daily_comments: tracking.daily_comments || '',
          issues_encountered: tracking.issues_encountered || '',
          next_actions: tracking.next_actions || '',
        });

        // Recalculer la progression globale après le préchargement
        setTimeout(() => {
          const newGlobal = calculateGlobalProgress();
          form.setValue('global_progress', newGlobal, { shouldValidate: false });
        }, 100);
      }
    } catch (error) {
      console.error('Error loading latest tracking:', error);
    }
  };

  const onSubmit = async (data: TrackingFormData) => {
    setIsSubmitting(true);

    try {
      // Vérifier que l'utilisateur est authentifié
      if (!user) {
        throw new Error('Utilisateur non authentifié');
      }

      // Calculer et ajouter la progression globale aux données
      const dataWithGlobalProgress = {
        ...data,
        global_progress: calculateGlobalProgress()
      };

      // Utiliser la logique de versioning : même jour = update, jour différent = nouveau
      await InstallationService.updateDailyTracking(installationId, dataWithGlobalProgress, user.id);

      // Fermer le modal
      onOpenChange(false);
      form.reset();

      // TODO: Rafraîchir les données de la liste
      console.log('Tracking saved successfully');
    } catch (error) {
      console.error('Error saving tracking:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-kya-primary">
            <Calendar className="h-5 w-5" />
            Suivi Journalier
          </DialogTitle>
          <DialogDescription>
            Mettez à jour les pourcentages de progression pour aujourd'hui
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Progression globale calculée automatiquement */}
            <Card className="border-kya-primary/20 bg-kya-primary/5">
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <TrendingUp className="h-5 w-5 text-kya-primary" />
                  Progression Globale (Calculée Automatiquement)
                </CardTitle>
                <CardDescription>
                  Moyenne automatique des pourcentages d'avancement par étape
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Progression Globale</span>
                    <span className="text-2xl font-bold text-kya-primary">
                      {currentGlobalProgress}%
                    </span>
                  </div>
                  <div className="w-full bg-muted rounded-full h-3">
                    <div
                      className="gradient-kya-primary h-3 rounded-full transition-all duration-300"
                      style={{ width: `${currentGlobalProgress}%` }}
                    ></div>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Ce taux est calculé automatiquement comme la moyenne de tous les pourcentages ci-dessous
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Progressions détaillées selon le type de produit */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Progression Détaillée</CardTitle>
                <CardDescription>
                  Pourcentages par étape de {productType === 'KYA-SoP' ? 'KYA-SoP' : 'Lampadaire'}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {productType === 'KYA-SoP' ? (
                  // Champs spécifiques pour KYA-SoP
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="metalwork_progress"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Métallerie (%)</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              min="0"
                              max="100"
                              placeholder="0"
                              {...field}
                              onChange={(e) => field.onChange(Number(e.target.value) || 0)}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="excavation_progress"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Terrassement (%)</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              min="0"
                              max="100"
                              placeholder="0"
                              {...field}
                              onChange={(e) => field.onChange(Number(e.target.value) || 0)}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="pv_supports_progress"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Supports PV (%)</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              min="0"
                              max="100"
                              placeholder="0"
                              {...field}
                              onChange={(e) => field.onChange(Number(e.target.value) || 0)}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="modules_wiring_progress"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Câblage Modules (%)</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              min="0"
                              max="100"
                              placeholder="0"
                              {...field}
                              onChange={(e) => field.onChange(Number(e.target.value) || 0)}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="pv_inverter_cables_progress"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Câbles PV-Onduleur (%)</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              min="0"
                              max="100"
                              placeholder="0"
                              {...field}
                              onChange={(e) => field.onChange(Number(e.target.value) || 0)}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="inverters_wiring_progress"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Câblage Onduleurs (%)</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              min="0"
                              max="100"
                              placeholder="0"
                              {...field}
                              onChange={(e) => field.onChange(Number(e.target.value) || 0)}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="batteries_wiring_progress"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Câblage Batteries (%)</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              min="0"
                              max="100"
                              placeholder="0"
                              {...field}
                              onChange={(e) => field.onChange(Number(e.target.value) || 0)}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="ac_dc_boxes_progress"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Coffrets AC/DC (%)</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              min="0"
                              max="100"
                              placeholder="0"
                              {...field}
                              onChange={(e) => field.onChange(Number(e.target.value) || 0)}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="load_separation_progress"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Séparation Charges (%)</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              min="0"
                              max="100"
                              placeholder="0"
                              {...field}
                              onChange={(e) => field.onChange(Number(e.target.value) || 0)}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="battery_inverter_connection_progress"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Connexion Batterie-Onduleur (%)</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              min="0"
                              max="100"
                              placeholder="0"
                              {...field}
                              onChange={(e) => field.onChange(Number(e.target.value) || 0)}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="grounding_progress"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Mise à la Terre (%)</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              min="0"
                              max="100"
                              placeholder="0"
                              {...field}
                              onChange={(e) => field.onChange(Number(e.target.value) || 0)}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                ) : (
                  // Champs spécifiques pour Lampadaire
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="metalwork_progress"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Métallerie (%)</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              min="0"
                              max="100"
                              placeholder="0"
                              {...field}
                              onChange={(e) => field.onChange(Number(e.target.value) || 0)}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="excavation_progress"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Terrassement (%)</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              min="0"
                              max="100"
                              placeholder="0"
                              {...field}
                              onChange={(e) => field.onChange(Number(e.target.value) || 0)}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="pole_installation_progress"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Installation Mât (%)</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              min="0"
                              max="100"
                              placeholder="0"
                              {...field}
                              onChange={(e) => field.onChange(Number(e.target.value) || 0)}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="lamp_installation_progress"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Installation Luminaire (%)</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              min="0"
                              max="100"
                              placeholder="0"
                              {...field}
                              onChange={(e) => field.onChange(Number(e.target.value) || 0)}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Informations complémentaires */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Informations Complémentaires</CardTitle>
                <CardDescription>
                  Heures travaillées, conditions météo et observations
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="hours_worked"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Heures Travaillées</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            min="0"
                            step="0.5"
                            placeholder="8"
                            {...field}
                            onChange={(e) => field.onChange(Number(e.target.value) || 0)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="weather_conditions"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Conditions Météo</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Ensoleillé, nuageux, pluvieux..."
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="daily_comments"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Commentaires du Jour</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Observations générales sur l'avancement..."
                          className="min-h-[80px]"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="issues_encountered"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Problèmes Rencontrés</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Difficultés, retards, problèmes techniques..."
                          className="min-h-[80px]"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="next_actions"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Actions Prévues</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Prochaines étapes, matériel nécessaire..."
                          className="min-h-[80px]"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            {/* Boutons d'action */}
            <div className="flex justify-end gap-4 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isSubmitting}
              >
                Annuler
              </Button>
              
              <Button
                type="submit"
                disabled={isSubmitting}
                className="gradient-kya-primary text-white hover:opacity-90"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Enregistrement...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Enregistrer
                  </>
                )}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
