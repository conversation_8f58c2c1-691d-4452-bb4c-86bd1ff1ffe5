# Concept de Domaines Métier - KYA Dashboards

## Vue d'ensemble

Ce document définit le concept général de modélisation des domaines métier dans KYA Dashboards. Il sert de référence pour l'implémentation de tous les domaines (installations, maintenance, commercial, projets, etc.).

## Architecture Conceptuelle

### 1. Pattern Fondamental : Table Principale + Table de Suivi

Chaque domaine métier suit le même pattern architectural :

```
[DOMAINE] (Table principale)
    ↓
[DOMAINE]_TRACKING (Table de suivi journalier)
```

**Exemple :**
- `installations` → `installations_tracking`
- `maintenance` → `maintenance_tracking`
- `commercial_offers` → `commercial_offers_tracking`

### 2. Séparation des Responsabilités

#### Table Principale (Master Data)
- **Rôle** : Données de référence stables
- **Contenu** : Informations descriptives, relations, métadonnées
- **Fréquence de mise à jour** : Occasionnelle (création, modifications structurelles)
- **Exemples** : Nom du projet, client, produit, équipe responsable

#### Table de Suivi (Tracking Data)
- **Rôle** : Données opérationnelles journalières
- **Contenu** : États, métriques, commentaires, décisions
- **Fréquence de mise à jour** : Quotidienne
- **Exemples** : Statut d'avancement, heures travaillées, problèmes rencontrés

### 3. Versioning Automatique

**Règle de versioning :**
- **Même jour** : Mise à jour de l'enregistrement existant
- **Jour différent** : Création d'un nouvel enregistrement

**Implémentation :**
```sql
-- Logique de versioning côté application
IF EXISTS (SELECT 1 FROM [domain]_tracking 
           WHERE [domain]_id = ? AND DATE(created_at) = CURRENT_DATE)
THEN
    UPDATE [domain]_tracking SET ... WHERE [domain]_id = ? AND DATE(created_at) = CURRENT_DATE
ELSE
    INSERT INTO [domain]_tracking (...)
END IF
```

## Structure des Données

### 1. Tables Principales

#### Champs Communs Obligatoires
```sql
CREATE TABLE [domain] (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,           -- Obligatoire pour contexte
    
    -- Relations avec données partagées
    client_id UUID REFERENCES clients(id),
    project_id UUID REFERENCES projects(id),
    product_id UUID REFERENCES products(id),
    
    -- Équipe flexible
    team_members UUID[] NOT NULL,        -- Array de person_id
    team_leader_id UUID REFERENCES persons(id),
    
    -- Métadonnées
    comments TEXT,                       -- Obligatoire pour traçabilité
    metadata JSONB DEFAULT '{}',
    
    -- Audit
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES auth_users(id),
    updated_by UUID REFERENCES auth_users(id)
);
```

#### Champs Spécifiques par Domaine
Chaque domaine ajoute ses champs métier spécifiques :
- **Installations** : `installation_type`, `location`, `equipment_specs`
- **Maintenance** : `maintenance_type`, `installation_id`, `priority`
- **Commercial** : `offer_type`, `amount`, `validity_date`

### 2. Tables de Suivi

#### Champs Communs Obligatoires
```sql
CREATE TABLE [domain]_tracking (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    [domain]_id UUID NOT NULL REFERENCES [domain](id) ON DELETE CASCADE,
    
    -- Statut et progression
    status VARCHAR(50) NOT NULL,
    progress_percentage INTEGER DEFAULT 0 CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
    
    -- Temps et effort
    hours_worked DECIMAL(5,2),
    estimated_hours_remaining DECIMAL(5,2),
    
    -- Commentaires et décisions
    daily_notes TEXT,
    issues_encountered TEXT,
    decisions_made TEXT,
    next_actions TEXT,
    
    -- Audit
    tracking_date DATE NOT NULL DEFAULT CURRENT_DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES auth_users(id),
    updated_by UUID REFERENCES auth_users(id),
    
    -- Contrainte d'unicité par jour
    UNIQUE([domain]_id, tracking_date)
);
```

#### Champs Conditionnels par Produit
Tous les champs spécifiques sont **optionnels** dans la table, la logique conditionnelle est gérée côté application :

```sql
-- Champs optionnels pour tous les produits possibles
installation_phase VARCHAR(50),          -- Pour produit "Installation"
maintenance_urgency VARCHAR(20),         -- Pour produit "Maintenance"  
commercial_probability INTEGER,          -- Pour produit "Commercial"
-- ... autres champs selon besoins
```

### 3. Tables de Données Partagées

#### Clients
```sql
CREATE TABLE clients (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    type VARCHAR(50) NOT NULL CHECK (type IN ('HOUSEHOLD', 'INSTITUTION')),
    contact_info JSONB DEFAULT '{}',
    address TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### Projets
```sql
CREATE TABLE projects (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    client_id UUID REFERENCES clients(id),
    start_date DATE,
    end_date DATE,
    budget DECIMAL(12,2),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### Produits
```sql
CREATE TABLE products (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    category VARCHAR(100) NOT NULL,
    field_config JSONB DEFAULT '{}',     -- Configuration des champs conditionnels
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## Gestion des Équipes

### 1. Modèle Flexible

**Principe :** Pas d'entité "équipe" rigide, mais des groupes flexibles de personnes.

```sql
-- Dans chaque table principale
team_members UUID[] NOT NULL,           -- Array de person_id
team_leader_id UUID REFERENCES persons(id)
```

### 2. Avantages
- **Flexibilité** : Personnes peuvent être dans plusieurs équipes
- **Simplicité** : Pas de gestion complexe d'entités d'équipe
- **Évolutivité** : Facile d'ajouter/retirer des membres

### 3. Requêtes Typiques
```sql
-- Récupérer les membres d'une équipe
SELECT p.* FROM persons p 
WHERE p.id = ANY(SELECT unnest(team_members) FROM installations WHERE id = ?);

-- Projets d'une personne
SELECT i.* FROM installations i 
WHERE ? = ANY(i.team_members);
```

## Interface Utilisateur

### 1. Architecture à 5 Tabs par Équipe

Chaque domaine métier utilise la même structure d'interface :

1. **Aperçu Global** : KPIs, graphiques, vue d'ensemble
2. **Suivi Journalier** : Formulaires de saisie, liste des éléments actifs
3. **Alertes & Décisions** : Notifications, éléments nécessitant attention
4. **Responsables & Équipes** : Gestion des membres, planning
5. **Historique & Performance** : Métriques, analyses, tendances

### 2. Navigation Contextuelle

```
Sidebar 1: Sections principales
├── Dashboard
├── Saisie de Données
│   ├── Installations
│   ├── Maintenance  
│   ├── Commercial
│   └── Projets
└── Administration

Sidebar 2: Navigation par équipe
├── Direction Technique
│   ├── Équipe Installation
│   ├── Équipe Maintenance
│   └── Équipe Projets
└── Direction Commerciale
    └── Équipe Commerciale
```

### 3. Filtres Globaux Intelligents

**Persistance par utilisateur :**
- Sauvegarde automatique des filtres
- Restauration à la reconnexion
- Vues personnalisées

**Adaptation contextuelle :**
- Filtres pertinents selon le domaine
- Masquage des options non applicables
- Suggestions intelligentes

## Saisie de Données

### 1. Formulaires Adaptatifs

**Configuration par produit :**
```json
{
  "product_id": "uuid-installation",
  "required_fields": ["installation_phase", "equipment_status"],
  "optional_fields": ["technical_notes", "client_feedback"],
  "conditional_fields": {
    "installation_phase": {
      "PREPARATION": ["site_survey_completed"],
      "INSTALLATION": ["equipment_installed", "tests_completed"],
      "FINALIZATION": ["client_training_done", "documentation_delivered"]
    }
  }
}
```

**Rendu dynamique :**
- Affichage conditionnel des champs selon le produit sélectionné
- Validation adaptée aux règles métier
- Aide contextuelle par champ

### 2. Workflow de Saisie

1. **Sélection du contexte** : Domaine → Projet → Produit
2. **Formulaire adaptatif** : Champs pertinents selon le produit
3. **Validation** : Règles métier + contraintes techniques
4. **Sauvegarde** : Versioning automatique selon la date
5. **Confirmation** : Feedback utilisateur + actions suivantes

### 3. Gestion des Versions

**Interface utilisateur :**
- Indicateur "Déjà saisi aujourd'hui" 
- Bouton "Modifier la saisie du jour"
- Historique des versions précédentes
- Comparaison entre versions

## Patterns d'Implémentation

### 1. Services Métier

```typescript
// Pattern générique pour tous les domaines
export abstract class BaseDomainService<T, U> {
  abstract createRecord(data: T): Promise<T>;
  abstract updateRecord(id: string, data: Partial<T>): Promise<T>;
  abstract createTracking(domainId: string, data: U): Promise<U>;
  abstract updateTodayTracking(domainId: string, data: Partial<U>): Promise<U>;
  abstract getTrackingHistory(domainId: string): Promise<U[]>;
}

// Implémentation spécifique
export class InstallationService extends BaseDomainService<Installation, InstallationTracking> {
  // Logique spécifique aux installations
}
```

### 2. Composants Réutilisables

```typescript
// Composant générique de suivi
<DomainTrackingForm<InstallationTracking>
  domainId={installationId}
  productConfig={productConfig}
  onSubmit={handleSubmit}
  existingData={todayTracking}
/>

// Composant générique de dashboard
<DomainDashboard
  domainType="installations"
  entityId={teamId}
  tabs={['overview', 'tracking', 'alerts', 'team', 'history']}
/>
```

### 3. Hooks Personnalisés

```typescript
// Hook générique pour le suivi journalier
export function useDomainTracking<T>(domainId: string, domainType: string) {
  const todayTracking = useQuery(['tracking', domainType, domainId, 'today'], 
    () => fetchTodayTracking(domainType, domainId));
  
  const createOrUpdate = useMutation(
    (data: Partial<T>) => upsertTodayTracking(domainType, domainId, data)
  );
  
  return { todayTracking, createOrUpdate };
}
```

## Évolutivité

### 1. Ajout de Nouveaux Domaines

Pour ajouter un nouveau domaine métier :

1. **Base de données** : Créer les tables `[domain]` et `[domain]_tracking`
2. **Types** : Définir les interfaces TypeScript
3. **Services** : Étendre `BaseDomainService`
4. **Composants** : Utiliser les composants génériques
5. **Navigation** : Ajouter les routes et menus

### 2. Extension des Fonctionnalités

- **Nouveaux champs** : Ajout en optionnel + configuration produit
- **Nouveaux statuts** : Extension des enums + migration
- **Nouvelles règles** : Configuration JSON + validation côté client

### 3. Intégrations Futures

- **API externes** : Services d'intégration par domaine
- **Notifications** : Système d'alertes configurable
- **Rapports** : Générateur de rapports par domaine
- **Mobile** : API REST pour applications mobiles

---

**Ce concept garantit une architecture cohérente, évolutive et maintenable pour tous les domaines métier de KYA Dashboards.**