'use client';

import { useState, useTransition } from 'react';
import { 
  createPersonAction,
  updatePersonAction,
  deletePersonAction,
  assignPersonToEntityAction,
  updatePersonEntityAssignmentAction,
  removePersonFromEntityAction,
  getPersonByIdAction
} from '../actions';
import type {
  PersonFormValues,
  UpdatePersonFormValues,
  PersonEntityFormValues,
  UpdatePersonEntityFormValues
} from '../schemas';
import type { PersonWithEntities } from '../types';

interface UsePersonMutationsReturn {
  // States
  isCreating: boolean;
  isUpdating: boolean;
  isDeleting: boolean;
  isAssigning: boolean;
  isUpdatingAssignment: boolean;
  isRemovingAssignment: boolean;
  isFetching: boolean;
  
  // Actions
  createPerson: (data: PersonFormValues) => Promise<{ success: boolean; data?: PersonWithEntities; error?: string }>;
  updatePerson: (id: string, data: UpdatePersonFormValues) => Promise<{ success: boolean; data?: PersonWithEntities; error?: string }>;
  deletePerson: (id: string) => Promise<{ success: boolean; error?: string }>;
  fetchPerson: (id: string) => Promise<{ success: boolean; data?: PersonWithEntities; error?: string }>;
  assignToEntity: (personId: string, data: PersonEntityFormValues) => Promise<{ success: boolean; error?: string }>;
  updateAssignment: (assignmentId: string, data: UpdatePersonEntityFormValues) => Promise<{ success: boolean; error?: string }>;
  removeFromEntity: (assignmentId: string) => Promise<{ success: boolean; error?: string }>;
}

export function usePersonMutations(): UsePersonMutationsReturn {
  const [isCreating, startCreateTransition] = useTransition();
  const [isUpdating, startUpdateTransition] = useTransition();
  const [isDeleting, startDeleteTransition] = useTransition();
  const [isAssigning, startAssignTransition] = useTransition();
  const [isUpdatingAssignment, startUpdateAssignmentTransition] = useTransition();
  const [isRemovingAssignment, startRemoveAssignmentTransition] = useTransition();
  const [isFetching, setIsFetching] = useState(false);

  const createPerson = async (data: PersonFormValues) => {
    return new Promise<{ success: boolean; data?: PersonWithEntities; error?: string }>((resolve) => {
      startCreateTransition(async () => {
        try {
          const result = await createPersonAction(data);
          
          if (result.error) {
            resolve({ success: false, error: result.error });
          } else {
            resolve({ success: true, data: result.data });
          }
        } catch (error) {
          resolve({ success: false, error: 'Une erreur inattendue s\'est produite.' });
        }
      });
    });
  };

  const updatePerson = async (id: string, data: UpdatePersonFormValues) => {
    return new Promise<{ success: boolean; data?: PersonWithEntities; error?: string }>((resolve) => {
      startUpdateTransition(async () => {
        try {
          const result = await updatePersonAction(id, data);
          
          if (result.error) {
            resolve({ success: false, error: result.error });
          } else {
            resolve({ success: true, data: result.data });
          }
        } catch (error) {
          resolve({ success: false, error: 'Une erreur inattendue s\'est produite.' });
        }
      });
    });
  };

  const deletePerson = async (id: string) => {
    return new Promise<{ success: boolean; error?: string }>((resolve) => {
      startDeleteTransition(async () => {
        try {
          const result = await deletePersonAction(id);
          
          if (result.error) {
            resolve({ success: false, error: result.error });
          } else {
            resolve({ success: true });
          }
        } catch (error) {
          resolve({ success: false, error: 'Une erreur inattendue s\'est produite.' });
        }
      });
    });
  };

  const fetchPerson = async (id: string) => {
    setIsFetching(true);
    try {
      const result = await getPersonByIdAction(id);
      
      if (result.error) {
        return { success: false, error: result.error };
      } else {
        return { success: true, data: result.data };
      }
    } catch (error) {
      return { success: false, error: 'Une erreur inattendue s\'est produite.' };
    } finally {
      setIsFetching(false);
    }
  };

  const assignToEntity = async (personId: string, data: PersonEntityFormValues) => {
    return new Promise<{ success: boolean; error?: string }>((resolve) => {
      startAssignTransition(async () => {
        try {
          const result = await assignPersonToEntityAction(personId, data);
          
          if (result.error) {
            resolve({ success: false, error: result.error });
          } else {
            resolve({ success: true });
          }
        } catch (error) {
          resolve({ success: false, error: 'Une erreur inattendue s\'est produite.' });
        }
      });
    });
  };

  const updateAssignment = async (assignmentId: string, data: UpdatePersonEntityFormValues) => {
    return new Promise<{ success: boolean; error?: string }>((resolve) => {
      startUpdateAssignmentTransition(async () => {
        try {
          const result = await updatePersonEntityAssignmentAction(assignmentId, data);
          
          if (result.error) {
            resolve({ success: false, error: result.error });
          } else {
            resolve({ success: true });
          }
        } catch (error) {
          resolve({ success: false, error: 'Une erreur inattendue s\'est produite.' });
        }
      });
    });
  };

  const removeFromEntity = async (assignmentId: string) => {
    return new Promise<{ success: boolean; error?: string }>((resolve) => {
      startRemoveAssignmentTransition(async () => {
        try {
          const result = await removePersonFromEntityAction(assignmentId);
          
          if (result.error) {
            resolve({ success: false, error: result.error });
          } else {
            resolve({ success: true });
          }
        } catch (error) {
          resolve({ success: false, error: 'Une erreur inattendue s\'est produite.' });
        }
      });
    });
  };

  return {
    // States
    isCreating,
    isUpdating,
    isDeleting,
    isAssigning,
    isUpdatingAssignment,
    isRemovingAssignment,
    isFetching,
    
    // Actions
    createPerson,
    updatePerson,
    deletePerson,
    fetchPerson,
    assignToEntity,
    updateAssignment,
    removeFromEntity,
  };
}
