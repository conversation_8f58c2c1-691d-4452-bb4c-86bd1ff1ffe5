// Re-export installation types for global use
export type {
  Installation,
  InstallationTracking,
  InstallationStatus,
  ProductType,
  ReceptionStatus,
  FunctioningState,
  CommissioningTestStatus,
  CreateInstallationData,
  UpdateInstallationData,
  InstallationTrackingData,
  InstallationStats,
  InstallationKPIs,
  InstallationFilters,
  InstallationSearchParams,
  ProgressStep,
  Client,
  Project,
} from '@/features/installations/types';

export { KYA_SOP_STEPS } from '@/features/installations/types';
