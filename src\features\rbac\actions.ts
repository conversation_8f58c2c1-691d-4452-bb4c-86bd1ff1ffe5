'use server';

import { RBACService } from './services/rbac-service';
import { requireRBACPermission } from '@/utils/rbac/guards';
import type { CreateRoleData, UpdateRoleData } from '@/types/rbac';

import { AuthService } from '@/features/auth/services/auth-service';

export async function getRolesAction() {
  try {
    await requireRBACPermission('admin.rbac.manage');
    return { data: await RBACService.getRoles() };
  } catch (error: any) {
    return { error: error.message };
  }
}

export async function getPermissionMatrixAction() {
  try {
    await requireRBACPermission('admin.rbac.manage');
    return { data: await RBACService.getPermissionMatrix() };
  } catch (error: any) {
    return { error: error.message };
  }
}

export async function createRoleAction(data: CreateRoleData) {
  try {
    const { userId } = await requireRBACPermission('admin.rbac.manage');
    const newRole = await RBACService.createRole(data, userId);
    return { data: newRole };
  } catch (error: any) {
    return { error: error.message };
  }
}

export async function updateRoleAction(id: string, data: UpdateRoleData) {
  try {
    const { userId } = await requireRBACPermission('admin.rbac.manage');
    const updatedRole = await RBACService.updateRole(id, data, userId);
    return { data: updatedRole };
  } catch (error: any) {
    return { error: error.message };
  }
}

export async function deleteRoleAction(id: string) {
  try {
    const { userId } = await requireRBACPermission('admin.rbac.manage');
    await RBACService.deleteRole(id, userId);
    return { success: true };
  } catch (error: any) {
    return { error: error.message };
  }
}

export async function updateRolePermissionsAction(roleId: string, permissionIds: string[]) {
  try {
    const { userId } = await requireRBACPermission('admin.rbac.manage');
    await RBACService.updateRolePermissions(roleId, permissionIds, userId);
    return { success: true };
  } catch (error: any) {
    return { error: error.message };
  }
}

export async function getRolesForSignup() {
  try {
    // Get all roles that are not system roles and can be assigned during signup
    const roles = await RBACService.getRoles();
    const signupRoles = roles
      // .filter(role => !role.isSystemRole) // Exclude system roles
      .map(role => ({
        id: role.id,
        displayName: role.name
      }));
    return { data: signupRoles };
  } catch (error: any) {
    return { error: error.message };
  }
}
