'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { CalendarIcon, MapPin, Users, Zap, <PERSON>ting<PERSON>, <PERSON>, Loader2 } from 'lucide-react';

import { ClientSelector } from './client-selector';
import { ProjectSelector } from './project-selector';
import { TeamSelector } from './team-selector';
import { createInstallationAction, generateInstallationNumberAction } from '../actions';
import type { CreateInstallationData, ProductType } from '../types';

// Form validation schema
const installationSchema = z.object({
  name: z.string().min(1, 'Le nom est requis').max(255, 'Le nom est trop long'),
  client_id: z.string().min(1, 'Le client est requis'),
  project_id: z.string().optional(),
  product_type: z.enum(['KYA-SoP', 'Lampadaire'], {
    required_error: 'Le type de produit est requis',
  }),
  installation_number: z.string().min(1, 'Le numéro d\'installation est requis'),
  site_location: z.string().min(1, 'L\'emplacement est requis'),
  gps_coordinates: z.string().optional(),
  
  // Conditional fields for KYA-SoP
  peak_power: z.string().optional(),
  inverter_specs: z.string().optional(),
  battery_capacity: z.string().optional(),
  equipment_description: z.string().optional(),
  
  // Team
  team_leader_id: z.string().optional(),
  team_members: z.array(z.string()).default([]),
  
  // Planning
  planned_start_date: z.string().optional(),
  planned_end_date: z.string().optional(),
  
  comments: z.string().optional(),
});

type InstallationFormData = z.infer<typeof installationSchema>;

interface InstallationCreateFormProps {
  entityId?: string;
  entityName?: string;
}

export function InstallationCreateForm({ entityId, entityName }: InstallationCreateFormProps) {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedClient, setSelectedClient] = useState<string>('');
  const [isGeneratingNumber, setIsGeneratingNumber] = useState(false);

  const form = useForm<InstallationFormData>({
    resolver: zodResolver(installationSchema),
    defaultValues: {
      name: '',
      client_id: '',
      project_id: '',
      product_type: 'KYA-SoP',
      installation_number: '',
      site_location: '',
      gps_coordinates: '',
      peak_power: '',
      inverter_specs: '',
      battery_capacity: '',
      equipment_description: '',
      team_leader_id: '',
      team_members: [],
      planned_start_date: '',
      planned_end_date: '',
      comments: '',
    },
  });

  const productType = form.watch('product_type');
  const isKyaSoP = productType === 'KYA-SoP';

  // Generate installation number on component mount
  useEffect(() => {
    const generateNumber = async () => {
      if (!form.getValues('installation_number')) {
        setIsGeneratingNumber(true);
        try {
          const result = await generateInstallationNumberAction();
          if (result.success && result.data) {
            form.setValue('installation_number', result.data);
          }
        } catch (error) {
          console.error('Error generating installation number:', error);
        } finally {
          setIsGeneratingNumber(false);
        }
      }
    };

    generateNumber();
  }, [form]);

  const onSubmit = async (data: InstallationFormData) => {
    setIsSubmitting(true);
    
    try {
      // Convert dates to proper format
      const formattedData: CreateInstallationData = {
        ...data,
        planned_start_date: data.planned_start_date ? new Date(data.planned_start_date) : undefined,
        planned_end_date: data.planned_end_date ? new Date(data.planned_end_date) : undefined,
        // Remove conditional fields if not KYA-SoP
        ...(isKyaSoP ? {} : {
          peak_power: undefined,
          inverter_specs: undefined,
          battery_capacity: undefined,
        }),
      };

      const result = await createInstallationAction(formattedData);
      
      if (result.success && result.data) {
        toast.success('Installation créée avec succès !');
        router.push('/saisie/installations');
      } else {
        toast.error(result.error || 'Erreur lors de la création');
      }
    } catch (error) {
      console.error('Error creating installation:', error);
      toast.error('Une erreur inattendue s\'est produite');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        {/* Section 1: Informations Générales */}
        <div className="space-y-6">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg">
              <Zap className="h-4 w-4 text-white" />
            </div>
            <div>
              <h3 className="text-lg font-semibold">Informations Générales</h3>
              <p className="text-sm text-muted-foreground">
                Détails de base de l'installation
              </p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nom de l'Installation *</FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="Ex: Installation Hôpital Dakar"
                      className="bg-white"
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="installation_number"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Numéro d'Installation *</FormLabel>
                  <FormControl>
                    <Input
                      placeholder={isGeneratingNumber ? "Génération..." : "Ex: INST-2024-001"}
                      className="bg-gray-50"
                      readOnly
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Numéro unique généré automatiquement
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="product_type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Type de Produit *</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger className="bg-white">
                        <SelectValue placeholder="Sélectionner le type" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="KYA-SoP">
                        <div className="flex items-center gap-2">
                          <Badge variant="secondary" className="bg-blue-100 text-blue-700">
                            KYA-SoP
                          </Badge>
                          <span>Système solaire photovoltaïque</span>
                        </div>
                      </SelectItem>
                      <SelectItem value="Lampadaire">
                        <div className="flex items-center gap-2">
                          <Badge variant="secondary" className="bg-amber-100 text-amber-700">
                            Lampadaire
                          </Badge>
                          <span>Éclairage public solaire</span>
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>

        <Separator />

        {/* Section 2: Client et Projet */}
        <div className="space-y-6">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-gradient-to-r from-green-500 to-emerald-600 rounded-lg">
              <Users className="h-4 w-4 text-white" />
            </div>
            <div>
              <h3 className="text-lg font-semibold">Client et Projet</h3>
              <p className="text-sm text-muted-foreground">
                Sélection ou création du client et du projet
              </p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              control={form.control}
              name="client_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Client *</FormLabel>
                  <FormControl>
                    <ClientSelector
                      value={field.value}
                      onValueChange={(value) => {
                        field.onChange(value);
                        setSelectedClient(value);
                        // Reset project when client changes
                        form.setValue('project_id', '');
                      }}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="project_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Projet (Optionnel)</FormLabel>
                  <FormControl>
                    <ProjectSelector
                      value={field.value || ''}
                      onValueChange={field.onChange}
                      clientId={selectedClient}
                    />
                  </FormControl>
                  <FormDescription>
                    Laisser vide si pas de projet spécifique
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>

        <Separator />

        {/* Section 3: Localisation */}
        <div className="space-y-6">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-gradient-to-r from-orange-500 to-red-600 rounded-lg">
              <MapPin className="h-4 w-4 text-white" />
            </div>
            <div>
              <h3 className="text-lg font-semibold">Localisation</h3>
              <p className="text-sm text-muted-foreground">
                Emplacement et coordonnées du site
              </p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              control={form.control}
              name="site_location"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Emplacement du Site *</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="Adresse complète du site d'installation"
                      className="bg-white min-h-[80px]"
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="gps_coordinates"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Coordonnées GPS</FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="Ex: 14.6928, -17.4467"
                      className="bg-white"
                      {...field} 
                    />
                  </FormControl>
                  <FormDescription>
                    Format: latitude, longitude
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>

        <Separator />

        {/* Section 4: Spécifications Techniques (Conditionnelle) */}
        {isKyaSoP && (
          <div className="space-y-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg">
                <Settings className="h-4 w-4 text-white" />
              </div>
              <div>
                <h3 className="text-lg font-semibold">Spécifications Techniques</h3>
                <p className="text-sm text-muted-foreground">
                  Configuration spécifique pour KYA-SoP
                </p>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <FormField
                control={form.control}
                name="peak_power"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Puissance Crête</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Ex: 10kW"
                        className="bg-white"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="inverter_specs"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Spécifications Onduleur</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Ex: 2x 8kW"
                        className="bg-white"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="battery_capacity"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Capacité Batterie</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Ex: 2x 100Ah/51,2V"
                        className="bg-white"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="equipment_description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description de l'Équipement</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Description détaillée des équipements et composants"
                      className="bg-white min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        )}

        {!isKyaSoP && (
          <div className="space-y-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-gradient-to-r from-amber-500 to-orange-600 rounded-lg">
                <Settings className="h-4 w-4 text-white" />
              </div>
              <div>
                <h3 className="text-lg font-semibold">Description de l'Équipement</h3>
                <p className="text-sm text-muted-foreground">
                  Détails du lampadaire solaire
                </p>
              </div>
            </div>

            <FormField
              control={form.control}
              name="equipment_description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description de l'Équipement</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Description du lampadaire, puissance LED, autonomie, etc."
                      className="bg-white min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        )}

        <Separator />

        {/* Section 5: Équipe */}
        <div className="space-y-6">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-gradient-to-r from-teal-500 to-cyan-600 rounded-lg">
              <Users className="h-4 w-4 text-white" />
            </div>
            <div>
              <h3 className="text-lg font-semibold">Assignation de l'Équipe</h3>
              <p className="text-sm text-muted-foreground">
                Sélection du chef d'équipe et des membres
              </p>
            </div>
          </div>

          <TeamSelector
            leaderId={form.watch('team_leader_id')}
            memberIds={form.watch('team_members')}
            onLeaderChange={(value) => form.setValue('team_leader_id', value)}
            onMembersChange={(value) => form.setValue('team_members', value)}
            disabled={isSubmitting}
          />
        </div>

        <Separator />

        {/* Section 6: Planification */}
        <div className="space-y-6">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg">
              <CalendarIcon className="h-4 w-4 text-white" />
            </div>
            <div>
              <h3 className="text-lg font-semibold">Planification</h3>
              <p className="text-sm text-muted-foreground">
                Dates prévisionnelles de l'installation
              </p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              control={form.control}
              name="planned_start_date"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Date de Début Prévue</FormLabel>
                  <FormControl>
                    <Input
                      type="date"
                      className="bg-white"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="planned_end_date"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Date de Fin Prévue</FormLabel>
                  <FormControl>
                    <Input
                      type="date"
                      className="bg-white"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="comments"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Commentaires</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Remarques, contraintes particulières, notes importantes..."
                    className="bg-white min-h-[100px]"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="flex justify-end gap-4 pt-6">
          <Button 
            type="button" 
            variant="outline" 
            onClick={() => router.back()}
            disabled={isSubmitting}
          >
            Annuler
          </Button>
          <Button 
            type="submit" 
            disabled={isSubmitting}
            className="bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700"
          >
            {isSubmitting ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Création...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Créer l'Installation
              </>
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
}
