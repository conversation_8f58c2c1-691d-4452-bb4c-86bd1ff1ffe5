// Installation Team Management Tab - KYA Dashboards
'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Progress } from '@/components/ui/progress';
import { 
  Users, 
  User, 
  Calendar, 
  Star,
  MapPin,
  Clock,
  Phone,
  Mail,
  Award,
  TrendingUp
} from 'lucide-react';

import type { InstallationKPIs } from '../types';
import { ProductTypeBadge } from './installation-dashboard';

interface InstallationTeamManagementProps {
  kpis?: InstallationKPIs;
}

export function InstallationTeamManagement({ kpis }: InstallationTeamManagementProps) {
  if (!kpis) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <Card key={i}>
            <CardContent className="pt-6">
              <div className="animate-pulse space-y-2">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  const { team_performance, stats } = kpis;

  // TODO: Replace with real API call to fetch team members
  const teamMembers: any[] = [];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'AVAILABLE':
        return 'bg-green-100 text-green-800';
      case 'ON_SITE':
        return 'bg-blue-100 text-blue-800';
      case 'ON_LEAVE':
        return 'bg-yellow-100 text-yellow-800';
      case 'UNAVAILABLE':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Team Management</h2>
          <p className="text-muted-foreground">
            Manage installation teams and track performance
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm">
            <Calendar className="h-4 w-4 mr-2" />
            Planifier
          </Button>
          <Button size="sm">
            <Users className="h-4 w-4 mr-2" />
            Ajouter Membre
          </Button>
        </div>
      </div>

      {/* Statistiques d'Équipe */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Membres d'Équipe</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{teamMembers.length}</div>
            <p className="text-xs text-muted-foreground">
              {teamMembers.filter((m: any) => m.status === 'AVAILABLE' || m.status === 'ON_SITE').length} actifs
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Données Équipes</CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Non disponible</div>
            <p className="text-xs text-muted-foreground">
              Connecter aux vraies données
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Team Members List */}
      <Card>
        <CardHeader>
          <CardTitle>Installation Team Members</CardTitle>
          <CardDescription>
            Current team composition and individual performance
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-lg font-medium text-gray-600">Aucune donnée d'équipe disponible</p>
            <p className="text-sm text-muted-foreground">Les données des équipes seront chargées depuis la base de données</p>
          </div>
        </CardContent>
      </Card>




    </div>
  );
}
