import { useMemo } from 'react';
import { useInstallations } from './use-installations';
import { getInstallationProgress } from '../utils/tracking-utils';

export interface InstallationKpis {
  active_installations: number;
  average_progress: number;
  overdue_installations: number;
  total_installations: number;
  completion_rate: number;
}

export function useInstallationKpis(installations?: any[]): {
  kpis: InstallationKpis;
  isLoading: boolean;
  error: Error | null;
} {
  const { data: fetchedInstallations, isLoading, error } = useInstallations({}, !!installations);

  const data = installations || fetchedInstallations;

  const kpis = useMemo(() => {
    if (!data) {
      return {
        active_installations: 0,
        average_progress: 0,
        overdue_installations: 0,
        total_installations: 0,
        completion_rate: 0,
      };
    }

    const now = new Date();

    // Installations actives (en cours)
    const activeInstallations = data.filter(
      (inst: any) => inst.status === 'IN_PROGRESS'
    );

    // Progression moyenne des installations actives
    const totalProgress = activeInstallations.reduce(
      (sum, inst) => sum + getInstallationProgress(inst),
      0
    );
    const averageProgress = activeInstallations.length > 0 
      ? totalProgress / activeInstallations.length 
      : 0;



    // En retard (dépassent la date prévue)
    const overdueInstallations = data.filter((inst: any) => {
      if (inst.status === 'COMPLETED' || !inst.planned_end_date) return false;
      const plannedEnd = new Date(inst.planned_end_date);
      return now > plannedEnd;
    }).length;

    // Taux de completion
    const completedInstallations = data.filter(
      (inst: any) => inst.status === 'COMPLETED'
    ).length;
    const completionRate = data.length > 0
      ? (completedInstallations / data.length) * 100
      : 0;

    return {
      active_installations: activeInstallations.length,
      average_progress: Math.round(averageProgress),
      overdue_installations: overdueInstallations,
      total_installations: data.length,
      completion_rate: Math.round(completionRate),
    };
  }, [data]);

  return {
    kpis,
    isLoading,
    error,
  };
}
