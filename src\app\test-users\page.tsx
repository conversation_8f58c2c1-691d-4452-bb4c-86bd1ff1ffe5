import { createClient } from '@/utils/supabase/server';

export default async function TestUsersPage() {
  const supabase = await createClient();
  
  // Test de la requête utilisateurs
  let users = [];
  let error = null;
  
  try {
    const { data, error: queryError } = await supabase
      .from('auth_users')
      .select(`
        id,
        email,
        is_active,
        created_at,
        user_profiles!auth_user_id (
          id,
          display_name,
          avatar_url
        ),
        user_roles!user_id (
          id,
          is_active,
          granted_at,
          roles (
            id,
            name,
            display_name,
            level
          )
        )
      `)
      .eq('is_active', true)
      .order('created_at', { ascending: false })
      .limit(10);

    if (queryError) {
      error = queryError;
    } else {
      users = data || [];
    }
  } catch (e) {
    error = e;
  }

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-6">Test de la requête utilisateurs</h1>
      
      {error ? (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <strong>Erreur:</strong>
          <pre className="mt-2 text-sm">{JSON.stringify(error, null, 2)}</pre>
        </div>
      ) : (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
          <strong>Succès!</strong> {users.length} utilisateur(s) trouvé(s)
        </div>
      )}

      <div className="space-y-4">
        {users.map((user: any) => (
          <div key={user.id} className="border rounded p-4 bg-white shadow">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <h3 className="font-semibold">Informations de base</h3>
                <p><strong>ID:</strong> {user.id}</p>
                <p><strong>Email:</strong> {user.email}</p>
                <p><strong>Actif:</strong> {user.is_active ? 'Oui' : 'Non'}</p>
                <p><strong>Créé le:</strong> {new Date(user.created_at).toLocaleDateString()}</p>
              </div>
              
              <div>
                <h3 className="font-semibold">Profil</h3>
                {user.user_profiles ? (
                  <>
                    <p><strong>Nom d'affichage:</strong> {user.user_profiles.display_name || 'Non défini'}</p>
                    <p><strong>Avatar:</strong> {user.user_profiles.avatar_url ? 'Oui' : 'Non'}</p>
                  </>
                ) : (
                  <p className="text-gray-500">Aucun profil</p>
                )}
              </div>
            </div>
            
            <div className="mt-4">
              <h3 className="font-semibold">Rôles</h3>
              {user.user_roles && user.user_roles.length > 0 ? (
                <div className="flex flex-wrap gap-2 mt-2">
                  {user.user_roles
                    .filter((ur: any) => ur.is_active)
                    .map((ur: any) => (
                      <span 
                        key={ur.id} 
                        className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm"
                      >
                        {ur.roles?.display_name || ur.roles?.name}
                      </span>
                    ))}
                </div>
              ) : (
                <p className="text-gray-500">Aucun rôle actif</p>
              )}
            </div>
          </div>
        ))}
      </div>

      <div className="mt-8 p-4 bg-gray-100 rounded">
        <h3 className="font-semibold mb-2">Requête SQL utilisée:</h3>
        <pre className="text-sm bg-white p-2 rounded overflow-x-auto">
{`SELECT 
  id, email, is_active, created_at,
  user_profiles!auth_user_id (id, display_name, avatar_url),
  user_roles!user_id (
    id, is_active, granted_at,
    roles (id, name, display_name, level)
  )
FROM auth_users 
WHERE is_active = true 
ORDER BY created_at DESC 
LIMIT 10`}
        </pre>
      </div>
    </div>
  );
}
