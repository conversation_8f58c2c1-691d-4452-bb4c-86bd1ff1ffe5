'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Calendar,
  Search,
  Filter,
  Download,
  Eye,
  Edit,
  Clock,
  CheckCircle,
  AlertTriangle
} from 'lucide-react';
import { format, startOfDay } from 'date-fns';
import { fr } from 'date-fns/locale';
import { Installation } from '../../types';
import { getInstallationProgress, getLastUpdateDate, hasTrackingForDate, getProgressDetails } from '../../utils/tracking-utils';

interface SuiviTabProps {
  installations: Installation[];
}

export function SuiviTab({ installations }: SuiviTabProps) {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState('');
  const [dateFilter, setDateFilter] = useState(startOfDay(new Date()).toISOString().split('T')[0]);
  const [expandedRow, setExpandedRow] = useState<string | null>(null);

  const handleInstallationClick = (installation: Installation) => {
    // Toggle expanded row instead of navigation
    setExpandedRow(expandedRow === installation.id ? null : installation.id);
  };

  if (!installations) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-kya-primary"></div>
      </div>
    );
  }

  if (!installations) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">Aucune donnée disponible</p>
      </div>
    );
  }
  const filteredInstallations = installations.filter(installation => {
    const matchesSearch = installation.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         installation.client?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         installation.installation_number?.toLowerCase().includes(searchTerm.toLowerCase());

    // Utiliser la fonction utilitaire pour vérifier la date
    const matchesDate = !dateFilter || hasTrackingForDate(installation, dateFilter);

    return matchesSearch && matchesDate;
  });

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      'PLANNING': { label: 'Planification', variant: 'secondary' as const, icon: Calendar },
      'IN_PROGRESS': { label: 'En cours', variant: 'default' as const, icon: Clock },
      'COMPLETED': { label: 'Terminé', variant: 'outline' as const, icon: CheckCircle },
      'ON_HOLD': { label: 'En attente', variant: 'destructive' as const, icon: AlertTriangle },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || {
      label: status,
      variant: 'outline' as const,
      icon: Clock
    };

    const Icon = config.icon;

    return (
      <Badge variant={config.variant} className="flex items-center gap-1 capitalize">
        <Icon className="h-3 w-3" />
        {config.label.toLowerCase()}
      </Badge>
    );
  };

  const getProgressColor = (progress: number) => {
    if (progress >= 80) return 'bg-green-500';
    if (progress >= 50) return 'bg-blue-500';
    if (progress >= 25) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  return (
    <div className="space-y-4">
      {/* Filtres et Actions */}
      <div className="bg-card p-3 rounded-lg border">
        <div className="flex flex-col lg:flex-row gap-3 items-start lg:items-center justify-between">
          <div className="flex flex-col sm:flex-row gap-3 flex-1">
            <div className="relative flex-1 max-w-xs">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Rechercher..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 h-8 text-sm"
              />
            </div>

            <Input
              type="date"
              value={dateFilter}
              onChange={(e) => setDateFilter(e.target.value)}
              className="w-auto h-8 text-sm"
            />
          </div>

          <div className="flex gap-2">
            <Button variant="outline" size="sm" className="h-8 text-xs">
              <Filter className="h-3 w-3 mr-1" />
              Filtres
            </Button>
            <Button variant="outline" size="sm" className="h-8 text-xs">
              <Download className="h-3 w-3 mr-1" />
              Exporter
            </Button>
          </div>
        </div>
      </div>

      {/* Tableau des Installations */}
      <div className="border rounded-lg overflow-hidden bg-card">
        <div className="p-3 border-b bg-muted/10">
          <h3 className="font-medium text-sm">Suivi Quotidien des Installations ({filteredInstallations.length})</h3>
          <p className="text-xs text-muted-foreground">Tableau détaillé avec progression et statuts en temps réel</p>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead>
              <tr className="border-b bg-muted/20">
                <th className="text-left p-3 font-medium text-xs uppercase tracking-wide text-muted-foreground">
                  Installation
                </th>
                <th className="text-left p-3 font-medium text-xs uppercase tracking-wide text-muted-foreground">
                  Client
                </th>
                <th className="text-left p-3 font-medium text-xs uppercase tracking-wide text-muted-foreground">
                  Type
                </th>
                <th className="text-left p-3 font-medium text-xs uppercase tracking-wide text-muted-foreground">
                  Statut
                </th>
                <th className="text-left p-3 font-medium text-xs uppercase tracking-wide text-muted-foreground">
                  Progression
                </th>
                <th className="text-left p-3 font-medium text-xs uppercase tracking-wide text-muted-foreground">
                  Dernière MAJ
                </th>
                <th className="text-right p-3 font-medium text-xs uppercase tracking-wide text-muted-foreground">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody>
              {filteredInstallations.map((installation) => {
                // Utiliser les fonctions utilitaires pour obtenir les données
                const progress = getInstallationProgress(installation);
                const lastUpdate = getLastUpdateDate(installation);
                const progressDetails = getProgressDetails(installation);
                
                return (
                  <>
                    <tr
                      key={installation.id}
                      className="border-b hover:bg-muted/50 transition-colors cursor-pointer"
                      onClick={() => handleInstallationClick(installation)}
                    >
                      <td className="p-4">
                        <div>
                          <p className="font-medium text-foreground">
                            {installation.name}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            {installation.site_location}
                          </p>
                        </div>
                      </td>
                      <td className="p-4">
                        <p className="font-medium">
                          {installation.client?.name || 'Non défini'}
                        </p>
                      </td>
                      <td className="p-4">
                        <Badge variant="outline">
                          {installation.product_type}
                        </Badge>
                      </td>
                      <td className="p-4">
                        {getStatusBadge(installation.status)}
                      </td>
                      <td className="p-4">
                        <div className="flex items-center space-x-2">
                          <div className="flex-1 bg-muted rounded-full h-1.5 max-w-[80px]">
                            <div
                              className={`h-1.5 rounded-full transition-all duration-500 ${getProgressColor(progress)}`}
                              style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
                            ></div>
                          </div>
                          <span className="font-medium min-w-[35px] text-xs">
                            {Math.round(progress)}%
                          </span>
                        </div>
                      </td>
                      <td className="p-4">
                        <p className="text-muted-foreground text-xs">
                          {lastUpdate ? format(new Date(lastUpdate), 'dd/MM/yyyy', { locale: fr }) : 'Jamais'}
                        </p>
                      </td>
                      <td className="p-4 text-right">
                        <div className="flex items-center justify-end space-x-1">
                          <Button variant="ghost" size="sm" className="h-7 w-7 p-0">
                            <Edit className="h-3 w-3" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                    {expandedRow === installation.id && (
                      <tr>
                        <td colSpan={7} className="p-0">
                          <div className="bg-muted/30 border-t">
                            <div className="p-4 space-y-4">
                              {/* Installation Overview */}
                              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div className="space-y-2">
                                  <h4 className="font-semibold text-sm text-foreground">Informations Générales</h4>
                                  <div className="space-y-1 text-xs">
                                    <div><span className="font-medium">Numéro:</span> {installation.installation_number || 'Non défini'}</div>
                                    <div><span className="font-medium">Localisation:</span> {installation.site_location}</div>
                                    <div><span className="font-medium">Coordonnées GPS:</span> {installation.gps_coordinates || 'Non définies'}</div>
                                    <div><span className="font-medium">Chef d'équipe:</span> {installation.team_leader ? `${installation.team_leader.firstName} ${installation.team_leader.lastName}` : 'Non assigné'}</div>
                                  </div>
                                </div>

                                <div className="space-y-2">
                                  <h4 className="font-semibold text-sm text-foreground">Planification</h4>
                                  <div className="space-y-1 text-xs">
                                    <div><span className="font-medium">Début prévu:</span> {installation.planned_start_date ? format(new Date(installation.planned_start_date), 'dd/MM/yyyy', { locale: fr }) : 'Non défini'}</div>
                                    <div><span className="font-medium">Fin prévue:</span> {installation.planned_end_date ? format(new Date(installation.planned_end_date), 'dd/MM/yyyy', { locale: fr }) : 'Non définie'}</div>
                                    <div><span className="font-medium">Début réel:</span> {installation.actual_start_date ? format(new Date(installation.actual_start_date), 'dd/MM/yyyy', { locale: fr }) : 'Non commencé'}</div>
                                    <div><span className="font-medium">Durée totale:</span> {installation.total_duration_days ? `${installation.total_duration_days} jours` : 'Non définie'}</div>
                                  </div>
                                </div>

                                <div className="space-y-2">
                                  <h4 className="font-semibold text-sm text-foreground">Spécifications Techniques</h4>
                                  <div className="space-y-1 text-xs">
                                    {installation.product_type === 'KYA-SoP' && (
                                      <>
                                        <div><span className="font-medium">Puissance crête:</span> {installation.peak_power || 'Non définie'}</div>
                                        <div><span className="font-medium">Onduleurs:</span> {installation.inverter_specs || 'Non définis'}</div>
                                        <div><span className="font-medium">Batteries:</span> {installation.battery_capacity || 'Non définies'}</div>
                                      </>
                                    )}
                                    {installation.equipment_description && (
                                      <div><span className="font-medium">Description:</span> {installation.equipment_description}</div>
                                    )}
                                  </div>
                                </div>
                              </div>

                              {/* Progress Details */}
                              <div className="border-t pt-4">
                                <h4 className="font-semibold text-sm text-foreground mb-3">Détails de Progression</h4>
                                <div className="grid grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3">
                                  {progressDetails && installation.product_type === 'KYA-SoP' ? (
                                    <>
                                      <div className="flex items-center justify-between p-2 bg-background rounded border">
                                        <span className="text-xs font-medium">Travaux de chaudronnerie</span>
                                        <span className="text-xs font-bold">{(progressDetails as any).metalwork_progress || 0}%</span>
                                      </div>
                                      <div className="flex items-center justify-between p-2 bg-background rounded border">
                                        <span className="text-xs font-medium">Fouilles</span>
                                        <span className="text-xs font-bold">{(progressDetails as any).excavation_progress || 0}%</span>
                                      </div>
                                      <div className="flex items-center justify-between p-2 bg-background rounded border">
                                        <span className="text-xs font-medium">Pose des supports PV</span>
                                        <span className="text-xs font-bold">{(progressDetails as any).pv_supports_progress || 0}%</span>
                                      </div>
                                      <div className="flex items-center justify-between p-2 bg-background rounded border">
                                        <span className="text-xs font-medium">Pose des modules et câblage</span>
                                        <span className="text-xs font-bold">{(progressDetails as any).modules_wiring_progress || 0}%</span>
                                      </div>
                                      <div className="flex items-center justify-between p-2 bg-background rounded border">
                                        <span className="text-xs font-medium">Câbles PV-Onduleurs</span>
                                        <span className="text-xs font-bold">{(progressDetails as any).pv_inverter_cables_progress || 0}%</span>
                                      </div>
                                      <div className="flex items-center justify-between p-2 bg-background rounded border">
                                        <span className="text-xs font-medium">Pose des onduleurs</span>
                                        <span className="text-xs font-bold">{(progressDetails as any).inverters_wiring_progress || 0}%</span>
                                      </div>
                                      <div className="flex items-center justify-between p-2 bg-background rounded border">
                                        <span className="text-xs font-medium">Pose des batteries</span>
                                        <span className="text-xs font-bold">{(progressDetails as any).batteries_wiring_progress || 0}%</span>
                                      </div>
                                      <div className="flex items-center justify-between p-2 bg-background rounded border">
                                        <span className="text-xs font-medium">Coffrets AC-DC</span>
                                        <span className="text-xs font-bold">{(progressDetails as any).ac_dc_boxes_progress || 0}%</span>
                                      </div>
                                      <div className="flex items-center justify-between p-2 bg-background rounded border">
                                        <span className="text-xs font-medium">Séparation des charges</span>
                                        <span className="text-xs font-bold">{(progressDetails as any).load_separation_progress || 0}%</span>
                                      </div>
                                      <div className="flex items-center justify-between p-2 bg-background rounded border">
                                        <span className="text-xs font-medium">Raccordement batterie-onduleurs</span>
                                        <span className="text-xs font-bold">{(progressDetails as any).battery_inverter_connection_progress || 0}%</span>
                                      </div>
                                      <div className="flex items-center justify-between p-2 bg-background rounded border">
                                        <span className="text-xs font-medium">Mise à la terre</span>
                                        <span className="text-xs font-bold">{(progressDetails as any).grounding_progress || 0}%</span>
                                      </div>
                                    </>
                                  ) : progressDetails && installation.product_type === 'Lampadaire' ? (
                                    <>
                                      <div className="flex items-center justify-between p-2 bg-background rounded border">
                                        <span className="text-xs font-medium">Travaux de chaudronnerie</span>
                                        <span className="text-xs font-bold">{(progressDetails as any).metalwork_progress || 0}%</span>
                                      </div>
                                      <div className="flex items-center justify-between p-2 bg-background rounded border">
                                        <span className="text-xs font-medium">Fouilles</span>
                                        <span className="text-xs font-bold">{(progressDetails as any).excavation_progress || 0}%</span>
                                      </div>
                                      <div className="flex items-center justify-between p-2 bg-background rounded border">
                                        <span className="text-xs font-medium">Pose des Mâts</span>
                                        <span className="text-xs font-bold">{(progressDetails as any).pole_installation_progress || 0}%</span>
                                      </div>
                                      <div className="flex items-center justify-between p-2 bg-background rounded border">
                                        <span className="text-xs font-medium">Pose des lampadaires</span>
                                        <span className="text-xs font-bold">{(progressDetails as any).lamp_installation_progress || 0}%</span>
                                      </div>
                                    </>
                                  ) : (
                                    <div className="col-span-full text-center text-muted-foreground text-sm py-4">
                                      Aucune donnée de suivi disponible
                                    </div>
                                  )}
                                </div>
                              </div>
                            </div>
                          </div>
                        </td>
                      </tr>
                    )}
                  </>
                );
              })}
            </tbody>
          </table>
          
          {filteredInstallations.length === 0 && (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-4">
                <Search className="h-12 w-12 mx-auto" />
              </div>
              <p className="text-lg font-medium text-gray-600">
                Aucune installation trouvée
              </p>
              <p className="text-sm text-muted-foreground">
                Essayez de modifier vos critères de recherche
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
