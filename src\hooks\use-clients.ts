// Client Hooks - KYA Dashboards
// Shared hooks for client management across all domains

'use client';

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { ClientService } from '@/services/client-service';
import type { Client, CreateClientData, UpdateClientData } from '@/types/shared';

// Query Keys
export const clientKeys = {
  all: ['clients'] as const,
  lists: () => [...clientKeys.all, 'list'] as const,
  list: (filters: Record<string, any>) => [...clientKeys.lists(), { filters }] as const,
  details: () => [...clientKeys.all, 'detail'] as const,
  detail: (id: string) => [...clientKeys.details(), id] as const,
  search: (query: string) => [...clientKeys.all, 'search', query] as const,
};

// Get all clients
export function useClients() {
  return useQuery({
    queryKey: clientKeys.lists(),
    queryFn: ClientService.getAllClients,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Get client by ID
export function useClient(id: string) {
  return useQuery({
    queryKey: clientKeys.detail(id),
    queryFn: () => ClientService.getClientById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Search clients
export function useSearchClients(query: string) {
  return useQuery({
    queryKey: clientKeys.search(query),
    queryFn: () => ClientService.searchClients(query),
    enabled: query.length >= 2,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

// Create client mutation
export function useCreateClient() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: CreateClientData) => ClientService.createClient(data),
    onSuccess: (newClient) => {
      // Invalidate and refetch clients list
      queryClient.invalidateQueries({ queryKey: clientKeys.lists() });
      
      // Add the new client to the cache
      queryClient.setQueryData(clientKeys.detail(newClient.id), newClient);
      
      toast.success('Client créé avec succès');
    },
    onError: (error) => {
      console.error('Error creating client:', error);
      toast.error('Erreur lors de la création du client');
    },
  });
}

// Update client mutation
export function useUpdateClient() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateClientData }) => 
      ClientService.updateClient(id, data),
    onSuccess: (updatedClient) => {
      // Invalidate and refetch clients list
      queryClient.invalidateQueries({ queryKey: clientKeys.lists() });
      
      // Update the client in the cache
      queryClient.setQueryData(clientKeys.detail(updatedClient.id), updatedClient);
      
      toast.success('Client mis à jour avec succès');
    },
    onError: (error) => {
      console.error('Error updating client:', error);
      toast.error('Erreur lors de la mise à jour du client');
    },
  });
}

// Delete client mutation
export function useDeleteClient() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => ClientService.deleteClient(id),
    onSuccess: (_, deletedId) => {
      // Invalidate and refetch clients list
      queryClient.invalidateQueries({ queryKey: clientKeys.lists() });
      
      // Remove the client from the cache
      queryClient.removeQueries({ queryKey: clientKeys.detail(deletedId) });
      
      toast.success('Client supprimé avec succès');
    },
    onError: (error) => {
      console.error('Error deleting client:', error);
      toast.error('Erreur lors de la suppression du client');
    },
  });
}
