# Schéma de Base de Données - KYA Dashboards

## Vue d'ensemble

Ce document décrit le schéma de base de données complet pour l'application KYA Dashboards, conçu pour gérer la structure organisationnelle, l'authentification, les permissions et les données de suivi.

## Architecture des Données

### 1. Structure Organisationnelle

#### Table `entities`
Modélise la hiérarchie organisationnelle avec un pattern d'auto-référence.

```sql
CREATE TABLE entities (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    type VARCHAR(50) NOT NULL CHECK (type IN ('DIRECTION', 'EQUIPE', 'SOUS_EQUIPE', 'DEPARTEMENT')),
    parent_id UUID REFERENCES entities(id) ON DELETE CASCADE,
    manager_id UUID REFERENCES persons(id) ON DELETE SET NULL,
    description TEXT,
    code VARCHAR(50) UNIQUE, -- <PERSON> métier pour identification
    is_active BOOLEAN DEFAULT true,
    metadata JSONB DEFAULT '{}', -- Données spécifiques par type
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### Table `persons`
Informations sur les personnes de l'organisation.

```sql
CREATE TABLE persons (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    email VARCHAR(255) UNIQUE,
    phone VARCHAR(20),
    employee_id VARCHAR(50) UNIQUE, -- Matricule employé
    position VARCHAR(100), -- Poste occupé
    hire_date DATE,
    is_active BOOLEAN DEFAULT true,
    metadata JSONB DEFAULT '{}', -- Informations RH supplémentaires
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### Table `entities_persons`
Relations entre personnes et entités organisationnelles.

```sql
CREATE TABLE entities_persons (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    entity_id UUID NOT NULL REFERENCES entities(id) ON DELETE CASCADE,
    person_id UUID NOT NULL REFERENCES persons(id) ON DELETE CASCADE,
    role_in_entity VARCHAR(100), -- Rôle spécifique dans cette entité
    start_date DATE DEFAULT CURRENT_DATE,
    end_date DATE,
    is_primary BOOLEAN DEFAULT false, -- Entité principale de la personne
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(entity_id, person_id, start_date)
);
```

### 2. Authentification et Profils Utilisateurs

#### Table `auth_users`
Comptes utilisateurs liés à Supabase Auth.

```sql
CREATE TABLE auth_users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    supabase_id UUID UNIQUE NOT NULL, -- Référence vers auth.users de Supabase
    person_id UUID REFERENCES persons(id) ON DELETE SET NULL,
    user_profile_id UUID REFERENCES user_profiles(id) ON DELETE SET NULL,
    is_active BOOLEAN DEFAULT true,
    email_verified BOOLEAN DEFAULT false,
    last_login_at TIMESTAMP WITH TIME ZONE,
    login_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### Table `user_profiles`
Profils utilisateurs personnalisables.

```sql
CREATE TABLE user_profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    display_name VARCHAR(255),
    avatar_url TEXT,
    bio TEXT,
    preferences JSONB DEFAULT '{}', -- Thème, langue, timezone, etc.
    notification_settings JSONB DEFAULT '{}', -- Préférences de notifications
    dashboard_config JSONB DEFAULT '{}', -- Configuration des dashboards
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 3. Système RBAC (Role-Based Access Control)

#### Table `roles`
Définition des rôles du système.

```sql
CREATE TABLE roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) UNIQUE NOT NULL,
    display_name VARCHAR(255) NOT NULL,
    description TEXT,
    level INTEGER NOT NULL DEFAULT 0, -- Niveau hiérarchique du rôle
    is_system_role BOOLEAN DEFAULT false, -- Rôle système non modifiable
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### Table `permissions`
Permissions granulaires du système.

```sql
CREATE TABLE permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) UNIQUE NOT NULL,
    display_name VARCHAR(255) NOT NULL,
    description TEXT,
    resource VARCHAR(100) NOT NULL, -- Ressource concernée (users, entities, data, etc.)
    action VARCHAR(50) NOT NULL, -- Action (create, read, update, delete, manage)
    is_system_permission BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### Table `roles_permissions`
Association entre rôles et permissions.

```sql
CREATE TABLE roles_permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    role_id UUID NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
    permission_id UUID NOT NULL REFERENCES permissions(id) ON DELETE CASCADE,
    granted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    granted_by UUID REFERENCES auth_users(id),
    UNIQUE(role_id, permission_id)
);
```

#### Table `user_roles`
Attribution de rôles aux utilisateurs par entité.

```sql
CREATE TABLE user_roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth_users(id) ON DELETE CASCADE,
    role_id UUID NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
    entity_id UUID REFERENCES entities(id) ON DELETE CASCADE, -- Rôle limité à cette entité
    granted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    granted_by UUID REFERENCES auth_users(id),
    expires_at TIMESTAMP WITH TIME ZONE, -- Rôle temporaire
    is_active BOOLEAN DEFAULT true,
    UNIQUE(user_id, role_id, entity_id)
);
```

### 4. Audit et Traçabilité

#### Table `audit_logs`
Journal d'audit des actions sensibles.

```sql
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth_users(id),
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(100) NOT NULL,
    resource_id UUID,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## Index et Contraintes

### Index pour les performances

```sql
-- Index sur les hiérarchies
CREATE INDEX idx_entities_parent_id ON entities(parent_id);
CREATE INDEX idx_entities_type ON entities(type);
CREATE INDEX idx_entities_manager_id ON entities(manager_id);

-- Index sur les relations personnes-entités
CREATE INDEX idx_entities_persons_entity_id ON entities_persons(entity_id);
CREATE INDEX idx_entities_persons_person_id ON entities_persons(person_id);
CREATE INDEX idx_entities_persons_active ON entities_persons(entity_id, person_id) WHERE end_date IS NULL;

-- Index sur l'authentification
CREATE INDEX idx_auth_users_supabase_id ON auth_users(supabase_id);
CREATE INDEX idx_auth_users_person_id ON auth_users(person_id);
CREATE INDEX idx_auth_users_email ON auth_users(email);

-- Index sur les rôles et permissions
CREATE INDEX idx_user_roles_user_id ON user_roles(user_id);
CREATE INDEX idx_user_roles_entity_id ON user_roles(entity_id);
CREATE INDEX idx_user_roles_active ON user_roles(user_id, entity_id) WHERE is_active = true;

-- Index sur l'audit
CREATE INDEX idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_resource ON audit_logs(resource_type, resource_id);
CREATE INDEX idx_audit_logs_created_at ON audit_logs(created_at);
```

### Contraintes de validation

```sql
-- Validation des emails
ALTER TABLE persons ADD CONSTRAINT check_email_format 
    CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$');

ALTER TABLE auth_users ADD CONSTRAINT check_email_format 
    CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$');

-- Validation des dates
ALTER TABLE entities_persons ADD CONSTRAINT check_date_range 
    CHECK (end_date IS NULL OR end_date >= start_date);

ALTER TABLE user_roles ADD CONSTRAINT check_expiry_date 
    CHECK (expires_at IS NULL OR expires_at > granted_at);
```


## Fonctions et Triggers

### Fonction de mise à jour automatique des timestamps

```sql
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';
```

### Triggers pour les timestamps

```sql
CREATE TRIGGER update_entities_updated_at BEFORE UPDATE ON entities
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_persons_updated_at BEFORE UPDATE ON persons
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_auth_users_updated_at BEFORE UPDATE ON auth_users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_profiles_updated_at BEFORE UPDATE ON user_profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

### Fonction pour vérifier les permissions

```sql
CREATE OR REPLACE FUNCTION check_user_permission(
    p_user_id UUID,
    p_permission_name VARCHAR,
    p_entity_id UUID DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
    has_permission BOOLEAN := FALSE;
BEGIN
    SELECT EXISTS(
        SELECT 1
        FROM user_roles ur
        JOIN roles_permissions rp ON ur.role_id = rp.role_id
        JOIN permissions p ON rp.permission_id = p.id
        WHERE ur.user_id = p_user_id
        AND p.name = p_permission_name
        AND ur.is_active = true
        AND (p_entity_id IS NULL OR ur.entity_id = p_entity_id OR ur.entity_id IS NULL)
        AND (ur.expires_at IS NULL OR ur.expires_at > NOW())
    ) INTO has_permission;
    
    RETURN has_permission;
END;
$$ LANGUAGE plpgsql;
```

## Migration et Maintenance

### Script de création complet

Le schéma complet peut être déployé via Supabase CLI ou directement dans l'interface Supabase. L'ordre de création recommandé :

1. Tables de base (persons, entities, user_profiles)
2. Tables d'authentification (auth_users)
3. Tables RBAC (roles, permissions, roles_permissions, user_roles)
4. Tables de relations (entities_persons)
5. Tables d'audit (audit_logs)
6. Index et contraintes
7. Fonctions et triggers

### Données de base

Les données de seed (rôles système, permissions de base) seront définies dans un fichier séparé `seed.sql` pour faciliter la maintenance et les déploiements.

### Sauvegarde et restauration

```sql
-- Sauvegarde des données critiques
pg_dump --data-only --table=entities --table=persons --table=auth_users kya_dashboards > backup_critical.sql

-- Restauration
psql kya_dashboards < backup_critical.sql
```

---

**Note** : Ce schéma est conçu pour être évolutif et peut être étendu selon les besoins spécifiques de chaque déploiement. La sécurité sera gérée au niveau applicatif via le système RBAC.