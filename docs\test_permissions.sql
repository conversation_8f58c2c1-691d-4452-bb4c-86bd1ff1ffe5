-- Test Script: Verify Permission System
-- This script helps verify that the permission system is working correctly

-- 1. Show all roles and their permission counts
SELECT 
    r.name as role_name,
    r.display_name,
    r.level,
    r.is_system_role,
    COUNT(p.id) as permission_count
FROM roles r
LEFT JOIN roles_permissions rp ON r.id = rp.role_id
LEFT JOIN permissions p ON rp.permission_id = p.id
GROUP BY r.id, r.name, r.display_name, r.level, r.is_system_role
ORDER BY r.level DESC;

-- 2. Show all permissions used in the code
SELECT 
    p.name,
    p.display_name,
    p.resource,
    p.action,
    COUNT(rp.role_id) as assigned_to_roles_count
FROM permissions p
LEFT JOIN roles_permissions rp ON p.id = rp.permission_id
WHERE p.name IN (
    'admin.access',
    'admin.users.read',
    'admin.users.manage', 
    'admin.rbac.manage',
    'admin.entities.manage',
    'dashboard.view.all',
    'dashboard.view.team',
    'data.entry',
    'data.validate'
)
GROUP BY p.id, p.name, p.display_name, p.resource, p.action
ORDER BY p.name;

-- 3. Verify SUPER_ADMIN has all critical permissions
SELECT 
    'SUPER_ADMIN Permission Check' as test_name,
    p.name as permission_name,
    CASE 
        WHEN rp.id IS NOT NULL THEN '✓ Assigned'
        ELSE '✗ Missing'
    END as status
FROM permissions p
LEFT JOIN roles_permissions rp ON p.id = rp.permission_id 
    AND rp.role_id = (SELECT id FROM roles WHERE name = 'SUPER_ADMIN')
WHERE p.name IN (
    'admin.access',
    'admin.users.read',
    'admin.users.manage', 
    'admin.rbac.manage',
    'admin.entities.manage'
)
ORDER BY p.name;

-- 4. Show users with SUPER_ADMIN role
SELECT 
    au.email,
    au.id as user_id,
    ur.granted_at,
    ur.is_active
FROM auth_users au
JOIN user_roles ur ON au.id = ur.user_id
JOIN roles r ON ur.role_id = r.id
WHERE r.name = 'SUPER_ADMIN'
AND ur.is_active = true;

-- 5. Test permission check function for a specific user (replace USER_ID)
-- Uncomment and replace 'YOUR_USER_ID_HERE' with actual user ID
/*
SELECT 
    'Permission Test for User' as test_name,
    check_user_permission('YOUR_USER_ID_HERE', 'admin.access') as admin_access,
    check_user_permission('YOUR_USER_ID_HERE', 'admin.users.read') as admin_users_read,
    check_user_permission('YOUR_USER_ID_HERE', 'admin.users.manage') as admin_users_manage,
    check_user_permission('YOUR_USER_ID_HERE', 'admin.rbac.manage') as admin_rbac_manage,
    check_user_permission('YOUR_USER_ID_HERE', 'admin.entities.manage') as admin_entities_manage;
*/

-- 6. Show detailed permission matrix
SELECT 
    r.name as role_name,
    r.level,
    p.name as permission_name,
    p.display_name as permission_display_name,
    p.resource,
    p.action
FROM roles_permissions rp
JOIN roles r ON rp.role_id = r.id
JOIN permissions p ON rp.permission_id = p.id
ORDER BY r.level DESC, r.name, p.name;
