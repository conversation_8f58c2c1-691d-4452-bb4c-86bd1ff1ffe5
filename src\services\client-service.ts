// Client Service - KYA Dashboards
// Shared service for client management across all domains

import { createClient as createSupabaseClient } from '@/utils/supabase/client';
import type { Client, CreateClientData, UpdateClientData } from '@/types/shared';

export class ClientService {
  
  static async getAllClients(): Promise<Client[]> {
    const supabase = createSupabaseClient();
    
    const { data, error } = await supabase
      .from('clients')
      .select('*')
      .eq('is_active', true)
      .order('name');
    
    if (error) throw error;
    return data || [];
  }
  
  static async getClientById(id: string): Promise<Client | null> {
    const supabase = createSupabaseClient();
    
    const { data, error } = await supabase
      .from('clients')
      .select('*')
      .eq('id', id)
      .single();
    
    if (error) {
      if (error.code === 'PGRST116') return null; // Not found
      throw error;
    }
    
    return data;
  }
  
  static async createClient(data: CreateClientData): Promise<Client> {
    const supabase = createSupabaseClient();
    
    const { data: client, error } = await supabase
      .from('clients')
      .insert({
        ...data,
        is_active: true,
      })
      .select()
      .single();
    
    if (error) throw error;
    return client;
  }
  
  static async updateClient(id: string, data: UpdateClientData): Promise<Client> {
    const supabase = createSupabaseClient();
    
    const { data: client, error } = await supabase
      .from('clients')
      .update({
        ...data,
        updated_at: new Date().toISOString(),
      })
      .eq('id', id)
      .select()
      .single();
    
    if (error) throw error;
    return client;
  }
  
  static async deleteClient(id: string): Promise<void> {
    const supabase = createSupabaseClient();
    
    // Soft delete by setting is_active to false
    const { error } = await supabase
      .from('clients')
      .update({ 
        is_active: false,
        updated_at: new Date().toISOString(),
      })
      .eq('id', id);
    
    if (error) throw error;
  }
  
  static async searchClients(query: string): Promise<Client[]> {
    const supabase = createSupabaseClient();
    
    const { data, error } = await supabase
      .from('clients')
      .select('*')
      .eq('is_active', true)
      .or(`name.ilike.%${query}%,address.ilike.%${query}%`)
      .order('name')
      .limit(20);
    
    if (error) throw error;
    return data || [];
  }
}

// Convenience functions for direct use
export const createClient = ClientService.createClient;
export const updateClient = ClientService.updateClient;
export const deleteClient = ClientService.deleteClient;
export const getClientById = ClientService.getClientById;
export const getAllClients = ClientService.getAllClients;
export const searchClients = ClientService.searchClients;
