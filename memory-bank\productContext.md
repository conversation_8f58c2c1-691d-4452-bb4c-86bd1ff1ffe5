# Project: kya-dashboards

## Overview
- **Project Name**: kya-dashboards
- **Project Type**: Enterprise Dashboard Application with Double Sidebar Navigation
- **Technology Stack**: Next.js 15.4.3, React 19.1.0, TypeScript, TailwindCSS, Supabase, Shadcn/ui
- **Created Date**: 2025-07-23
- **Architecture Finalized**: 2025-07-23

## Project Scope
A comprehensive dashboard application designed to replace manual Excel-based daily work tracking across multiple departments. Features a sophisticated double sidebar navigation system for hierarchical data exploration and automated statistics generation for directors.

## Key Features
- **Double Sidebar Navigation**: Primary sidebar (Dashboard, Data Entry, Settings) + Secondary sidebar (Directions → Teams)
- **Role-Based Dashboards**: Adaptive interfaces for Directors, Managers, and Team members
- **Contextual Tabs**: Rich tabs per team (Overview, Daily Tracking, Alerts, Managers, History, Performance)
- **Visual Filter System**: Filter indicators with visual marking of affected elements
- **Daily Data Versioning**: Intelligent versioning system for daily tracking data
- **RBAC System**: Complete role-based access control with granular permissions

## Architecture Components
- **Authentication**: Supa<PERSON> Auth with AuthProvider context
- **Database**: PostgreSQL via Supabase (no RLS, application-level security)
- **UI Framework**: Shadcn/ui components with TailwindCSS
- **State Management**: React Context + TanStack Query
- **Server Logic**: Next.js Server Actions (no API routes)
- **Features Structure**: Autonomous features with services, components, hooks, types, utils

## Organization Structure
```
Enterprise
├── Direction Technique
│   ├── Équipe Installation
│   ├── Équipe Maintenance
│   └── Équipe Support
├── Direction Commerciale
│   ├── Équipe Ventes
│   └── Équipe Marketing
└── Direction Services Supports
    ├── Équipe RH
    └── Équipe Finance
```

## Data Model (Business Example)
```
projects → installations → installations_suivi (with versioning)
```
- **Conditional Versioning**: Same day = update, different day = new version
- **Audit Trail**: Complete tracking of modifications with reasons
- **English Naming**: All database elements will use English names in implementation

## User Interface Design
### Double Sidebar System
- **Primary Sidebar**: Main navigation sections
- **Secondary Sidebar**: Contextual navigation (directions → teams)
- **Main Content**: Dashboard with contextual tabs and visual filters
- **Breadcrumb**: Navigation context preservation

### Dashboard Tabs (Example: Installation Team)
1. **Aperçu global du projet** - Project overview with key metrics
2. **Suivi journalier** - Daily tracking forms and progress
3. **Alertes et décisions urgentes** - Critical alerts and decisions
4. **Responsables & équipes** - Team management and assignments
5. **Historique & performance** - Historical data and performance analytics

## Development Phases
### Phase 1: Critical Infrastructure (5-6 days)
- Supabase configuration and types
- Authentication system with AuthProvider
- RBAC system implementation
- Base services and utilities

### Phase 2: Dashboard Structure (4-5 days)
- Double sidebar navigation system
- Entity management (directions, teams)
- Main layout and routing
- Shadcn/ui components setup

### Phase 3-4: Administration & Dashboards (9-11 days)
- User management interface
- Dashboard components with tabs
- Visual filter system
- Statistics and charts integration

### Phase 5+: Team-Specific Features (Iterative)
- Business-specific data models
- Team dashboards and data entry forms
- Specialized workflows per team

## Technical Standards
- **TypeScript**: Strict mode with comprehensive typing
- **Server Actions**: All backend logic via Next.js Server Actions
- **Feature Architecture**: Complete autonomy per feature
- **Service Layer**: Business logic separated from actions
- **RBAC Guards**: Permission checks at action level
- **Responsive Design**: Mobile-first approach with TailwindCSS

## Success Metrics
- **User Adoption**: Replace all Excel-based workflows
- **Time Savings**: Reduce manual reporting time by 80%
- **Data Accuracy**: Eliminate manual data entry errors
- **Director Efficiency**: Real-time dashboard access vs email reports
- **Team Productivity**: Streamlined data entry and validation

## Current Status
- ✅ Architecture completely defined and documented
- ✅ Database schema designed (without RLS)
- ✅ UI/UX specifications validated
- ✅ Development plan prioritized
- ✅ Technical stack confirmed
- 🚀 Ready to begin Phase 1 development

[2025-07-23 14:56] - Complete architecture documentation finalized, development ready to begin
