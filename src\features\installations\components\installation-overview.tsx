// Installation Overview Tab - KYA Dashboards
'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { 
  BarChart3, 
  TrendingUp, 
  MapPin, 
  Clock,
  Users,
  Zap,
  Eye
} from 'lucide-react';

import type { InstallationKPIs, ProductType } from '../types';
import { InstallationStatusBadge, ProductTypeBadge, ProgressBar } from './installation-dashboard';

interface InstallationOverviewProps {
  kpis?: InstallationKPIs;
}

export function InstallationOverview({ kpis }: InstallationOverviewProps) {
  if (!kpis) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {[...Array(6)].map((_, i) => (
          <Card key={i}>
            <CardContent className="pt-6">
              <div className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  const { stats, progress_by_type, team_performance, recent_completions, overdue_installations } = kpis;

  return (
    <div className="space-y-6">
      {/* Key Performance Indicators */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completion Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {stats.total_installations > 0 
                ? Math.round((stats.completed_installations / stats.total_installations) * 100)
                : 0}%
            </div>
            <p className="text-xs text-muted-foreground">
              {stats.completed_installations} of {stats.total_installations} completed
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">This Month</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.this_month_completed}</div>
            <p className="text-xs text-muted-foreground">
              Installations completed
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Team Performance</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {team_performance.length > 0 
                ? Math.round(team_performance.reduce((acc, team) => acc + team.average_progress, 0) / team_performance.length)
                : 0}%
            </div>
            <p className="text-xs text-muted-foreground">
              Average team progress
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Energy Capacity</CardTitle>
            <Zap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {/* This would be calculated from installation specs */}
              {Math.round(stats.completed_installations * 8.5)}kW
            </div>
            <p className="text-xs text-muted-foreground">
              Total installed capacity
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Progress by Product Type */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Progress by Product Type</CardTitle>
            <CardDescription>
              Average completion progress for each product category
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {Object.entries(progress_by_type).map(([type, progress]) => (
              <div key={type} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <ProductTypeBadge productType={type as ProductType} />
                  </div>
                  <span className="text-sm font-medium">{Math.round(progress)}%</span>
                </div>
                <ProgressBar value={progress} />
              </div>
            ))}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Team Performance</CardTitle>
            <CardDescription>
              Performance metrics by team leader
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {team_performance.slice(0, 5).map((team) => (
                <div key={team.team_leader_id} className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium">{team.team_leader_name}</p>
                    <p className="text-xs text-muted-foreground">
                      {team.installations_count} installations
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium">{Math.round(team.average_progress)}%</p>
                    <p className="text-xs text-muted-foreground">
                      {Math.round(team.completion_rate)}% completion
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Completions and Overdue Installations */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <TrendingUp className="h-4 w-4" />
              <span>Recent Completions</span>
            </CardTitle>
            <CardDescription>
              Latest completed installations
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recent_completions.slice(0, 5).map((installation) => (
                <div key={installation.id} className="flex items-center justify-between">
                  <div className="space-y-1">
                    <p className="text-sm font-medium">{installation.name}</p>
                    <div className="flex items-center space-x-2">
                      <ProductTypeBadge productType={installation.product_type} />
                      <span className="text-xs text-muted-foreground">
                        {installation.client?.name}
                      </span>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-xs text-muted-foreground">
                      {installation.actual_end_date 
                        ? new Date(installation.actual_end_date).toLocaleDateString()
                        : 'N/A'
                      }
                    </p>
                    <Button variant="ghost" size="sm">
                      <Eye className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              ))}
              {recent_completions.length === 0 && (
                <p className="text-sm text-muted-foreground text-center py-4">
                  No recent completions
                </p>
              )}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Clock className="h-4 w-4 text-red-500" />
              <span>Overdue Installations</span>
            </CardTitle>
            <CardDescription>
              Installations past their planned end date
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {overdue_installations.slice(0, 5).map((installation) => (
                <div key={installation.id} className="flex items-center justify-between">
                  <div className="space-y-1">
                    <p className="text-sm font-medium">{installation.name}</p>
                    <div className="flex items-center space-x-2">
                      <ProductTypeBadge productType={installation.product_type} />
                      <span className="text-xs text-muted-foreground">
                        {installation.client?.name}
                      </span>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-xs text-red-600 font-medium">
                      {installation.planned_end_date 
                        ? `${Math.ceil((new Date().getTime() - new Date(installation.planned_end_date).getTime()) / (1000 * 60 * 60 * 24))} days overdue`
                        : 'Overdue'
                      }
                    </p>
                    <Button variant="ghost" size="sm">
                      <Eye className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              ))}
              {overdue_installations.length === 0 && (
                <p className="text-sm text-muted-foreground text-center py-4">
                  No overdue installations
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Geographic Distribution (Placeholder for future map integration) */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <MapPin className="h-4 w-4" />
            <span>Geographic Distribution</span>
          </CardTitle>
          <CardDescription>
            Installation locations and coverage areas
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-64 bg-gray-100 rounded-lg flex items-center justify-center">
            <div className="text-center">
              <MapPin className="h-12 w-12 text-gray-400 mx-auto mb-2" />
              <p className="text-sm text-muted-foreground">
                Interactive map will be displayed here
              </p>
              <p className="text-xs text-muted-foreground">
                Showing {stats.total_installations} installation locations
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
