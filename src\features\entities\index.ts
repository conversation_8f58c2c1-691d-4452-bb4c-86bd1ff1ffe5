// Entities Feature Exports - KYA Dashboards

// Types
export type {
  Entity,
  EntityType,
  EntityPerson,
  EntityHierarchy,
  EntityTreeNode,
  CreateEntityData,
  UpdateEntityData,
  EntityFilters,
  EntityStats,
  BusinessModule,
  EntityFeatureMapping,
  EntityNavigationItem,
} from '@/types/entities';

// Services
export { EntityService } from './services/entity-service';
export { EntityFeatureMappingService } from './services/entity-feature-mapping';

// Utilities
export { DynamicRoutingService } from './utils/dynamic-routing';

// Actions
export {
  getHierarchyAction,
  getNavigationHierarchyAction,
  getEntityWithMappingAction,
  createEntityAction,
  updateEntityAction,
  deleteEntityAction,
} from './actions';

// Components
export { EntityTree } from './components/entity-tree';
export { EntityForm } from './components/entity-form';
export { ManageEntityDialog } from './components/manage-entity-dialog';
export { DeleteEntityAlert } from './components/delete-entity-alert';

// Schemas
export { entitySchema, ENTITY_TYPES } from './schemas';
export type { EntitySchema } from './schemas';
