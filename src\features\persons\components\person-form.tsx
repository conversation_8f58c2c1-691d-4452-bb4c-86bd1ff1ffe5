'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { CalendarIcon, Plus, X } from 'lucide-react';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import { cn } from '@/lib/utils';
import { usePersonMutations } from '../hooks/use-person-mutations';
import { PersonEntityManager } from './entity-assignment-manager';
import { EntitySelector, type SelectedEntity } from './entity-selector';
import { createPersonFormSchema, updatePersonFormSchema } from '../schemas';
import type { PersonFormValues, UpdatePersonFormValues } from '../schemas';
import type { PersonWithEntities } from '../types';

interface PersonFormProps {
  person?: PersonWithEntities;
  onClose: () => void;
  onSuccess: (person: PersonWithEntities) => void;
}

export function PersonForm({ person, onClose, onSuccess }: PersonFormProps) {
  const [showEntityManager, setShowEntityManager] = useState(false);
  const [showEntitySelector, setShowEntitySelector] = useState(false);
  const [selectedEntities, setSelectedEntities] = useState<SelectedEntity[]>([]);
  const isEditing = !!person;
  
  const { createPerson, updatePerson, assignToEntity, isCreating, isUpdating } = usePersonMutations();

  const form = useForm<PersonFormValues | UpdatePersonFormValues>({
    resolver: zodResolver(isEditing ? updatePersonFormSchema : createPersonFormSchema),
    defaultValues: isEditing ? {
      firstName: person.firstName,
      lastName: person.lastName,
      email: person.email || '',
      phone: person.phone || '',
      employeeId: person.employeeId || '',
      position: person.position || '',
      hireDate: person.hireDate ? new Date(person.hireDate) : undefined,
      isActive: person.isActive,
      metadata: person.metadata || {},
    } : {
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      employeeId: '',
      position: '',
      hireDate: undefined,
      metadata: {},
      entities: [],
    },
  });

  const onSubmit = async (values: PersonFormValues | UpdatePersonFormValues) => {
    try {
      let result;

      if (isEditing && person) {
        result = await updatePerson(person.id, values as UpdatePersonFormValues);
      } else {
        // Prepare person data with selected entities
        const personDataWithEntities = {
          ...(values as PersonFormValues),
          entities: selectedEntities.map(entity => ({
            entityId: entity.entityId,
            roleInEntity: entity.roleInEntity,
            startDate: entity.startDate,
            isPrimary: entity.isPrimary,
          }))
        };

        result = await createPerson(personDataWithEntities);
      }

      if (result.success && result.data) {
        onSuccess(result.data);
      } else {
        // Handle error - could show toast or form errors
        console.error('Form submission error:', result.error);
      }
    } catch (error) {
      console.error('Unexpected error:', error);
    }
  };

  const isLoading = isCreating || isUpdating;

  return (
    <Dialog open onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {isEditing ? `Modifier ${person.fullName}` : 'Nouvelle personne'}
          </DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Basic Information */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Informations personnelles</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="firstName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Prénom *</FormLabel>
                        <FormControl>
                          <Input placeholder="Prénom" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="lastName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Nom *</FormLabel>
                        <FormControl>
                          <Input placeholder="Nom de famille" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email</FormLabel>
                        <FormControl>
                          <Input 
                            type="email" 
                            placeholder="<EMAIL>" 
                            {...field} 
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="phone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Téléphone</FormLabel>
                        <FormControl>
                          <Input placeholder="+33 1 23 45 67 89" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Professional Information */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Informations professionnelles</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="employeeId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Matricule</FormLabel>
                        <FormControl>
                          <Input placeholder="EMP001" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="position"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Poste</FormLabel>
                        <FormControl>
                          <Input placeholder="Développeur, Manager, etc." {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="hireDate"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Date d'embauche</FormLabel>
                        <Popover>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <Button
                                variant="outline"
                                className={cn(
                                  "w-full pl-3 text-left font-normal",
                                  !field.value && "text-muted-foreground"
                                )}
                              >
                                {field.value ? (
                                  format(field.value, "PPP", { locale: fr })
                                ) : (
                                  <span>Sélectionner une date</span>
                                )}
                                <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                              </Button>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0" align="start">
                            <Calendar
                              mode="single"
                              selected={field.value || undefined}
                              onSelect={field.onChange}
                              disabled={(date) =>
                                date > new Date() || date < new Date("1900-01-01")
                              }
                              initialFocus
                              locale={fr}
                            />
                          </PopoverContent>
                        </Popover>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {isEditing && (
                    <FormField
                      control={form.control}
                      name="isActive"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                          <div className="space-y-0.5">
                            <FormLabel className="text-base">Statut actif</FormLabel>
                            <div className="text-sm text-muted-foreground">
                              La personne est-elle active dans l'organisation ?
                            </div>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Entity Assignments - Only for new persons */}
            {!isEditing && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center justify-between">
                    Assignations d'entités
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => setShowEntityManager(true)}
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Ajouter une entité
                    </Button>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {selectedEntities.map((entity) => (
                      <div key={entity.entityId} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center gap-2">
                          <Badge variant="secondary">
                            {entity.entityName}
                          </Badge>
                          {entity.isPrimary && (
                            <Badge variant="default">Principal</Badge>
                          )}
                          {entity.roleInEntity && (
                            <span className="text-sm text-muted-foreground">
                              - {entity.roleInEntity}
                            </span>
                          )}
                        </div>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setSelectedEntities(prev => prev.filter(e => e.entityId !== entity.entityId));
                          }}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                    {selectedEntities.length === 0 && (
                      <div className="text-center py-4">
                        <p className="text-sm text-muted-foreground mb-3">
                          Aucune assignation d'entité sélectionnée.
                        </p>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => setShowEntitySelector(true)}
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          Ajouter une entité
                        </Button>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Current Entity Assignments - Only for editing */}
            {isEditing && person && person.entities && person.entities.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Entités assignées</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {person.entities.map((assignment) => (
                      <div key={assignment.id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center gap-2">
                          <Badge variant="secondary">
                            {assignment.entity?.name}
                          </Badge>
                          {assignment.isPrimary && (
                            <Badge variant="default">Principal</Badge>
                          )}
                          {assignment.roleInEntity && (
                            <span className="text-sm text-muted-foreground">
                              - {assignment.roleInEntity}
                            </span>
                          )}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          Depuis le {format(new Date(assignment.startDate), "PPP", { locale: fr })}
                        </div>
                      </div>
                    ))}
                  </div>
                  <Separator className="my-4" />
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setShowEntityManager(true)}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Gérer les assignations
                  </Button>
                </CardContent>
              </Card>
            )}

            {/* Form Actions */}
            <div className="flex justify-end gap-2 pt-4">
              <Button type="button" variant="outline" onClick={onClose}>
                Annuler
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? 'Enregistrement...' : (isEditing ? 'Mettre à jour' : 'Créer')}
              </Button>
            </div>
          </form>
        </Form>

        {/* Entity Manager Modal */}
        {showEntityManager && (
          <PersonEntityManager
            person={person}
            onClose={() => setShowEntityManager(false)}
            onUpdate={() => {
              // Refresh the person data or trigger a re-fetch
              setShowEntityManager(false);
              onClose(); // Close the form to refresh the list
            }}
          />
        )}

        {/* Entity Selector Modal for creation */}
        {showEntitySelector && (
          <EntitySelector
            selectedEntities={selectedEntities}
            onEntitiesChange={setSelectedEntities}
            onClose={() => setShowEntitySelector(false)}
          />
        )}
      </DialogContent>
    </Dialog>
  );
}
