// Dashboard and Statistics Types
import type { Entity, Person } from './entities';
import type { User } from './users';

export interface Dashboard {
  id: string;
  name: string;
  description?: string;
  type: DashboardType;
  entityId?: string;
  ownerId: string;
  config: DashboardConfig;
  isDefault: boolean;
  isPublic: boolean;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  
  // Relations (populated when needed)
  entity?: Entity;
  owner?: User;
  widgets?: DashboardWidget[];
  filters?: DashboardFilter[];
}

export type DashboardType = 'OVERVIEW' | 'DIRECTOR' | 'MANAGER' | 'TEAM' | 'PERSONAL' | 'CUSTOM';

export interface DashboardConfig {
  layout: DashboardLayout;
  theme?: string;
  refreshInterval?: number;
  defaultFilters?: Record<string, any>;
  permissions?: DashboardPermissions;
  metadata?: Record<string, any>;
}

export interface DashboardLayout {
  columns: number;
  rows: number;
  widgets: WidgetPosition[];
  responsive?: ResponsiveLayout;
}

export interface WidgetPosition {
  widgetId: string;
  x: number;
  y: number;
  width: number;
  height: number;
  minWidth?: number;
  minHeight?: number;
  maxWidth?: number;
  maxHeight?: number;
}

export interface ResponsiveLayout {
  mobile?: Partial<DashboardLayout>;
  tablet?: Partial<DashboardLayout>;
  desktop?: Partial<DashboardLayout>;
}

export interface DashboardPermissions {
  view: string[];
  edit: string[];
  share: string[];
  delete: string[];
}

export interface DashboardWidget {
  id: string;
  dashboardId: string;
  name: string;
  type: WidgetType;
  config: WidgetConfig;
  dataSource: WidgetDataSource;
  position: WidgetPosition;
  isVisible: boolean;
  createdAt: Date;
  updatedAt: Date;
  
  // Relations (populated when needed)
  dashboard?: Dashboard;
}

export type WidgetType = 
  | 'CHART' 
  | 'TABLE' 
  | 'METRIC' 
  | 'PROGRESS' 
  | 'ALERT' 
  | 'LIST' 
  | 'CALENDAR' 
  | 'MAP' 
  | 'TEXT' 
  | 'IMAGE';

export interface WidgetConfig {
  title?: string;
  subtitle?: string;
  chartType?: ChartType;
  colors?: string[];
  showLegend?: boolean;
  showGrid?: boolean;
  showTooltip?: boolean;
  animation?: boolean;
  refreshInterval?: number;
  maxItems?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  filters?: Record<string, any>;
  formatting?: WidgetFormatting;
}

export type ChartType = 
  | 'line' 
  | 'bar' 
  | 'column' 
  | 'pie' 
  | 'doughnut' 
  | 'area' 
  | 'scatter' 
  | 'gauge' 
  | 'funnel';

export interface WidgetFormatting {
  numberFormat?: string;
  dateFormat?: string;
  currency?: string;
  precision?: number;
  showPercentage?: boolean;
  showTrend?: boolean;
}

export interface WidgetDataSource {
  type: DataSourceType;
  query?: string;
  endpoint?: string;
  params?: Record<string, any>;
  aggregation?: DataAggregation;
  filters?: DataFilter[];
  groupBy?: string[];
  orderBy?: string[];
}

export type DataSourceType = 'DATABASE' | 'API' | 'STATIC' | 'CALCULATED';

export interface DataAggregation {
  method: AggregationMethod;
  field: string;
  groupBy?: string[];
  having?: Record<string, any>;
}

export type AggregationMethod = 'COUNT' | 'SUM' | 'AVG' | 'MIN' | 'MAX' | 'DISTINCT';

export interface DataFilter {
  field: string;
  operator: FilterOperator;
  value: any;
  type?: FilterType;
}

export type FilterOperator = 
  | 'eq' | 'ne' | 'gt' | 'gte' | 'lt' | 'lte' 
  | 'in' | 'nin' | 'like' | 'ilike' | 'between' 
  | 'is_null' | 'is_not_null';

export type FilterType = 'string' | 'number' | 'date' | 'boolean' | 'array';

export interface DashboardFilter {
  id: string;
  dashboardId: string;
  name: string;
  field: string;
  type: FilterType;
  operator: FilterOperator;
  value: any;
  isActive: boolean;
  isGlobal: boolean;
  affectedWidgets: string[];
  
  // Relations (populated when needed)
  dashboard?: Dashboard;
}

// Statistics and Metrics Types
export interface DashboardStats {
  totalDashboards: number;
  dashboardsByType: Record<DashboardType, number>;
  totalWidgets: number;
  widgetsByType: Record<WidgetType, number>;
  averageWidgetsPerDashboard: number;
  mostUsedDashboard: string;
  lastUpdated: Date;
}

export interface EntityStats {
  entityId: string;
  entityName: string;
  entityType: string;
  totalMembers: number;
  activeMembers: number;
  completedTasks: number;
  pendingTasks: number;
  overdueTasks: number;
  performance: number;
  trend: 'up' | 'down' | 'stable';
  lastUpdated: Date;
}

export interface PerformanceMetric {
  id: string;
  name: string;
  value: number;
  target?: number;
  unit?: string;
  trend: TrendDirection;
  change: number;
  changePercent: number;
  period: TimePeriod;
  lastUpdated: Date;
}

export type TrendDirection = 'up' | 'down' | 'stable';
export type TimePeriod = 'day' | 'week' | 'month' | 'quarter' | 'year';

export interface Alert {
  id: string;
  title: string;
  message: string;
  type: AlertType;
  severity: AlertSeverity;
  entityId?: string;
  userId?: string;
  isRead: boolean;
  isResolved: boolean;
  data?: Record<string, any>;
  createdAt: Date;
  resolvedAt?: Date;
  
  // Relations (populated when needed)
  entity?: Entity;
  user?: User;
}

export type AlertType = 'SYSTEM' | 'PERFORMANCE' | 'DEADLINE' | 'ERROR' | 'WARNING' | 'INFO';
export type AlertSeverity = 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';

// Form and Input Types
export interface CreateDashboardData {
  name: string;
  description?: string;
  type: DashboardType;
  entityId?: string;
  config?: Partial<DashboardConfig>;
  isDefault?: boolean;
  isPublic?: boolean;
}

export interface UpdateDashboardData {
  name?: string;
  description?: string;
  config?: Partial<DashboardConfig>;
  isDefault?: boolean;
  isPublic?: boolean;
  isActive?: boolean;
}

export interface CreateWidgetData {
  dashboardId: string;
  name: string;
  type: WidgetType;
  config: WidgetConfig;
  dataSource: WidgetDataSource;
  position: WidgetPosition;
}

export interface UpdateWidgetData {
  name?: string;
  config?: Partial<WidgetConfig>;
  dataSource?: Partial<WidgetDataSource>;
  position?: Partial<WidgetPosition>;
  isVisible?: boolean;
}

// Query and Filter Types
export interface DashboardFilters {
  type?: DashboardType;
  entityId?: string;
  ownerId?: string;
  isDefault?: boolean;
  isPublic?: boolean;
  isActive?: boolean;
  searchTerm?: string;
}

export interface WidgetFilters {
  dashboardId?: string;
  type?: WidgetType;
  isVisible?: boolean;
  searchTerm?: string;
}

// Export and Import Types
export interface DashboardExport {
  dashboard: Dashboard;
  widgets: DashboardWidget[];
  filters: DashboardFilter[];
  metadata: {
    exportedAt: Date;
    exportedBy: string;
    version: string;
  };
}

export interface DashboardImport {
  dashboard: Omit<Dashboard, 'id' | 'createdAt' | 'updatedAt'>;
  widgets: Omit<DashboardWidget, 'id' | 'dashboardId' | 'createdAt' | 'updatedAt'>[];
  filters: Omit<DashboardFilter, 'id' | 'dashboardId'>[];
  options?: {
    overwrite?: boolean;
    preserveIds?: boolean;
    updateOwner?: boolean;
  };
}

// Real-time and Subscription Types
export interface DashboardSubscription {
  dashboardId: string;
  userId: string;
  widgets: string[];
  filters: Record<string, any>;
  lastUpdate: Date;
}

export interface WidgetUpdate {
  widgetId: string;
  data: any;
  timestamp: Date;
  version: number;
}
