'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { CalendarIcon, Save, Loader2, <PERSON><PERSON>dingUp, AlertCircle } from 'lucide-react';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';

import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage, FormDescription } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { cn } from '@/lib/utils';

import { createInstallationTracking } from '@/features/installations/actions';
import type { Installation } from '@/features/installations/types';

// Schema de validation pour le formulaire de suivi
const trackingSchema = z.object({
  tracking_date: z.date({
    required_error: 'La date de suivi est requise',
  }),
  overall_progress: z.number()
    .min(0, 'La progression ne peut pas être négative')
    .max(100, 'La progression ne peut pas dépasser 100%'),
  
  // Champs spécifiques KYA-SoP
  foundation_progress: z.number().min(0).max(100).optional(),
  installation_progress: z.number().min(0).max(100).optional(),
  connection_progress: z.number().min(0).max(100).optional(),
  testing_progress: z.number().min(0).max(100).optional(),
  
  // Champs spécifiques Lampadaire
  pole_installation_progress: z.number().min(0).max(100).optional(),
  electrical_connection_progress: z.number().min(0).max(100).optional(),
  lighting_test_progress: z.number().min(0).max(100).optional(),
  
  // Champs communs
  weather_conditions: z.string().optional(),
  team_present: z.string().optional(),
  issues_encountered: z.string().optional(),
  next_steps: z.string().optional(),
  notes: z.string().optional(),
});

type TrackingFormData = z.infer<typeof trackingSchema>;

interface TrackingFormProps {
  installationId: string;
  installation: Installation;
}

export function TrackingForm({ installationId, installation }: TrackingFormProps) {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<TrackingFormData>({
    resolver: zodResolver(trackingSchema),
    defaultValues: {
      tracking_date: new Date(),
      overall_progress: installation.overall_progress || 0,
      foundation_progress: 0,
      installation_progress: 0,
      connection_progress: 0,
      testing_progress: 0,
      pole_installation_progress: 0,
      electrical_connection_progress: 0,
      lighting_test_progress: 0,
      weather_conditions: '',
      team_present: installation.team_leader_name || '',
      issues_encountered: '',
      next_steps: '',
      notes: '',
    },
  });

  const onSubmit = async (data: TrackingFormData) => {
    setIsSubmitting(true);
    
    try {
      const result = await createInstallationTracking(installationId, {
        tracking_date: data.tracking_date.toISOString(),
        overall_progress: data.overall_progress,
        foundation_progress: data.foundation_progress,
        installation_progress: data.installation_progress,
        connection_progress: data.connection_progress,
        testing_progress: data.testing_progress,
        pole_installation_progress: data.pole_installation_progress,
        electrical_connection_progress: data.electrical_connection_progress,
        lighting_test_progress: data.lighting_test_progress,
        weather_conditions: data.weather_conditions,
        team_present: data.team_present,
        issues_encountered: data.issues_encountered,
        next_steps: data.next_steps,
        notes: data.notes,
      });

      if (result.success) {
        router.push(`/saisie/installations/${installationId}`);
      } else {
        console.error('Error creating tracking:', result.error);
      }
    } catch (error) {
      console.error('Error submitting tracking:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const isKyaSop = installation.product_type === 'KYA_SOP';
  const isLampadaire = installation.product_type === 'LAMPADAIRE';

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        {/* Informations générales */}
        <Card className="border-kya-primary/20">
          <CardHeader>
            <CardTitle className="text-kya-primary">Informations Générales</CardTitle>
            <CardDescription>
              Date et progression globale de l'installation
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Date de suivi */}
              <FormField
                control={form.control}
                name="tracking_date"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Date de Suivi</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              "w-full pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value ? (
                              format(field.value, "PPP", { locale: fr })
                            ) : (
                              <span>Sélectionner une date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) =>
                            date > new Date() || date < new Date("1900-01-01")
                          }
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Progression globale */}
              <FormField
                control={form.control}
                name="overall_progress"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Progression Globale (%)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="0"
                        max="100"
                        {...field}
                        onChange={(e) => field.onChange(Number(e.target.value))}
                        className="border-kya-primary/30 focus:border-kya-primary"
                      />
                    </FormControl>
                    <FormDescription>
                      Progression générale de l'installation (0-100%)
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </CardContent>
        </Card>

        {/* Progression détaillée selon le type de produit */}
        {isKyaSop && (
          <Card className="border-blue-200">
            <CardHeader>
              <CardTitle className="text-blue-700 flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Progression KYA-SoP
              </CardTitle>
              <CardDescription>
                Détail de la progression pour l'installation KYA-SoP
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="foundation_progress"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Fondations (%)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="0"
                          max="100"
                          {...field}
                          onChange={(e) => field.onChange(Number(e.target.value) || 0)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="installation_progress"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Installation (%)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="0"
                          max="100"
                          {...field}
                          onChange={(e) => field.onChange(Number(e.target.value) || 0)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="connection_progress"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Connexions (%)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="0"
                          max="100"
                          {...field}
                          onChange={(e) => field.onChange(Number(e.target.value) || 0)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="testing_progress"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Tests (%)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="0"
                          max="100"
                          {...field}
                          onChange={(e) => field.onChange(Number(e.target.value) || 0)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>
        )}

        {isLampadaire && (
          <Card className="border-green-200">
            <CardHeader>
              <CardTitle className="text-green-700 flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Progression Lampadaire
              </CardTitle>
              <CardDescription>
                Détail de la progression pour l'installation de lampadaire
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <FormField
                  control={form.control}
                  name="pole_installation_progress"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Installation Poteau (%)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="0"
                          max="100"
                          {...field}
                          onChange={(e) => field.onChange(Number(e.target.value) || 0)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="electrical_connection_progress"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Connexion Électrique (%)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="0"
                          max="100"
                          {...field}
                          onChange={(e) => field.onChange(Number(e.target.value) || 0)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="lighting_test_progress"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Test Éclairage (%)</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          min="0"
                          max="100"
                          {...field}
                          onChange={(e) => field.onChange(Number(e.target.value) || 0)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>
        )}

        <Separator />

        {/* Informations contextuelles */}
        <Card>
          <CardHeader>
            <CardTitle>Informations Contextuelles</CardTitle>
            <CardDescription>
              Conditions et observations du jour
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="weather_conditions"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Conditions Météo</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Ex: Ensoleillé, pluvieux, venteux..."
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="team_present"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Équipe Présente</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Membres de l'équipe présents"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="issues_encountered"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Problèmes Rencontrés</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Décrivez les difficultés ou problèmes rencontrés..."
                      className="min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="next_steps"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Prochaines Étapes</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Planifiez les prochaines actions à réaliser..."
                      className="min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Notes Additionnelles</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Observations, remarques ou informations supplémentaires..."
                      className="min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
        </Card>

        {/* Boutons d'action */}
        <div className="flex justify-end gap-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.back()}
            disabled={isSubmitting}
          >
            Annuler
          </Button>
          
          <Button
            type="submit"
            disabled={isSubmitting}
            className="gradient-kya-primary text-white hover:opacity-90"
          >
            {isSubmitting ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Enregistrement...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Enregistrer le Suivi
              </>
            )}
          </Button>
        </div>

        {/* Alerte d'information */}
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            <strong>Versioning automatique :</strong> Si un suivi existe déjà pour cette date, 
            il sera mis à jour. Sinon, un nouveau suivi sera créé.
          </AlertDescription>
        </Alert>
      </form>
    </Form>
  );
}
