'use client';

import { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { createClient } from '@/utils/supabase/client';
import type { AuthUser, UserProfile } from '@/types/auth';
import { SupabaseClient } from '@supabase/supabase-js';
import { keysToCamel } from '@/utils/case-converter';
import { AuthLoading } from '@/components/auth-loading';
import { usePermissionStore } from '@/features/rbac/stores/permission-store';

interface AuthContextType {
  user: AuthUser | null;
  profile: UserProfile | null;
  isLoading: boolean;
  refetchUserData: () => void;
}

export const AuthContext = createContext<AuthContextType | null>(null);

interface AuthProviderProps {
  children: React.ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const supabase = createClient();
  const [user, setUser] = useState<AuthUser | null>(null);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const { fetchPermissions, clearPermissions } = usePermissionStore();

  const checkUser = useCallback(async () => {
    try {
      const { data: { user: supabaseUser } } = await supabase.auth.getUser();

      if (supabaseUser) {
        const { data: authUser, error: userError } = await supabase
          .from('auth_users')
          .select('*')
          .eq('supabase_id', supabaseUser.id)
          .single();
        
        if (userError) throw userError;
        
        const camelUser: AuthUser = keysToCamel(authUser);
        setUser(camelUser);
        fetchPermissions(camelUser.id);

        const { data: userProfile, error: profileError } = await supabase
          .from('user_profiles')
          .select('*')
          .eq('auth_user_id', camelUser.id)
          .single();

        if (profileError) throw profileError;
        
        setProfile(keysToCamel(userProfile));
      } else {
        setUser(null);
        setProfile(null);
        clearPermissions();
      }
    } catch (error) {
      console.error("Error checking user session:", error);
      setUser(null);
      setProfile(null);
      clearPermissions();
    } finally {
      setIsLoading(false);
    }
  }, [supabase, fetchPermissions, clearPermissions]);

  useEffect(() => {
    // Check session on initial load
    checkUser();

    // Listen for auth state changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      console.log(`Auth event: ${event}`);
      checkUser();
    });

    return () => {
      subscription.unsubscribe();
    };
  }, [checkUser, supabase]);

  const contextValue: AuthContextType = {
    user,
    profile,
    isLoading,
    refetchUserData: checkUser, // refetch is now just checkUser
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {isLoading ? <AuthLoading /> : children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
