// Installation Daily Tracking Tab - KYA Dashboards
'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Plus, 
  Search, 
  Filter, 
  Calendar,
  Edit,
  Eye,
  Save,
  X
} from 'lucide-react';

import { useInstallations, useInstallationTracking } from '../hooks/use-installations';
import { InstallationStatusBadge, ProductTypeBadge, ProgressBar } from './installation-dashboard';
import type { Installation, InstallationSearchParams } from '../types';

// Import dynamique pour éviter les problèmes circulaires
const InstallationTrackingForm = ({ installation, onSubmit }: any) => {
  return (
    <div className="p-4 text-center text-muted-foreground">
      <p>Formulaire de suivi sera affiché ici</p>
      <p className="text-xs mt-2">Installation: {installation.name}</p>
    </div>
  );
};

interface InstallationDailyTrackingProps {
  teamLeaderId?: string;
  entityId?: string;
}

export function InstallationDailyTracking({ teamLeaderId, entityId }: InstallationDailyTrackingProps) {
  const [searchParams, setSearchParams] = useState<InstallationSearchParams>({
    filters: {
      status: 'IN_PROGRESS', // Show only active installations by default
      team_leader_id: teamLeaderId,
    }
  });
  const [selectedInstallation, setSelectedInstallation] = useState<Installation | null>(null);
  const [showTrackingForm, setShowTrackingForm] = useState(false);

  const { data: installations, isLoading } = useInstallations(searchParams);

  const handleSearch = (query: string) => {
    setSearchParams(prev => ({
      ...prev,
      query
    }));
  };

  const handleFilterChange = (key: string, value: any) => {
    setSearchParams(prev => ({
      ...prev,
      filters: {
        ...prev.filters,
        [key]: value === 'all' ? undefined : value
      }
    }));
  };

  const handleTrackingSubmit = () => {
    setShowTrackingForm(false);
    setSelectedInstallation(null);
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <Card key={i}>
            <CardContent className="pt-6">
              <div className="animate-pulse space-y-2">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Actions */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Suivi Quotidien</h2>
          <p className="text-muted-foreground">
            Mettre à jour les progrès et suivre les activités quotidiennes
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm">
            <Calendar className="h-4 w-4 mr-2" />
            Aujourd'hui: {new Date().toLocaleDateString('fr-FR')}
          </Button>
          <Button size="sm">
            <Plus className="h-4 w-4 mr-2" />
            Nouvelle Installation
          </Button>
        </div>
      </div>

      {/* Recherche et Filtres */}
      <Card>
        <CardHeader>
          <CardTitle>Recherche et Filtres</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Rechercher des installations..."
                  className="pl-8"
                  onChange={(e) => handleSearch(e.target.value)}
                />
              </div>
            </div>
            <Select
              value={searchParams.filters?.status || 'all'}
              onValueChange={(value) => handleFilterChange('status', value)}
            >
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filtrer par statut" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tous les Statuts</SelectItem>
                <SelectItem value="PLANNING">Planification</SelectItem>
                <SelectItem value="IN_PROGRESS">En Cours</SelectItem>
                <SelectItem value="TESTING">Test</SelectItem>
                <SelectItem value="COMPLETED">Terminé</SelectItem>
                <SelectItem value="ON_HOLD">En Attente</SelectItem>
              </SelectContent>
            </Select>
            <Select
              value={searchParams.filters?.product_type || 'all'}
              onValueChange={(value) => handleFilterChange('product_type', value)}
            >
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filtrer par type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tous les Types</SelectItem>
                <SelectItem value="KYA-SoP">KYA-SoP</SelectItem>
                <SelectItem value="Lampadaire">Lampadaire</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Main Content */}
      <div className="grid gap-6 lg:grid-cols-3">
        {/* Installation List */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Active Installations ({installations?.length || 0})</CardTitle>
              <CardDescription>
                Click on an installation to update its progress
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {installations?.map((installation) => (
                  <InstallationTrackingCard
                    key={installation.id}
                    installation={installation}
                    onSelect={() => setSelectedInstallation(installation)}
                    onTrack={() => {
                      setSelectedInstallation(installation);
                      setShowTrackingForm(true);
                    }}
                    isSelected={selectedInstallation?.id === installation.id}
                  />
                ))}
                {installations?.length === 0 && (
                  <div className="text-center py-8">
                    <p className="text-muted-foreground">No installations found</p>
                    <Button variant="outline" className="mt-2">
                      <Plus className="h-4 w-4 mr-2" />
                      Create New Installation
                    </Button>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Tracking Form or Details */}
        <div>
          {showTrackingForm && selectedInstallation ? (
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Update Progress</CardTitle>
                    <CardDescription>
                      {selectedInstallation.name}
                    </CardDescription>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowTrackingForm(false)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <InstallationTrackingForm
                  installation={selectedInstallation}
                  onSubmit={handleTrackingSubmit}
                />
              </CardContent>
            </Card>
          ) : selectedInstallation ? (
            <InstallationDetailsCard
              installation={selectedInstallation}
              onTrack={() => setShowTrackingForm(true)}
            />
          ) : (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center text-muted-foreground">
                  <Calendar className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>Select an installation to view details or update progress</p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}

// Installation Tracking Card Component
function InstallationTrackingCard({ 
  installation, 
  onSelect, 
  onTrack, 
  isSelected 
}: {
  installation: Installation;
  onSelect: () => void;
  onTrack: () => void;
  isSelected: boolean;
}) {
  const progress = installation.latest_tracking?.global_progress || 0;

  return (
    <div
      className={`p-4 border rounded-lg cursor-pointer transition-colors ${
        isSelected ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'
      }`}
      onClick={onSelect}
    >
      <div className="flex items-center justify-between mb-3">
        <div className="space-y-1">
          <h3 className="font-medium">{installation.name}</h3>
          <div className="flex items-center space-x-2">
            <ProductTypeBadge productType={installation.product_type} />
            <InstallationStatusBadge status={installation.status} />
          </div>
        </div>
        <div className="text-right">
          <p className="text-sm font-medium">{progress}%</p>
          <p className="text-xs text-muted-foreground">Progress</p>
        </div>
      </div>

      <div className="space-y-2">
        <ProgressBar value={progress} />
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <span>{installation.client?.name}</span>
          <span>{installation.site_location}</span>
        </div>
      </div>

      <div className="flex items-center justify-between mt-3">
        <div className="text-xs text-muted-foreground">
          {installation.team_leader ? (
            <span>Leader: {installation.team_leader.firstName} {installation.team_leader.lastName}</span>
          ) : (
            <span>No team leader assigned</span>
          )}
        </div>
        <div className="flex items-center space-x-1">
          <Button variant="ghost" size="sm" onClick={(e) => { e.stopPropagation(); onSelect(); }}>
            <Eye className="h-3 w-3" />
          </Button>
          <Button variant="ghost" size="sm" onClick={(e) => { e.stopPropagation(); onTrack(); }}>
            <Edit className="h-3 w-3" />
          </Button>
        </div>
      </div>
    </div>
  );
}

// Installation Details Card Component
function InstallationDetailsCard({ 
  installation, 
  onTrack 
}: {
  installation: Installation;
  onTrack: () => void;
}) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>{installation.name}</CardTitle>
        <CardDescription>Installation Details</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <Label className="text-muted-foreground">Client</Label>
            <p className="font-medium">{installation.client?.name}</p>
          </div>
          <div>
            <Label className="text-muted-foreground">Type</Label>
            <ProductTypeBadge productType={installation.product_type} />
          </div>
          <div>
            <Label className="text-muted-foreground">Status</Label>
            <InstallationStatusBadge status={installation.status} />
          </div>
          <div>
            <Label className="text-muted-foreground">Progress</Label>
            <p className="font-medium">{installation.latest_tracking?.global_progress || 0}%</p>
          </div>
        </div>

        <div>
          <Label className="text-muted-foreground">Location</Label>
          <p className="font-medium">{installation.site_location}</p>
        </div>

        {installation.equipment_description && (
          <div>
            <Label className="text-muted-foreground">Equipment</Label>
            <p className="text-sm">{installation.equipment_description}</p>
          </div>
        )}

        <div className="pt-4">
          <Button onClick={onTrack} className="w-full">
            <Edit className="h-4 w-4 mr-2" />
            Update Progress
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
