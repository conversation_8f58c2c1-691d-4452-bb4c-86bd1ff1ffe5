'use client';

import { useEffect, useState, useTransition } from 'react';
import { MoreHorizontal, PlusCircle } from 'lucide-react';
import { toast } from 'sonner';
import {
  getUsersAction,
  createUserAction,
  updateUserAction,
  deleteUserAction,
} from '../actions';
import type { UserWithProfile } from '@/types/users';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ManageUserDialog } from './manage-user-dialog';
import { DeleteUserAlert } from './delete-user-alert';
import type {
  CreateUserFormValues,
  UpdateUserFormValues,
} from '../schemas';

export function UserList() {
  const [users, setUsers] = useState<UserWithProfile[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isManageUserOpen, setManageUserOpen] = useState(false);
  const [isDeleteAlertOpen, setDeleteAlertOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<UserWithProfile | null>(
    null
  );
  const [isPending, startTransition] = useTransition();

  const fetchUsers = async () => {
    setIsLoading(true);
    const result = await getUsersAction();
    if (result.data) {
      setUsers(result.data);
    } else if (result.error) {
      setError(result.error);
      toast.error(result.error);
    }
    setIsLoading(false);
  };

  useEffect(() => {
    fetchUsers();
  }, []);

  const handleAddUser = () => {
    setSelectedUser(null);
    setManageUserOpen(true);
  };

  const handleEditUser = (user: UserWithProfile) => {
    setSelectedUser(user);
    setManageUserOpen(true);
  };

  const handleDeleteUser = (user: UserWithProfile) => {
    setSelectedUser(user);
    setDeleteAlertOpen(true);
  };

  const handleFormSubmit = (
    values: CreateUserFormValues | UpdateUserFormValues
  ) => {
    startTransition(async () => {
      const action = selectedUser
        ? updateUserAction(selectedUser.id, values as UpdateUserFormValues)
        : createUserAction(values as CreateUserFormValues);

      const result = await action;

      if (result.data) {
        toast.success(
          `Utilisateur ${selectedUser ? 'mis à jour' : 'créé'} avec succès.`
        );
        setManageUserOpen(false);
        fetchUsers();
      } else {
        toast.error(result.error || 'Une erreur est survenue.');
      }
    });
  };

  const handleDeleteConfirm = () => {
    if (!selectedUser) return;
    startTransition(async () => {
      const result = await deleteUserAction(selectedUser.id);
      if (result.data) {
        toast.success('Utilisateur supprimé avec succès.');
        setDeleteAlertOpen(false);
        fetchUsers();
      } else {
        toast.error(result.error || 'Une erreur est survenue.');
      }
    });
  };

  if (isLoading) {
    return <div>Chargement de la liste des utilisateurs...</div>;
  }

  if (error && users.length === 0) {
    return <div className="text-destructive">Erreur: {error}</div>;
  }

  return (
    <>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Utilisateurs</CardTitle>
            <Button onClick={handleAddUser}>
              <PlusCircle className="mr-2 h-4 w-4" />
              Ajouter un utilisateur
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Email</TableHead>
                <TableHead>Nom d'affichage</TableHead>
                <TableHead>Rôles</TableHead>
                <TableHead>Statut</TableHead>
                <TableHead>Date de création</TableHead>
                <TableHead>
                  <span className="sr-only">Actions</span>
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {users.map((user) => (
                <TableRow key={user.id}>
                  <TableCell className="font-medium">{user.email}</TableCell>
                  <TableCell>{user.profile?.displayName || 'N/A'}</TableCell>
                  <TableCell>
                    {user.roles?.map((role) => (
                      <Badge key={role.id} variant="secondary" className="mr-1">
                        {role.name}
                      </Badge>
                    ))}
                  </TableCell>
                  <TableCell>
                    <Badge variant={user.isActive ? 'default' : 'destructive'}>
                      {user.isActive ? 'Actif' : 'Inactif'}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {new Date(user.createdAt).toLocaleDateString()}
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          aria-haspopup="true"
                          size="icon"
                          variant="ghost"
                        >
                          <MoreHorizontal className="h-4 w-4" />
                          <span className="sr-only">Toggle menu</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuItem onClick={() => handleEditUser(user)}>
                          Modifier
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleDeleteUser(user)}
                          className="text-destructive"
                        >
                          Supprimer
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
      <ManageUserDialog
        isOpen={isManageUserOpen}
        onOpenChange={setManageUserOpen}
        user={selectedUser}
        onSubmit={handleFormSubmit}
        isSubmitting={isPending}
      />
      <DeleteUserAlert
        isOpen={isDeleteAlertOpen}
        onOpenChange={setDeleteAlertOpen}
        onConfirm={handleDeleteConfirm}
        isDeleting={isPending}
      />
    </>
  );
}