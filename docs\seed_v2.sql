-- KYA Dashboards - Simplified RB<PERSON> Seed (Version 2)
-- This script uses grouped permissions and special logic for SUPER_ADMIN
-- It's designed to be idempotent, so it can be run safely multiple times.

-- 1. System Roles
-------------------------------------------------------------------------------
-- These are the core roles for the application.
-- level: Higher numbers have more privilege.
-- is_system_role: Prevents deletion from the UI.
INSERT INTO roles (name, display_name, description, level, is_system_role) VALUES
('SUPER_ADMIN', 'Super Administrateur', 'Accès complet et illimité à tout le système. Bypass toutes les vérifications de permissions.', 100, true),
('DIRECTOR', 'Directeur', 'Accès à tous les dashboards et aux paramètres globaux.', 90, true),
('MANAGER', 'Manager', 'Gestion des entités (équipes/directions) et de leurs membres.', 50, true),
('MEMBER', 'Membre', 'Accès à la saisie de données et à la consultation de son équipe.', 10, true)
ON CONFLICT (name) DO NOTHING;

-- 2. Simplified System Permissions
-------------------------------------------------------------------------------
-- Using grouped permissions that match the code requirements

-- Admin Access
INSERT INTO permissions (name, display_name, resource, action, is_system_permission) VALUES
('admin.access', 'Accéder à l''administration', 'admin', 'access', true)
ON CONFLICT (name) DO NOTHING;

-- User Management (Grouped)
INSERT INTO permissions (name, display_name, resource, action, is_system_permission) VALUES
('admin.users.read', 'Consulter les utilisateurs (Admin)', 'admin_users', 'read', true),
('admin.users.manage', 'Gérer les utilisateurs (Admin)', 'admin_users', 'manage', true)
ON CONFLICT (name) DO NOTHING;

-- RBAC Management (Grouped)
INSERT INTO permissions (name, display_name, resource, action, is_system_permission) VALUES
('admin.rbac.manage', 'Gérer les rôles et permissions (Admin)', 'admin_rbac', 'manage', true)
ON CONFLICT (name) DO NOTHING;

-- Entity Management (Grouped)
INSERT INTO permissions (name, display_name, resource, action, is_system_permission) VALUES
('admin.entities.manage', 'Gérer les entités (Admin)', 'admin_entities', 'manage', true)
ON CONFLICT (name) DO NOTHING;

-- Person Management (Grouped)
INSERT INTO permissions (name, display_name, resource, action, is_system_permission) VALUES
('admin.persons.read', 'Consulter les personnes (Admin)', 'admin_persons', 'read', true),
('admin.persons.manage', 'Gérer les personnes (Admin)', 'admin_persons', 'manage', true),
('admin.persons.assign', 'Gérer les assignations de personnes (Admin)', 'admin_persons', 'assign', true)
ON CONFLICT (name) DO NOTHING;

-- Dashboard Access
INSERT INTO permissions (name, display_name, resource, action, is_system_permission) VALUES
('dashboard.view.all', 'Voir tous les dashboards', 'dashboard', 'view_all', true),
('dashboard.view.team', 'Voir le dashboard de son équipe', 'dashboard', 'view_team', true)
ON CONFLICT (name) DO NOTHING;

-- Data Entry
INSERT INTO permissions (name, display_name, resource, action, is_system_permission) VALUES
('data.entry', 'Saisir les données', 'data_entry', 'create', true),
('data.validate', 'Valider les données', 'data_entry', 'validate', true)
ON CONFLICT (name) DO NOTHING;

-- Legacy permissions (for backward compatibility)
INSERT INTO permissions (name, display_name, resource, action, is_system_permission) VALUES
('users.create', 'Créer des utilisateurs', 'users', 'create', true),
('users.read', 'Consulter les utilisateurs', 'users', 'read', true),
('users.update', 'Modifier les utilisateurs', 'users', 'update', true),
('users.delete', 'Supprimer les utilisateurs', 'users', 'delete', true),
('entities.create', 'Créer des entités', 'entities', 'create', true),
('entities.read', 'Consulter les entités', 'entities', 'read', true),
('entities.update', 'Modifier les entités', 'entities', 'update', true),
('entities.delete', 'Supprimer les entités', 'entities', 'delete', true),
('roles.manage', 'Gérer les rôles et permissions', 'roles', 'manage', true)
ON CONFLICT (name) DO NOTHING;

-- 3. Role-Permission Assignments
-------------------------------------------------------------------------------

-- SUPER_ADMIN: Gets all permissions (but will bypass checks in code anyway)
-- This is for consistency and potential future use
INSERT INTO roles_permissions (role_id, permission_id)
SELECT r.id, p.id FROM roles r, permissions p WHERE r.name = 'SUPER_ADMIN'
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- DIRECTOR: Admin access and management permissions
INSERT INTO roles_permissions (role_id, permission_id)
SELECT
    (SELECT id FROM roles WHERE name = 'DIRECTOR'),
    p.id
FROM permissions p
WHERE p.name IN (
    'admin.access',
    'admin.users.read', 'admin.users.manage',
    'admin.entities.manage',
    'admin.persons.read', 'admin.persons.manage', 'admin.persons.assign',
    'admin.rbac.manage',
    'dashboard.view.all',
    -- Legacy permissions for compatibility
    'users.create', 'users.read', 'users.update',
    'entities.read'
)
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- MANAGER: Can manage their assigned entities and validate data
INSERT INTO roles_permissions (role_id, permission_id)
SELECT
    (SELECT id FROM roles WHERE name = 'MANAGER'),
    p.id
FROM permissions p
WHERE p.name IN (
    'entities.read', 'entities.update', -- For their own entities
    'users.read', -- To see team members
    'dashboard.view.team',
    'data.entry', 'data.validate'
)
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- MEMBER: Can only perform data entry and view their team's dashboard
INSERT INTO roles_permissions (role_id, permission_id)
SELECT
    (SELECT id FROM roles WHERE name = 'MEMBER'),
    p.id
FROM permissions p
WHERE p.name IN (
    'dashboard.view.team',
    'data.entry'
)
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- 4. Verification Query
-------------------------------------------------------------------------------
-- Show all role-permission assignments
SELECT 
    r.name as role_name, 
    r.level,
    COUNT(p.id) as permission_count,
    STRING_AGG(p.name, ', ' ORDER BY p.name) as permissions
FROM roles_permissions rp
JOIN roles r ON rp.role_id = r.id
JOIN permissions p ON rp.permission_id = p.id
GROUP BY r.id, r.name, r.level
ORDER BY r.level DESC, r.name;
