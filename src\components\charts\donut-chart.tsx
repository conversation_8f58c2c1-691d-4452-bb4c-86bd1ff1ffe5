import { useMemo } from 'react';
import { <PERSON><PERSON><PERSON> as Recharts<PERSON>ie<PERSON><PERSON>, Pie, Cell } from 'recharts';
import { 
  ChartContainer, 
  ChartTooltip, 
  ChartTooltipContent, 
  ChartLegend, 
  ChartLegendContent,
  type ChartConfig 
} from '@/components/ui/chart';
import { cn } from '@/lib/utils';

export interface DonutChartData {
  label: string;
  value: number;
  color?: string;
  fill?: string;
}

interface DonutChartProps {
  data: DonutChartData[];
  className?: string;
  showTooltip?: boolean;
  showLegend?: boolean;
  innerRadius?: number;
  outerRadius?: number;
  centerText?: string;
  centerSubtext?: string;
  config?: ChartConfig;
  onSegmentClick?: (data: DonutChartData, index: number) => void;
  showPercentages?: boolean;
  showValues?: boolean;
}

const defaultColors = [
  'hsl(var(--chart-1))', // KYA Primary
  'hsl(var(--chart-2))', // KYA Secondary  
  'hsl(var(--chart-3))', // KYA Accent
  'hsl(var(--chart-4))', // KYA Brown
  'hsl(var(--chart-5))', // Additional colors
];

export function DonutChart({
  data,
  className,
  showTooltip = true,
  showLegend = true,
  innerRadius = 60,
  outerRadius = 100,
  centerText,
  centerSubtext,
  config,
  onSegmentClick,
  showPercentages = false,
  showValues = true,
}: DonutChartProps) {
  const processedData = useMemo(() => {
    const total = data.reduce((sum, item) => sum + item.value, 0);
    
    return data.map((item, index) => ({
      ...item,
      fill: item.fill || item.color || `var(--color-${item.label.toLowerCase().replace(/\s+/g, '-')})` || defaultColors[index % defaultColors.length],
      percentage: total > 0 ? (item.value / total) * 100 : 0,
    }));
  }, [data]);

  const chartConfig = useMemo(() => {
    if (config) return config;
    
    return data.reduce((acc, item, index) => {
      const key = item.label.toLowerCase().replace(/\s+/g, '-');
      acc[key] = {
        label: item.label,
        color: item.color || defaultColors[index % defaultColors.length],
      };
      return acc;
    }, {} as ChartConfig);
  }, [data, config]);

  const total = processedData.reduce((sum, item) => sum + item.value, 0);

  if (total === 0) {
    return (
      <div className={cn('flex items-center justify-center h-[300px]', className)}>
        <div className="text-center text-muted-foreground">
          <div className="text-4xl mb-2">🍩</div>
          <p>Aucune donnée disponible</p>
        </div>
      </div>
    );
  }

  // Calcul automatique du texte central si non fourni
  const displayCenterText = centerText || total.toLocaleString('fr-FR');
  const displayCenterSubtext = centerSubtext || 'Total';

  return (
    <div className={cn('relative', className)}>
      <ChartContainer config={chartConfig} className="h-[300px]">
        <RechartsPieChart>
          <Pie
            data={processedData}
            cx="50%"
            cy="50%"
            innerRadius={innerRadius}
            outerRadius={outerRadius}
            paddingAngle={2}
            dataKey="value"
            onClick={onSegmentClick}
            className={onSegmentClick ? 'cursor-pointer' : ''}
          >
            {processedData.map((entry, index) => (
              <Cell 
                key={`cell-${index}`} 
                fill={entry.fill}
                className="hover:opacity-80 transition-opacity"
              />
            ))}
          </Pie>
          
          {showTooltip && (
            <ChartTooltip 
              content={
                <ChartTooltipContent 
                  formatter={(value, name, props) => {
                    const percentage = props.payload?.percentage || 0;
                    const formattedValue = Number(value).toLocaleString('fr-FR');
                    
                    if (showPercentages && showValues) {
                      return [`${formattedValue} (${percentage.toFixed(1)}%)`, name];
                    } else if (showPercentages) {
                      return [`${percentage.toFixed(1)}%`, name];
                    } else {
                      return [formattedValue, name];
                    }
                  }}
                />
              } 
            />
          )}
          
          {showLegend && (
            <ChartLegend content={<ChartLegendContent />} />
          )}
        </RechartsPieChart>
      </ChartContainer>

      {/* Texte central */}
      <div className="absolute inset-0 flex flex-col items-center justify-center text-center pointer-events-none">
        <div className="text-2xl font-bold text-foreground">
          {displayCenterText}
        </div>
        <div className="text-sm text-muted-foreground mt-1">
          {displayCenterSubtext}
        </div>
      </div>
    </div>
  );
}

// Composant de donut chart avec légende personnalisée
interface DonutChartWithStatsProps extends DonutChartProps {
  showStats?: boolean;
}

export function DonutChartWithStats({
  data,
  showStats = true,
  showLegend = false,
  className,
  ...props
}: DonutChartWithStatsProps) {
  const total = data.reduce((sum, item) => sum + item.value, 0);
  
  const processedData = useMemo(() => {
    return data.map((item) => ({
      ...item,
      percentage: total > 0 ? (item.value / total) * 100 : 0,
    }));
  }, [data, total]);

  return (
    <div className={cn('space-y-4', className)}>
      <DonutChart
        data={data}
        showLegend={showLegend}
        {...props}
      />
      
      {showStats && (
        <div className="grid grid-cols-1 gap-3">
          {processedData.map((item, index) => (
            <div key={index} className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
              <div className="flex items-center space-x-3">
                <div
                  className="w-4 h-4 rounded-full flex-shrink-0"
                  style={{ backgroundColor: item.color || defaultColors[index % defaultColors.length] }}
                />
                <span className="font-medium text-sm">{item.label}</span>
              </div>
              <div className="text-right">
                <div className="font-bold text-sm">
                  {item.value.toLocaleString('fr-FR')}
                </div>
                <div className="text-xs text-muted-foreground">
                  {item.percentage.toFixed(1)}%
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

// Composant de mini donut chart
interface MiniDonutChartProps {
  data: DonutChartData[];
  size?: number;
  centerText?: string;
  className?: string;
}

export function MiniDonutChart({ 
  data, 
  size = 80,
  centerText,
  className 
}: MiniDonutChartProps) {
  const total = data.reduce((sum, item) => sum + item.value, 0);
  const displayText = centerText || total.toLocaleString('fr-FR');

  return (
    <div className={cn('relative', className)} style={{ width: size, height: size }}>
      <DonutChart
        data={data}
        showTooltip={false}
        showLegend={false}
        innerRadius={size * 0.3}
        outerRadius={size * 0.45}
        centerText={displayText}
        centerSubtext=""
      />
    </div>
  );
}

// Composant de donut chart progressif (pour montrer un pourcentage)
interface ProgressDonutProps {
  value: number;
  max: number;
  label?: string;
  size?: number;
  color?: string;
  backgroundColor?: string;
  className?: string;
  showPercentage?: boolean;
}

export function ProgressDonut({
  value,
  max,
  label = 'Progression',
  size = 120,
  color = 'hsl(var(--chart-1))',
  backgroundColor = 'hsl(var(--muted))',
  className,
  showPercentage = true,
}: ProgressDonutProps) {
  const percentage = Math.min(100, Math.max(0, (value / max) * 100));
  const remaining = max - value;

  const data: DonutChartData[] = [
    {
      label: 'Complété',
      value: value,
      color: color,
    },
    {
      label: 'Restant',
      value: remaining,
      color: backgroundColor,
    },
  ];

  const centerText = showPercentage 
    ? `${percentage.toFixed(1)}%`
    : value.toLocaleString('fr-FR');

  return (
    <div className={cn('flex flex-col items-center space-y-2', className)}>
      <DonutChart
        data={data}
        showTooltip={false}
        showLegend={false}
        innerRadius={size * 0.35}
        outerRadius={size * 0.45}
        centerText={centerText}
        centerSubtext={label}
      />
      
      <div className="text-center">
        <div className="text-sm font-medium">
          {value.toLocaleString('fr-FR')} / {max.toLocaleString('fr-FR')}
        </div>
      </div>
    </div>
  );
}

// Hook pour préparer les données du donut chart
export function useDonutChartData<T>(
  data: T[],
  config: {
    labelKey: keyof T;
    valueKey: keyof T;
    colorKey?: keyof T;
    colors?: string[];
  }
) {
  return useMemo(() => {
    return data.map((item, index) => ({
      label: String(item[config.labelKey]),
      value: Number(item[config.valueKey]),
      color: config.colorKey 
        ? String(item[config.colorKey])
        : config.colors?.[index % (config.colors?.length || defaultColors.length)] 
        || defaultColors[index % defaultColors.length],
    }));
  }, [data, config]);
}

// Utilitaire pour générer des couleurs de donut automatiquement
export function generateDonutColors(count: number, baseHue = 160): string[] {
  const colors: string[] = [];
  const saturation = 65;
  const lightness = 55;
  
  for (let i = 0; i < count; i++) {
    const hue = (baseHue + (i * 360 / count)) % 360;
    colors.push(`hsl(${hue}, ${saturation}%, ${lightness}%)`);
  }
  
  return colors;
}
