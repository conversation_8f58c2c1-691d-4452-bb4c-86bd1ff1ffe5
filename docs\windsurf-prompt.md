# Prompt Windsurf pour KYA Dashboards - Phase 1

## Contexte du Projet

Je développe **KYA Dashboards**, une application Next.js 15 + TypeScript + Supabase + Shadcn/ui pour remplacer les suivis Excel manuels par des dashboards automatisés avec système de double sidebar.

## Architecture Confirmée

- **Next.js 15.4.3** avec App Router
- **Supabase** (déjà configuré dans `utils/supabase/client.ts`)
- **Shadcn/ui** (déjà configuré, voir `components.json`)
- **Features autonomes** : chaque feature avec `components/`, `hooks/`, `services/`, `types/`, `utils/`, `actions.ts`
- **Server Actions uniquement** (pas d'API routes)
- **RBAC applicatif** (pas de RLS Supabase)

## Phase 1 à Implémenter (5-6 jours)

### 1.1 Types TypeScript Globaux
Créer dans `types/` :
- `auth.ts` - Types d'authentification et utilisateurs
- `entities.ts` - Types des entités organisationnelles (directions, équipes)
- `users.ts` - Types de gestion des utilisateurs
- `rbac.ts` - Types pour rôles, permissions, assignations
- `dashboard.ts` - Types pour dashboards et statistiques

### 1.2 Configuration Supabase Server
- Implémenter `utils/supabase/server.ts` (le client existe déjà)
- Créer les utilitaires de connexion serveur pour Server Actions

### 1.3 Services de Base
Créer dans `utils/` :
- `services/audit-service.ts` - Service d'audit trail
- `rbac/guards.ts` - Guards pour vérification des permissions
- `validations/schemas.ts` - Schémas Zod pour validation

### 1.4 Feature Auth Complète
Créer `features/auth/` avec :
- `services/auth-service.ts` - Logique métier authentification
- `components/login-form.tsx` - Formulaire de connexion
- `components/auth-guard.tsx` - Protection des routes
- `actions.ts` - Server Actions pour auth
- `hooks/use-auth.ts` - Hook personnalisé
- `types/index.ts` - Types spécifiques auth
- `utils/validation.ts` - Validation auth

### 1.5 AuthProvider
- `components/providers/auth-provider.tsx` - Contexte global d'authentification
- Intégration dans `app/layout.tsx`

### 1.6 Feature RBAC
Créer `features/rbac/` avec :
- `services/rbac-service.ts` - Logique métier RBAC
- `components/role-list.tsx` - Liste des rôles
- `components/permission-matrix.tsx` - Matrice permissions
- `actions.ts` - Server Actions RBAC
- `hooks/use-rbac.ts` - Hook RBAC
- Structure complète avec types, utils, validation

### 1.7 Base de Données
- Exécuter le schéma SQL de `docs/database-schema.md`
- Créer `seed.sql` avec données de base (rôles, permissions système)

## Contraintes Techniques

1. **Utiliser l'existant** : Supabase et Shadcn/ui déjà configurés
2. **Server Actions uniquement** : Pas d'API routes
3. **Services séparés** : Logique métier dans services, actions légères
4. **RBAC guards** : Vérification permissions dans chaque action
5. **Types stricts** : TypeScript strict avec typage complet
6. **Features autonomes** : Chaque feature complètement indépendante

## Structure Attendue

```
src/
├── types/
│   ├── auth.ts
│   ├── entities.ts
│   ├── users.ts
│   ├── rbac.ts
│   └── dashboard.ts
├── utils/
│   ├── supabase/
│   │   ├── client.ts (existant)
│   │   └── server.ts (à créer)
│   ├── services/
│   │   └── audit-service.ts
│   ├── rbac/
│   │   └── guards.ts
│   └── validations/
│       └── schemas.ts
├── features/
│   ├── auth/
│   │   ├── components/
│   │   ├── services/
│   │   ├── hooks/
│   │   ├── types/
│   │   ├── utils/
│   │   └── actions.ts
│   └── rbac/
│       ├── components/
│       ├── services/
│       ├── hooks/
│       ├── types/
│       ├── utils/
│       └── actions.ts
├── components/
│   └── providers/
│       └── auth-provider.tsx
└── app/
    ├── (auth)/
    │   ├── login/
    │   │   └── page.tsx
    │   └── layout.tsx
    └── layout.tsx (avec AuthProvider)
```

## Demande Spécifique

**Implémente la Phase 1 complète** en respectant cette architecture. Commence par les types TypeScript, puis les services de base, puis les features auth et RBAC.

Assure-toi que :
- Tous les fichiers suivent les patterns définis
- Les Server Actions utilisent les guards RBAC
- L'AuthProvider gère l'état global d'authentification
- La base de données est correctement configurée
- Les types sont cohérents entre toutes les features

**Objectif** : Avoir une base technique solide avec authentification et RBAC fonctionnels pour passer à la Phase 2 (structure dashboard).