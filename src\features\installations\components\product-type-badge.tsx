'use client';

import { Badge } from '@/components/ui/badge';
import { Lightbulb, Zap } from 'lucide-react';
import type { ProductType } from '../types';

interface ProductTypeBadgeProps {
  productType: ProductType;
  className?: string;
}

const PRODUCT_TYPE_CONFIG = {
  'KYA-SoP': {
    label: 'KYA-SoP',
    icon: Zap,
    className: 'bg-kya-primary/10 text-kya-primary hover:bg-kya-primary/20',
  },
  'Lampadaire': {
    label: 'Lampadaire',
    icon: Lightbulb,
    className: 'bg-kya-secondary/10 text-kya-secondary hover:bg-kya-secondary/20',
  },
};

export function ProductTypeBadge({ productType, className }: ProductTypeBadgeProps) {
  const config = PRODUCT_TYPE_CONFIG[productType];
  const Icon = config.icon;

  return (
    <Badge 
      variant="outline"
      className={`flex items-center gap-1 ${config.className} ${className || ''}`}
    >
      <Icon className="h-3 w-3" />
      {config.label}
    </Badge>
  );
}
