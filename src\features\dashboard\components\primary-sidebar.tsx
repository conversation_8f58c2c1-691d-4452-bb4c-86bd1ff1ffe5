'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Home, Settings, Users, User, Shield, Building, PlusCircle, Zap, Wrench, Cog } from 'lucide-react';
import { cn } from '@/lib/utils';
import {
  Sidebar,
  SidebarHeader,
  SidebarContent,
  SidebarMenu,
  SidebarMenuItem,
  SidebarFooter,
  SidebarTrigger,
  useSidebar,
  SidebarMenuButton,
} from '@/components/ui/sidebar';

const navItems = [
  {
    href: '/dashboard',
    label: 'Dashboard',
    icon: Home,
    permission: null,
  },
  {
    label: 'Saisie',
    icon: PlusCircle,
    permission: null,
    subItems: [
        { href: '/saisie/installations', label: 'Installations', icon: Zap, permission: null },
        { href: '/saisie/maintenance', label: 'Maintenance', icon: Wrench, permission: null },
        { href: '/saisie/configuration', label: 'Configuration', icon: Cog, permission: null },
    ]
  },
  {
    label: 'Administration',
    icon: Settings,
    permission: 'admin.access',
    subItems: [
        { href: '/admin/users', label: 'Utilisateurs', icon: Users, permission: 'admin.users.read' },
        { href: '/admin/persons', label: 'Personnes', icon: User, permission: 'admin.persons.read' },
        { href: '/admin/roles', label: 'Rôles', icon: Shield, permission: 'admin.rbac.manage' },
        { href: '/admin/entities', label: 'Entités', icon: Building, permission: 'admin.entities.manage' },
    ]
  },
];

interface PrimarySidebarProps {
  permissions: string[];
}

export function PrimarySidebar({ permissions }: PrimarySidebarProps) {
  const pathname = usePathname();
  const { state } = useSidebar();
  const isOpen = state === 'expanded';

  // Debug: Log permissions
  console.log('🔐 Permissions dans le sidebar:', permissions);

  const hasPermission = (permission: string | null) => {
    if (!permission) return true;
    const hasIt = permissions.includes(permission);
    console.log(`🔍 Vérification permission "${permission}":`, hasIt);
    return hasIt;
  };

  return (
    <Sidebar collapsible="icon" className="bg-sidebar text-sidebar-foreground">
      <SidebarHeader>
        <h2 className={cn('font-bold text-xl', !isOpen && 'hidden')}>KYA</h2>
        <div className={cn(isOpen && 'hidden')}>
            <h2 className="font-bold text-xl">K</h2>
        </div>
      </SidebarHeader>
      <SidebarContent>
        <SidebarMenu>
          {navItems.map(item =>
            hasPermission(item.permission) ? (
              <SidebarMenuItem key={item.label}>
                {item.subItems ? (
                  <div>
                    <span className={cn("flex items-center gap-3 rounded-md px-3 py-2 text-sm font-medium text-muted-foreground", !isOpen && "justify-center")}>
                      <item.icon className="h-4 w-4" />
                      <span className={cn(!isOpen && 'hidden')}>{item.label}</span>
                    </span>
                    <ul className={cn("pl-7 mt-1 space-y-1", !isOpen && 'hidden')}>
                      {item.subItems.map(subItem =>
                        hasPermission(subItem.permission) ? (
                          <li key={subItem.label}>
                              <Link href={subItem.href} className={cn(
                                  "flex items-center gap-3 rounded-md px-3 py-2 text-xs font-medium",
                                  pathname.startsWith(subItem.href)
                                  ? "bg-sidebar-accent text-sidebar-accent-foreground"
                                  : "hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
                              )}>
                                  <subItem.icon className="h-3 w-3" />
                                  {subItem.label}
                              </Link>
                          </li>
                        ) : null
                      )}
                    </ul>
                  </div>
                ) : (
                  <SidebarMenuButton
                    asChild
                    isActive={pathname.startsWith(item.href!)}
                    className={cn(
                        "w-full transition-colors",
                        !isOpen && "justify-center",
                        pathname.startsWith(item.href!)
                        ? "bg-sidebar-primary text-sidebar-primary-foreground"
                        : "hover:bg-sidebar-accent hover:text-sidebar-accent-foreground"
                    )}
                  >
                    <Link href={item.href!}>
                      <item.icon className="h-4 w-4" />
                      <span className={cn(!isOpen && 'hidden')}>{item.label}</span>
                    </Link>
                  </SidebarMenuButton>
                )}
              </SidebarMenuItem>
            ) : null
          )}
        </SidebarMenu>
      </SidebarContent>
      <SidebarFooter>
        <SidebarTrigger />
      </SidebarFooter>
    </Sidebar>
  );
}