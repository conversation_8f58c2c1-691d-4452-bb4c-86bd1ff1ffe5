// Installation Dashboard - KYA Dashboards
'use client';

import { useState } from 'react';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import {
  BarChart3,
  Calendar,
  AlertTriangle,
  Construction,
  Download,
  Plus
} from 'lucide-react';

import { useInstallationKPIs, useClients, useProjects, useInstallations } from '../hooks/use-installations';
import { InstallationFilters, InstallationFilterValues, useInstallationFiltering } from './installation-filters';
import { ApercuTab } from './tabs/apercu-tab';
import { SuiviTab } from './tabs/suivi-tab';
import { AlertesTab } from './tabs/alertes-tab';

// Re-export utility components
export { InstallationStatusBadge } from './installation-status-badge';
export { ProductTypeBadge } from './product-type-badge';
export { ProgressBar } from './progress-bar';





// Interfaces
interface InstallationDashboardProps {
  teamLeaderId?: string;
  entityId?: string;
}



// Composant principal du dashboard
export function InstallationDashboard({ teamLeaderId, entityId }: InstallationDashboardProps) {
  const [activeTab, setActiveTab] = useState('apercu');
  const [filters, setFilters] = useState<InstallationFilterValues>({
    clients: [],
    projects: [],
    types: [],
    statuses: [],
    search: '',
    clientTypes: [],
  });

  // Hooks pour récupérer les données
  const { data: kpis, isLoading: kpisLoading, error } = useInstallationKPIs(teamLeaderId, entityId);
  const { data: installations = [], isLoading: installationsLoading } = useInstallations();
  const { data: clients = [] } = useClients();
  const { data: projects = [] } = useProjects();



  // Gestion des filtres
  const handleFiltersChange = (newFilters: InstallationFilterValues) => {
    setFilters(newFilters);
  };

  const filteredInstallations = useInstallationFiltering(installations, filters);

  const isLoading = kpisLoading || installationsLoading;

  // État de chargement avec design KYA
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-kya-primary/5 via-white to-kya-secondary/5">
        <div className="flex items-center justify-center h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-16 w-16 border-4 border-kya-primary border-t-transparent mx-auto mb-4"></div>
            <p className="text-lg font-medium text-kya-brown">Chargement du dashboard...</p>
            <p className="text-sm text-gray-600">Récupération des données en cours</p>
          </div>
        </div>
      </div>
    );
  }

  // État d'erreur avec design KYA
  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-red-50 via-white to-orange-50">
        <Card className="max-w-md mx-auto mt-20 border-red-200 shadow-lg">
          <CardContent className="pt-6">
            <div className="text-center text-red-600">
              <AlertTriangle className="h-12 w-12 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Erreur de chargement</h3>
              <p className="text-sm">{error.message}</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary/5 via-white to-accent/5">
      <div className="container mx-auto px-4 py-4 space-y-4">
        {/* Header Optimisé */}
        <div className="mb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 gradient-kya-primary rounded-xl shadow-lg">
                <Construction className="h-5 w-5 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-kya-primary">
                  Gestion des Installations
                </h1>
                <p className="text-sm text-muted-foreground">
                  {filteredInstallations.length} installation{filteredInstallations.length > 1 ? 's' : ''} • Direction Technique
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" className="text-xs">
                <Download className="h-3 w-3 mr-1" />
                Export
              </Button>
              <Button size="sm" className="gradient-kya-primary text-white text-xs">
                <Plus className="h-3 w-3 mr-1" />
                Nouvelle Installation
              </Button>
            </div>
          </div>
        </div>

        {/* Filtres d'installations */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
          <div className="relative z-10">
            <InstallationFilters
              installations={installations}
              clients={clients}
              projects={projects}
              onFiltersChange={handleFiltersChange}
              className="mb-2 filters-compact"
            />
            <div className="flex justify-center my-3 z-0">
              <TabsList className="grid grid-cols-3 w-auto bg-gradient-to-r from-white/80 to-muted/40 backdrop-blur-md border border-border/50 rounded-2xl p-1 h-auto shadow-lg">
                <TabsTrigger
                  value="apercu"
                  className="flex items-center space-x-2 px-6 py-3 rounded-xl data-[state=active]:gradient-kya-primary data-[state=active]:text-white data-[state=active]:shadow-lg data-[state=active]:scale-105 transition-all duration-300 hover:bg-kya-primary/10 hover:scale-102 group"
                >
                  <BarChart3 className="h-4 w-4 group-data-[state=active]:animate-pulse" />
                  <span className="font-medium text-sm">APERÇU</span>
                </TabsTrigger>
                <TabsTrigger
                  value="suivi"
                  className="flex items-center space-x-2 px-6 py-3 rounded-xl data-[state=active]:gradient-kya-primary data-[state=active]:text-white data-[state=active]:shadow-lg data-[state=active]:scale-105 transition-all duration-300 hover:bg-kya-primary/10 hover:scale-102 group"
                >
                  <Calendar className="h-4 w-4 group-data-[state=active]:animate-pulse" />
                  <span className="font-medium text-sm">SUIVI</span>
                </TabsTrigger>
                <TabsTrigger
                  value="alertes"
                  className="flex items-center space-x-2 px-6 py-3 rounded-xl data-[state=active]:bg-gradient-to-r data-[state=active]:from-red-500 data-[state=active]:to-orange-500 data-[state=active]:text-white data-[state=active]:shadow-lg data-[state=active]:scale-105 transition-all duration-300 hover:bg-red-500/10 hover:scale-102 group"
                >
                  <AlertTriangle className="h-4 w-4 group-data-[state=active]:animate-pulse" />
                  <span className="font-medium text-sm">ALERTES</span>
                </TabsTrigger>
              </TabsList>
            </div>
          </div>
          
          {/* Contenu des tabs */}
          <TabsContent value="apercu" className="space-y-4">
            <ApercuTab installations={filteredInstallations} />
          </TabsContent>

          <TabsContent value="suivi" className="space-y-4">
            <SuiviTab installations={filteredInstallations} />
          </TabsContent>

          <TabsContent value="alertes" className="space-y-4">
            <AlertesTab installations={filteredInstallations} />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}



