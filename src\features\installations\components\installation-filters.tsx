'use client';

import { useMemo } from 'react';
import { GlobalFilters, GlobalFilterConfig, GlobalFilterValues, useGlobalFilters } from '@/components/dashboard/filters/global-filters';
import { CheckCircle, Clock, AlertTriangle, Pause, XCircle, Wrench } from 'lucide-react';
import type { Installation, Client, Project } from '../types';

interface InstallationFiltersProps {
  installations: Installation[];
  clients?: Client[];
  projects?: Project[];
  onFiltersChange: (filters: InstallationFilterValues) => void;
  className?: string;
}

export interface InstallationFilterValues {
  clients: string[];
  projects: string[];
  types: string[];
  statuses: string[];
  search: string;
  clientTypes: string[];
}

// Configuration des statuts d'installation avec icônes et couleurs
const INSTALLATION_STATUS_OPTIONS = [
  {
    value: 'PLANNING',
    label: 'Planification',
    icon: <Clock className="h-3 w-3" />,
    color: '#6b7280',
    count: 0,
  },
  {
    value: 'IN_PROGRESS',
    label: 'En cours',
    icon: <Wrench className="h-3 w-3" />,
    color: '#3b82f6',
    count: 0,
  },
  {
    value: 'TESTING',
    label: 'Tests',
    icon: <AlertTriangle className="h-3 w-3" />,
    color: '#f59e0b',
    count: 0,
  },
  {
    value: 'COMPLETED',
    label: 'Terminé',
    icon: <CheckCircle className="h-3 w-3" />,
    color: '#10b981',
    count: 0,
  },
  {
    value: 'ON_HOLD',
    label: 'En attente',
    icon: <Pause className="h-3 w-3" />,
    color: '#f59e0b',
    count: 0,
  },
  {
    value: 'CANCELLED',
    label: 'Annulé',
    icon: <XCircle className="h-3 w-3" />,
    color: '#ef4444',
    count: 0,
  },
];

// Configuration des types de produits
const PRODUCT_TYPE_OPTIONS = [
  {
    value: 'KYA-SoP',
    label: 'KYA-SoP',
    count: 0,
  },
  {
    value: 'Lampadaire',
    label: 'Lampadaire',
    count: 0,
  },
];

export function InstallationFilters({
  installations,
  clients = [],
  projects = [],
  onFiltersChange,
  className,
}: InstallationFiltersProps) {
  // Configuration des filtres basée sur les données disponibles
  const filterConfig: GlobalFilterConfig = useMemo(() => {
    // Calculer les compteurs pour chaque option
    const statusCounts = installations.reduce((acc, installation) => {
      acc[installation.status] = (acc[installation.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const typeCounts = installations.reduce((acc, installation) => {
      acc[installation.product_type] = (acc[installation.product_type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const clientCounts = installations.reduce((acc, installation) => {
      if (installation.client_id) {
        acc[installation.client_id] = (acc[installation.client_id] || 0) + 1;
      }
      return acc;
    }, {} as Record<string, number>);

    const projectCounts = installations.reduce((acc, installation) => {
      if (installation.project_id) {
        acc[installation.project_id] = (acc[installation.project_id] || 0) + 1;
      }
      return acc;
    }, {} as Record<string, number>);

    return {
      search: {
        enabled: true,
        placeholder: "Rechercher une installation...",
      },
      clients: {
        enabled: false,
        placeholder: "Tous les clients",
        options: [],
      },
      projects: {
        enabled: projects.length > 0,
        placeholder: "Tous les projets",
        options: projects.map(project => ({
          value: project.id,
          label: project.name,
          count: projectCounts[project.id] || 0,
        })),
      },
      clientTypes: {
        enabled: true,
        placeholder: "Tous les types de client",
        options: [
          { value: 'INDIVIDUAL', label: 'Ménage', count: installations.filter(i => i.client?.type === 'INDIVIDUAL').length },
          { value: 'INSTITUTION', label: 'Institution', count: installations.filter(i => i.client?.type === 'INSTITUTION' || i.client?.type === 'COMPANY').length },
        ]
      },
      types: {
        enabled: true,
        placeholder: "Tous les types",
        options: PRODUCT_TYPE_OPTIONS.map(type => ({
          ...type,
          count: typeCounts[type.value] || 0,
        })),
      },
      statuses: {
        enabled: true,
        placeholder: "Tous les statuts",
        options: INSTALLATION_STATUS_OPTIONS.map(status => ({
          ...status,
          count: statusCounts[status.value] || 0,
        })),
      },
    };
  }, [installations, clients, projects]);

  // Gestion des filtres avec le hook générique
  const { filters, setFilters, reset } = useGlobalFilters();

  // Conversion des filtres globaux vers les filtres d'installation
  const handleFiltersChange = (globalFilters: GlobalFilterValues) => {
    const installationFilters: InstallationFilterValues = {
      clients: globalFilters.clients,
      projects: globalFilters.projects,
      types: globalFilters.types,
      statuses: globalFilters.statuses,
      search: globalFilters.search,
      clientTypes: globalFilters.clientTypes,
    };
    
    onFiltersChange(installationFilters);
    setFilters(globalFilters);
  };

  return (
    <GlobalFilters
      config={filterConfig}
      values={filters}
      onChange={handleFiltersChange}
      className={`${className} py-2`}
      collapsible={true}
      defaultCollapsed={false}
      showActiveCount={true}
      onReset={reset}
    />
  );
}

// Hook pour filtrer les installations basé sur les critères
export function useInstallationFiltering(
  installations: Installation[],
  filters: InstallationFilterValues
) {
  return useMemo(() => {
    return installations.filter(installation => {
      // Filtre par recherche
      if (filters.search.trim()) {
        const searchTerm = filters.search.toLowerCase();
        const matchesSearch = 
          installation.name.toLowerCase().includes(searchTerm) ||
          installation.installation_number?.toLowerCase().includes(searchTerm) ||
          installation.site_location?.toLowerCase().includes(searchTerm) ||
          installation.client?.name?.toLowerCase().includes(searchTerm);
        
        if (!matchesSearch) return false;
      }

      // Filtre par clients
      if (filters.clients.length > 0 && installation.client_id) {
        if (!filters.clients.includes(installation.client_id)) return false;
      }

      if (filters.clientTypes.length > 0 && installation.client?.type) {
        const clientType = (installation.client.type === 'COMPANY' || installation.client.type === 'INDIVIDUAL') ? 'Ménage' : 'Institution';
        const selectedTypes = filters.clientTypes.map((t: string) => {
            if (t === 'INDIVIDUAL') return 'Ménage';
            if (t === 'INSTITUTION') return 'Institution';
            return t;
        });
        if (!selectedTypes.includes(clientType)) return false;
      }

      // Filtre par projets
      if (filters.projects.length > 0 && installation.project_id) {
        if (!filters.projects.includes(installation.project_id)) return false;
      }

      // Filtre par types
      if (filters.types.length > 0) {
        if (!filters.types.includes(installation.product_type)) return false;
      }

      // Filtre par statuts
      if (filters.statuses.length > 0) {
        if (!filters.statuses.includes(installation.status)) return false;
      }

      return true;
    });
  }, [installations, filters]);
}

// Hook pour les statistiques des filtres
export function useInstallationFilterStats(
  installations: Installation[],
  filteredInstallations: Installation[]
) {
  return useMemo(() => {
    return {
      total: installations.length,
      filtered: filteredInstallations.length,
      hidden: installations.length - filteredInstallations.length,
      percentage: installations.length > 0 
        ? Math.round((filteredInstallations.length / installations.length) * 100)
        : 0,
    };
  }, [installations, filteredInstallations]);
}
