// Page Principale des Installations - KYA Dashboards

import { Suspense } from 'react';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Plus, TrendingUp, Clock, CheckCircle, AlertTriangle, Loader2 } from 'lucide-react';
import { InstallationServerService } from '@/features/installations/services/installation-server-service';
import { InstallationsExpandableTable } from '@/features/installations/components/installations-expandable-table';

// Composant pour afficher les statistiques
async function InstallationStats() {
  try {
    const stats = await InstallationServerService.getInstallationStats();
    
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card className="kpi-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Installations</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-primary">{stats.total_installations}</div>
            <p className="text-xs text-muted-foreground">
              Toutes les installations
            </p>
          </CardContent>
        </Card>

        <Card className="kpi-card kpi-card-success">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Terminées</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.completed_installations}</div>
            <p className="text-xs text-muted-foreground">
              Installations complétées
            </p>
          </CardContent>
        </Card>

        <Card className="kpi-card kpi-card-warning">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">En Cours</CardTitle>
            <Clock className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{stats.active_installations}</div>
            <p className="text-xs text-muted-foreground">
              Installations actives
            </p>
          </CardContent>
        </Card>

        <Card className="kpi-card kpi-card-critical">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">En Retard</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{stats.overdue_installations}</div>
            <p className="text-xs text-muted-foreground">
              Installations en retard
            </p>
          </CardContent>
        </Card>
      </div>
    );
  } catch (error) {
    console.error('Error loading installation stats:', error);
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">Erreur lors du chargement des statistiques</p>
      </div>
    );
  }
}

// Fonction pour récupérer les installations avec leurs dernières données de suivi
async function getInstallationsWithLatestTracking() {
  const installations = await InstallationServerService.getInstallations();

  // Pour chaque installation, récupérer le dernier suivi pour avoir le taux global correct
  const installationsWithTracking = await Promise.all(
    installations.map(async (installation) => {
      try {
        const latestTracking = await InstallationServerService.getLatestTracking(installation.id);
        return {
          ...installation,
          global_progress: latestTracking?.global_progress || installation.global_progress || 0,
          // Ajouter les données de progression détaillées si disponibles
          metalwork_progress: latestTracking?.metalwork_progress || 0,
          excavation_progress: latestTracking?.excavation_progress || 0,
          pv_supports_progress: latestTracking?.pv_supports_progress || 0,
          modules_wiring_progress: latestTracking?.modules_wiring_progress || 0,
          pv_inverter_cables_progress: latestTracking?.pv_inverter_cables_progress || 0,
          inverters_wiring_progress: latestTracking?.inverters_wiring_progress || 0,
          batteries_wiring_progress: latestTracking?.batteries_wiring_progress || 0,
          ac_dc_boxes_progress: latestTracking?.ac_dc_boxes_progress || 0,
          load_separation_progress: latestTracking?.load_separation_progress || 0,
          battery_inverter_connection_progress: latestTracking?.battery_inverter_connection_progress || 0,
          grounding_progress: latestTracking?.grounding_progress || 0,
          pole_installation_progress: latestTracking?.pole_installation_progress || 0,
          lamp_installation_progress: latestTracking?.lamp_installation_progress || 0,
        };
      } catch (error) {
        console.error(`Error loading tracking for installation ${installation.id}:`, error);
        return installation;
      }
    })
  );

  return installationsWithTracking;
}

// Composant pour afficher la liste des installations
async function InstallationsList() {
  try {
    const installations = await getInstallationsWithLatestTracking();

    return (
      <Card className="kpi-card">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-xl">Installations</CardTitle>
              <CardDescription>
                Gérez toutes vos installations KYA-SoP et Lampadaires
              </CardDescription>
            </div>
            <Button asChild className="gradient-kya-primary text-white hover:opacity-90">
              <Link href="/saisie/installations/create">
                <Plus className="h-4 w-4 mr-2" />
                Nouvelle Installation
              </Link>
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <InstallationsExpandableTable installations={installations} />
        </CardContent>
      </Card>
    );
  } catch (error) {
    console.error('Error loading installations:', error);
    return (
      <Card className="kpi-card">
        <CardHeader>
          <CardTitle className="text-xl">Installations</CardTitle>
          <CardDescription>
            Gérez toutes vos installations KYA-SoP et Lampadaires
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-muted-foreground">Erreur lors du chargement des installations</p>
            <Button variant="outline" className="mt-4" onClick={() => window.location.reload()}>
              Réessayer
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }
}

// Composants de chargement
function StatsLoading() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      {[...Array(4)].map((_, i) => (
        <Card key={i} className="kpi-card">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <div className="h-4 w-24 bg-muted animate-pulse rounded" />
            <div className="h-4 w-4 bg-muted animate-pulse rounded" />
          </CardHeader>
          <CardContent>
            <div className="h-8 w-16 bg-muted animate-pulse rounded mb-2" />
            <div className="h-3 w-32 bg-muted animate-pulse rounded" />
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

function InstallationsLoading() {
  return (
    <Card className="kpi-card">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <div className="h-6 w-32 bg-muted animate-pulse rounded mb-2" />
            <div className="h-4 w-48 bg-muted animate-pulse rounded" />
          </div>
          <div className="h-10 w-40 bg-muted animate-pulse rounded" />
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="h-16 w-full bg-muted animate-pulse rounded" />
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

export default function InstallationsPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-2">
            <div className="p-2 gradient-kya-primary rounded-lg shadow-kya-card">
              <TrendingUp className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-kya-primary">
                Gestion des Installations
              </h1>
              <p className="text-muted-foreground">
                Suivez et gérez toutes vos installations KYA
              </p>
            </div>
          </div>
          <div className="h-1 w-20 gradient-kya-primary rounded-full"></div>
        </div>

        {/* Statistiques */}
        <Suspense fallback={<StatsLoading />}>
          <InstallationStats />
        </Suspense>

        {/* Liste des installations */}
        <Suspense fallback={<InstallationsLoading />}>
          <InstallationsList />
        </Suspense>
      </div>
    </div>
  );
}
