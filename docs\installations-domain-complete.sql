-- Installation Domain Tables - KYA Dashboards
-- To be added to existing database

-- 1. Shared Tables (if not exist)

-- Table `clients` (shared across all domains)
CREATE TABLE IF NOT EXISTS clients (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    type VARCHAR(50) NOT NULL CHECK (type IN ('INDIVIDUAL',  'INSTITUTION')),
    contact_info JSONB DEFAULT '{}', -- Phone, email, etc.
    address TEXT,
    gps_coordinates TEXT, -- "(latitude, longitude)"
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table `projects` (shared across all domains)
CREATE TABLE IF NOT EXISTS projects (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    client_id UUID NOT NULL REFERENCES clients(id),
    description TEXT,
    start_date DATE,
    end_date DATE,
    budget DECIMAL(15,2),
    status VARCHAR(50) DEFAULT 'ACTIVE' CHECK (status IN ('ACTIVE', 'COMPLETED', 'CANCELLED', 'ON_HOLD')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Installation Domain Tables

-- Main installations table
CREATE TABLE installations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    client_id UUID NOT NULL REFERENCES clients(id),
    project_id UUID REFERENCES projects(id),
    product_type VARCHAR(50) NOT NULL CHECK (product_type IN ('KYA-SoP', 'Lampadaire')),
    
    -- Installation specific fields
    installation_number VARCHAR(50) UNIQUE NOT NULL,
    site_location TEXT NOT NULL,
    gps_coordinates TEXT, -- "(latitude, longitude)"
    
    -- Equipment specifications (conditional based on product_type)
    peak_power VARCHAR(50), -- "10kW" (KYA-SoP only)
    inverter_specs TEXT, -- "2x 8kW" (KYA-SoP only)
    battery_capacity TEXT, -- "2 batteries de 100Ah/51,2V" (KYA-SoP only)
    equipment_description TEXT, -- General equipment description
    
    -- Team composition (references to persons table)
    team_leader_id UUID REFERENCES persons(id),
    team_members UUID[] DEFAULT '{}', -- Array of person IDs
    
    -- Planning
    planned_start_date DATE,
    planned_end_date DATE,
    actual_start_date DATE,
    actual_end_date DATE,
    total_duration_days INTEGER,
    
    -- Reception and delivery
    technical_reception_date DATE,
    technical_reception_status VARCHAR(50) CHECK (technical_reception_status IN ('PENDING', 'APPROVED_WITH_RESERVES', 'APPROVED_WITHOUT_RESERVES', 'REJECTED')),
    technical_reception_notes TEXT,
    
    provisional_reception_date DATE,
    provisional_reception_status VARCHAR(50) CHECK (provisional_reception_status IN ('PENDING', 'APPROVED_WITH_RESERVES', 'APPROVED_WITHOUT_RESERVES', 'REJECTED')),
    provisional_reception_notes TEXT,
    
    general_functioning_state VARCHAR(50) CHECK (general_functioning_state IN ('EXCELLENT', 'GOOD', 'FAIR', 'POOR', 'NOT_FUNCTIONAL')),
    
    -- Status
    status VARCHAR(50) DEFAULT 'PLANNING' CHECK (status IN ('PLANNING', 'IN_PROGRESS', 'TESTING', 'COMPLETED', 'ON_HOLD', 'CANCELLED')),
    
    comments TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID NOT NULL REFERENCES auth_users(id),
    updated_by UUID REFERENCES auth_users(id)
);

-- Daily tracking table for installations
CREATE TABLE installations_tracking (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    installation_id UUID NOT NULL REFERENCES installations(id) ON DELETE CASCADE,
    tracking_date DATE NOT NULL DEFAULT CURRENT_DATE,
    
    -- Common progress fields
    execution_file_progress INTEGER DEFAULT 0 CHECK (execution_file_progress >= 0 AND execution_file_progress <= 100),
    global_progress INTEGER DEFAULT 0 CHECK (global_progress >= 0 AND global_progress <= 100),
    
    -- KYA-SoP specific progress fields (nullable for Lampadaire)
    metalwork_progress INTEGER CHECK (metalwork_progress >= 0 AND metalwork_progress <= 100),
    excavation_progress INTEGER CHECK (excavation_progress >= 0 AND excavation_progress <= 100),
    pv_supports_progress INTEGER CHECK (pv_supports_progress >= 0 AND pv_supports_progress <= 100),
    modules_wiring_progress INTEGER CHECK (modules_wiring_progress >= 0 AND modules_wiring_progress <= 100),
    pv_inverter_cables_progress INTEGER CHECK (pv_inverter_cables_progress >= 0 AND pv_inverter_cables_progress <= 100),
    inverters_wiring_progress INTEGER CHECK (inverters_wiring_progress >= 0 AND inverters_wiring_progress <= 100),
    batteries_wiring_progress INTEGER CHECK (batteries_wiring_progress >= 0 AND batteries_wiring_progress <= 100),
    ac_dc_boxes_progress INTEGER CHECK (ac_dc_boxes_progress >= 0 AND ac_dc_boxes_progress <= 100),
    load_separation_progress INTEGER CHECK (load_separation_progress >= 0 AND load_separation_progress <= 100),
    battery_inverter_connection_progress INTEGER CHECK (battery_inverter_connection_progress >= 0 AND battery_inverter_connection_progress <= 100),
    grounding_progress INTEGER CHECK (grounding_progress >= 0 AND grounding_progress <= 100),
    
    -- Lampadaire specific progress fields (nullable for KYA-SoP)
    pole_installation_progress INTEGER CHECK (pole_installation_progress >= 0 AND pole_installation_progress <= 100),
    lamp_installation_progress INTEGER CHECK (lamp_installation_progress >= 0 AND lamp_installation_progress <= 100),
    
    -- Testing and commissioning
    commissioning_test_status VARCHAR(50) CHECK (commissioning_test_status IN ('NOT_STARTED', 'IN_PROGRESS', 'PASSED_WITH_RESERVES', 'PASSED_WITHOUT_RESERVES', 'FAILED')),
    commissioning_test_notes TEXT,
    commissioning_test_date DATE,
    
    -- Daily tracking fields
    daily_comments TEXT,
    issues_encountered TEXT,
    next_actions TEXT,
    hours_worked DECIMAL(5,2),
    weather_conditions VARCHAR(100),
    
    -- Audit fields
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID NOT NULL REFERENCES auth_users(id),
    updated_by UUID REFERENCES auth_users(id),
    
    -- Unique constraint for one tracking per day per installation
    UNIQUE(installation_id, tracking_date)
);

-- 3. Indexes for performance

-- Installations indexes
CREATE INDEX idx_installations_client_id ON installations(client_id);
CREATE INDEX idx_installations_project_id ON installations(project_id);
CREATE INDEX idx_installations_product_type ON installations(product_type);
CREATE INDEX idx_installations_status ON installations(status);
CREATE INDEX idx_installations_team_leader_id ON installations(team_leader_id);
CREATE INDEX idx_installations_planned_dates ON installations(planned_start_date, planned_end_date);
CREATE INDEX idx_installations_number ON installations(installation_number);

-- Tracking indexes
CREATE INDEX idx_installations_tracking_installation_id ON installations_tracking(installation_id);
CREATE INDEX idx_installations_tracking_date ON installations_tracking(tracking_date);
CREATE INDEX idx_installations_tracking_global_progress ON installations_tracking(global_progress);
CREATE INDEX idx_installations_tracking_created_by ON installations_tracking(created_by);

-- View for latest tracking
CREATE OR REPLACE VIEW latest_installations_tracking AS
SELECT DISTINCT ON (installation_id)
    id,
    installation_id,
    tracking_date,
    global_progress,
    execution_file_progress,
    metalwork_progress,
    excavation_progress,
    pv_supports_progress,
    modules_wiring_progress,
    pv_inverter_cables_progress,
    inverters_wiring_progress,
    batteries_wiring_progress,
    ac_dc_boxes_progress,
    load_separation_progress,
    battery_inverter_connection_progress,
    grounding_progress,
    pole_installation_progress,
    lamp_installation_progress,
    commissioning_test_status,
    created_at
FROM installations_tracking
ORDER BY installation_id, tracking_date DESC;

-- 4. Triggers for automatic updates

-- Update updated_at on installations
CREATE TRIGGER update_installations_updated_at 
    BEFORE UPDATE ON installations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Update updated_at on installations_tracking
CREATE TRIGGER update_installations_tracking_updated_at 
    BEFORE UPDATE ON installations_tracking
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Update updated_at on clients
CREATE TRIGGER update_clients_updated_at 
    BEFORE UPDATE ON clients
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Update updated_at on projects
CREATE TRIGGER update_projects_updated_at 
    BEFORE UPDATE ON projects
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 5. Functions for business logic

-- Function to calculate installation progress based on product type
CREATE OR REPLACE FUNCTION calculate_installation_progress(p_installation_id UUID, p_tracking_date DATE DEFAULT CURRENT_DATE)
RETURNS INTEGER AS $$
DECLARE
    v_product_type VARCHAR(50);
    v_progress INTEGER := 0;
    v_step_count INTEGER := 0;
    v_total_progress INTEGER := 0;
BEGIN
    -- Get product type
    SELECT product_type INTO v_product_type 
    FROM installations 
    WHERE id = p_installation_id;
    
    -- Calculate progress based on product type
    IF v_product_type = 'KYA-SoP' THEN
        SELECT 
            COALESCE(execution_file_progress, 0) +
            COALESCE(metalwork_progress, 0) +
            COALESCE(excavation_progress, 0) +
            COALESCE(pv_supports_progress, 0) +
            COALESCE(modules_wiring_progress, 0) +
            COALESCE(pv_inverter_cables_progress, 0) +
            COALESCE(inverters_wiring_progress, 0) +
            COALESCE(batteries_wiring_progress, 0) +
            COALESCE(ac_dc_boxes_progress, 0) +
            COALESCE(load_separation_progress, 0) +
            COALESCE(battery_inverter_connection_progress, 0) +
            COALESCE(grounding_progress, 0)
        INTO v_total_progress
        FROM installations_tracking
        WHERE installation_id = p_installation_id AND tracking_date = p_tracking_date;
        
        v_step_count := 12; -- Number of KYA-SoP steps
        
    ELSIF v_product_type = 'Lampadaire' THEN
        SELECT 
            COALESCE(execution_file_progress, 0) +
            COALESCE(metalwork_progress, 0) +
            COALESCE(excavation_progress, 0) +
            COALESCE(pole_installation_progress, 0) +
            COALESCE(lamp_installation_progress, 0)
        INTO v_total_progress
        FROM installations_tracking
        WHERE installation_id = p_installation_id AND tracking_date = p_tracking_date;
        
        v_step_count := 5; -- Number of Lampadaire steps
    END IF;
    
    -- Calculate average progress
    IF v_step_count > 0 THEN
        v_progress := v_total_progress / v_step_count;
    END IF;
    
    RETURN LEAST(v_progress, 100); -- Cap at 100%
END;
$$ LANGUAGE plpgsql;

-- Function to get installation statistics for dashboard
CREATE OR REPLACE FUNCTION get_installation_stats(p_team_leader_id UUID DEFAULT NULL, p_entity_id UUID DEFAULT NULL)
RETURNS TABLE(
    total_installations INTEGER,
    active_installations INTEGER,
    completed_installations INTEGER,
    average_progress DECIMAL,
    overdue_installations INTEGER,
    this_month_completed INTEGER
) AS $$
BEGIN
    RETURN QUERY
    WITH installation_data AS (
        SELECT 
            i.*,
            COALESCE(it.global_progress, 0) as current_progress,
            CASE 
                WHEN i.actual_end_date IS NOT NULL THEN 'COMPLETED'
                WHEN i.planned_end_date < CURRENT_DATE AND i.actual_end_date IS NULL THEN 'OVERDUE'
                ELSE 'ACTIVE'
            END as computed_status
        FROM installations i
        LEFT JOIN installations_tracking it ON i.id = it.installation_id 
            AND it.tracking_date = (
                SELECT MAX(tracking_date) 
                FROM installations_tracking 
                WHERE installation_id = i.id
            )
        WHERE (p_team_leader_id IS NULL OR i.team_leader_id = p_team_leader_id)
        AND (p_entity_id IS NULL OR i.team_leader_id IN (
            SELECT person_id FROM entities_persons 
            WHERE entity_id = p_entity_id AND end_date IS NULL
        ))
    )
    SELECT 
        COUNT(*)::INTEGER as total_installations,
        COUNT(CASE WHEN computed_status = 'ACTIVE' THEN 1 END)::INTEGER as active_installations,
        COUNT(CASE WHEN computed_status = 'COMPLETED' THEN 1 END)::INTEGER as completed_installations,
        COALESCE(AVG(current_progress), 0)::DECIMAL as average_progress,
        COUNT(CASE WHEN computed_status = 'OVERDUE' THEN 1 END)::INTEGER as overdue_installations,
        COUNT(CASE WHEN actual_end_date >= DATE_TRUNC('month', CURRENT_DATE) THEN 1 END)::INTEGER as this_month_completed
    FROM installation_data;
END;
$$ LANGUAGE plpgsql;
