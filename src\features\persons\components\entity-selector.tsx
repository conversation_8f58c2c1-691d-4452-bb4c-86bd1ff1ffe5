'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Plus, X, Building, Crown, Calendar } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { Calendar as CalendarComponent } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import { cn } from '@/lib/utils';
import { z } from 'zod';
import { getEntitiesForSelectionAction } from '@/features/entities/actions';
import type { Entity } from '@/types/entities';

// Schema for entity selection in creation mode
const entitySelectionSchema = z.object({
  entityId: z.string().min(1, 'Veuillez sélectionner une entité'),
  roleInEntity: z.string().optional(),
  startDate: z.date().optional(),
  isPrimary: z.boolean(),
});

type EntitySelectionFormValues = z.infer<typeof entitySelectionSchema>;

export interface SelectedEntity {
  entityId: string;
  entityName: string;
  entityCode: string;
  entityType: string;
  roleInEntity?: string;
  startDate?: Date;
  isPrimary: boolean;
}

interface EntitySelectorProps {
  selectedEntities: SelectedEntity[];
  onEntitiesChange: (entities: SelectedEntity[]) => void;
  onClose: () => void;
}

export function EntitySelector({ selectedEntities, onEntitiesChange, onClose }: EntitySelectorProps) {
  const [showAddForm, setShowAddForm] = useState(false);
  const [availableEntities, setAvailableEntities] = useState<Entity[]>([]);
  const [isLoadingEntities, setIsLoadingEntities] = useState(true);

  const form = useForm<EntitySelectionFormValues>({
    resolver: zodResolver(entitySelectionSchema) as any,
    defaultValues: {
      entityId: '',
      roleInEntity: '',
      startDate: new Date(),
      isPrimary: false,
    },
  });

  // Load entities
  useEffect(() => {
    const loadEntities = async () => {
      try {
        const result = await getEntitiesForSelectionAction();
        if (result.data) {
          setAvailableEntities(result.data);
        }
      } catch (error) {
        console.error('Error loading entities:', error);
      } finally {
        setIsLoadingEntities(false);
      }
    };

    loadEntities();
  }, []);

  const getEntityTypeLabel = (type: string) => {
    const labels: Record<string, string> = {
      'DIRECTION': 'Direction',
      'DEPARTEMENT': 'Département',
      'EQUIPE': 'Équipe',
      'SOUS_EQUIPE': 'Sous-équipe',
    };
    return labels[type] || type;
  };

  const handleAddEntity = (values: EntitySelectionFormValues) => {
    const selectedEntity = availableEntities.find(e => e.id === values.entityId);
    if (!selectedEntity) return;

    // Check if entity is already selected
    if (selectedEntities.some(e => e.entityId === values.entityId)) {
      form.setError('entityId', { message: 'Cette entité est déjà sélectionnée' });
      return;
    }

    // If this is set as primary, remove primary from others
    let updatedEntities = selectedEntities;
    if (values.isPrimary) {
      updatedEntities = selectedEntities.map(e => ({ ...e, isPrimary: false }));
    }

    const newEntity: SelectedEntity = {
      entityId: selectedEntity.id,
      entityName: selectedEntity.name,
      entityCode: selectedEntity.code,
      entityType: selectedEntity.type,
      roleInEntity: values.roleInEntity || undefined,
      startDate: values.startDate,
      isPrimary: values.isPrimary,
    };

    onEntitiesChange([...updatedEntities, newEntity]);
    form.reset();
    setShowAddForm(false);
  };

  const handleRemoveEntity = (entityId: string) => {
    onEntitiesChange(selectedEntities.filter(e => e.entityId !== entityId));
  };

  const handleTogglePrimary = (entityId: string) => {
    onEntitiesChange(
      selectedEntities.map(e => ({
        ...e,
        isPrimary: e.entityId === entityId ? !e.isPrimary : false
      }))
    );
  };

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Building className="h-5 w-5" />
            Sélection des entités
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Add entity form */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">Ajouter une entité</CardTitle>
                <Button
                  onClick={() => setShowAddForm(!showAddForm)}
                  size="sm"
                  variant={showAddForm ? "outline" : "default"}
                >
                  {showAddForm ? (
                    <>
                      <X className="h-4 w-4 mr-2" />
                      Annuler
                    </>
                  ) : (
                    <>
                      <Plus className="h-4 w-4 mr-2" />
                      Ajouter
                    </>
                  )}
                </Button>
              </div>
            </CardHeader>

            {showAddForm && (
              <CardContent>
                <Form {...form}>
                  <form onSubmit={form.handleSubmit(handleAddEntity)} className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="entityId"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Entité *</FormLabel>
                            <Select onValueChange={field.onChange} value={field.value}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Sélectionner une entité" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {isLoadingEntities ? (
                                  <SelectItem value="loading" disabled>Chargement...</SelectItem>
                                ) : availableEntities.filter(entity => !selectedEntities.some(s => s.entityId === entity.id)).length === 0 ? (
                                  <SelectItem value="no-entities" disabled>Aucune entité disponible</SelectItem>
                                ) : (
                                  availableEntities
                                    .filter(entity => !selectedEntities.some(s => s.entityId === entity.id))
                                    .map((entity) => (
                                      <SelectItem key={entity.id} value={entity.id}>
                                        <div className="flex items-center gap-2">
                                          <span>{entity.name}</span>
                                          <Badge variant="outline" className="text-xs">
                                            {entity.code}
                                          </Badge>
                                        </div>
                                      </SelectItem>
                                    ))
                                )}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="roleInEntity"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Rôle dans l'entité</FormLabel>
                            <FormControl>
                              <Input placeholder="Ex: Développeur Senior" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="startDate"
                        render={({ field }) => (
                          <FormItem className="flex flex-col">
                            <FormLabel>Date de début</FormLabel>
                            <Popover>
                              <PopoverTrigger asChild>
                                <FormControl>
                                  <Button
                                    variant="outline"
                                    className={cn(
                                      "pl-3 text-left font-normal",
                                      !field.value && "text-muted-foreground"
                                    )}
                                  >
                                    {field.value ? (
                                      format(field.value, "PPP", { locale: fr })
                                    ) : (
                                      <span>Sélectionner une date</span>
                                    )}
                                    <Calendar className="ml-auto h-4 w-4 opacity-50" />
                                  </Button>
                                </FormControl>
                              </PopoverTrigger>
                              <PopoverContent className="w-auto p-0" align="start">
                                <CalendarComponent
                                  mode="single"
                                  selected={field.value}
                                  onSelect={field.onChange}
                                  disabled={(date) => date > new Date()}
                                  initialFocus
                                  locale={fr}
                                />
                              </PopoverContent>
                            </Popover>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="isPrimary"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                            <div className="space-y-0.5">
                              <FormLabel className="text-base">Entité principale</FormLabel>
                              <div className="text-sm text-muted-foreground">
                                Cette entité sera l'entité principale de la personne
                              </div>
                            </div>
                            <FormControl>
                              <Switch
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="flex justify-end gap-2">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => setShowAddForm(false)}
                      >
                        Annuler
                      </Button>
                      <Button type="submit">
                        Ajouter l'entité
                      </Button>
                    </div>
                  </form>
                </Form>
              </CardContent>
            )}
          </Card>

          {/* Selected entities */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Entités sélectionnées</h3>
            {selectedEntities.length > 0 ? (
              selectedEntities.map((entity) => (
                <Card key={entity.entityId}>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <Building className="h-5 w-5 text-muted-foreground" />
                        <div>
                          <div className="flex items-center gap-2">
                            <span className="font-medium">{entity.entityName}</span>
                            <Badge variant="outline" className="text-xs">
                              {getEntityTypeLabel(entity.entityType)}
                            </Badge>
                            <Badge variant="secondary" className="text-xs">
                              {entity.entityCode}
                            </Badge>
                            {entity.isPrimary && (
                              <Badge variant="default" className="text-xs flex items-center gap-1">
                                <Crown className="h-3 w-3" />
                                Principale
                              </Badge>
                            )}
                          </div>
                          {entity.roleInEntity && (
                            <p className="text-sm text-muted-foreground mt-1">
                              Rôle: {entity.roleInEntity}
                            </p>
                          )}
                          {entity.startDate && (
                            <p className="text-sm text-muted-foreground mt-1 flex items-center gap-1">
                              <Calendar className="h-3 w-3" />
                              Début: {format(entity.startDate, "PPP", { locale: fr })}
                            </p>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleTogglePrimary(entity.entityId)}
                        >
                          {entity.isPrimary ? 'Retirer principal' : 'Définir principal'}
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleRemoveEntity(entity.entityId)}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            ) : (
              <Card>
                <CardContent className="py-8 text-center text-muted-foreground">
                  <Building className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>Aucune entité sélectionnée.</p>
                  <p className="text-sm mt-1">
                    Cliquez sur "Ajouter" pour sélectionner des entités.
                  </p>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-2 pt-4 border-t">
            <Button variant="outline" onClick={onClose}>
              Fermer
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
