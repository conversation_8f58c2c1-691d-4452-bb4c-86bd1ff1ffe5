'use client';

import { useState, useEffect, useCallback } from 'react';
import { getPersonsAction, getPersonStatsAction } from '../actions';
import type { 
  PersonWithEntities, 
  PersonFilters, 
  PersonSortOptions, 
  PersonsResponse,
  PersonStatsResponse 
} from '../types';

interface UsePersonsOptions {
  initialFilters?: PersonFilters;
  initialSort?: PersonSortOptions;
  initialPage?: number;
  initialLimit?: number;
  autoFetch?: boolean;
}

interface UsePersonsReturn {
  persons: PersonWithEntities[];
  total: number;
  page: number;
  limit: number;
  isLoading: boolean;
  error: string | null;
  filters: PersonFilters;
  sort: PersonSortOptions;
  stats: PersonStatsResponse | null;
  isLoadingStats: boolean;
  
  // Actions
  fetchPersons: () => Promise<void>;
  fetchStats: () => Promise<void>;
  setFilters: (filters: PersonFilters) => void;
  setSort: (sort: PersonSortOptions) => void;
  setPage: (page: number) => void;
  setLimit: (limit: number) => void;
  resetFilters: () => void;
  refresh: () => Promise<void>;
}

const defaultFilters: PersonFilters = {};
const defaultSort: PersonSortOptions = { field: 'lastName', direction: 'asc' };

export function usePersons(options: UsePersonsOptions = {}): UsePersonsReturn {
  const {
    initialFilters = defaultFilters,
    initialSort = defaultSort,
    initialPage = 1,
    initialLimit = 20,
    autoFetch = true
  } = options;

  // State
  const [persons, setPersons] = useState<PersonWithEntities[]>([]);
  const [total, setTotal] = useState(0);
  const [page, setPageState] = useState(initialPage);
  const [limit, setLimitState] = useState(initialLimit);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFiltersState] = useState<PersonFilters>(initialFilters);
  const [sort, setSortState] = useState<PersonSortOptions>(initialSort);
  const [stats, setStats] = useState<PersonStatsResponse | null>(null);
  const [isLoadingStats, setIsLoadingStats] = useState(false);

  // Fetch persons
  const fetchPersons = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const result = await getPersonsAction(filters, sort, { page, limit });
      
      if (result.error) {
        setError(result.error);
        setPersons([]);
        setTotal(0);
      } else if (result.data) {
        setPersons(result.data.data);
        setTotal(result.data.total);
      }
    } catch (err) {
      setError('Une erreur inattendue s\'est produite.');
      setPersons([]);
      setTotal(0);
    } finally {
      setIsLoading(false);
    }
  }, [filters, sort, page, limit]);

  // Fetch stats
  const fetchStats = useCallback(async () => {
    setIsLoadingStats(true);
    
    try {
      const result = await getPersonStatsAction();
      
      if (result.data) {
        setStats(result.data);
      }
    } catch (err) {
      console.error('Error fetching person stats:', err);
    } finally {
      setIsLoadingStats(false);
    }
  }, []);

  // Actions
  const setFilters = useCallback((newFilters: PersonFilters) => {
    setFiltersState(newFilters);
    setPageState(1); // Reset to first page when filters change
  }, []);

  const setSort = useCallback((newSort: PersonSortOptions) => {
    setSortState(newSort);
    setPageState(1); // Reset to first page when sort changes
  }, []);

  const setPage = useCallback((newPage: number) => {
    setPageState(newPage);
  }, []);

  const setLimit = useCallback((newLimit: number) => {
    setLimitState(newLimit);
    setPageState(1); // Reset to first page when limit changes
  }, []);

  const resetFilters = useCallback(() => {
    setFiltersState(defaultFilters);
    setSortState(defaultSort);
    setPageState(1);
  }, []);

  const refresh = useCallback(async () => {
    await Promise.all([fetchPersons(), fetchStats()]);
  }, [fetchPersons, fetchStats]);

  // Effects
  useEffect(() => {
    if (autoFetch) {
      fetchPersons();
    }
  }, [fetchPersons, autoFetch]);

  useEffect(() => {
    if (autoFetch) {
      fetchStats();
    }
  }, [fetchStats, autoFetch]);

  return {
    persons,
    total,
    page,
    limit,
    isLoading,
    error,
    filters,
    sort,
    stats,
    isLoadingStats,
    
    // Actions
    fetchPersons,
    fetchStats,
    setFilters,
    setSort,
    setPage,
    setLimit,
    resetFilters,
    refresh,
  };
}
