'use client';

import React, { useState } from 'react';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import {
  ChevronDown,
  ChevronRight,
  User,
  Calendar,
  MessageSquare,
  TrendingUp,
  MapPin,
  Building2,
  Phone
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Installation, InstallationTracking } from '../types';


interface TrackingHistoryExpandableTableProps {
  trackingHistory: InstallationTracking[];
  installation: Installation;
}

export function TrackingHistoryExpandableTable({ 
  trackingHistory, 
  installation 
}: TrackingHistoryExpandableTableProps) {
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());

  const toggleRow = (trackingId: string) => {
    const newExpanded = new Set(expandedRows);
    if (newExpanded.has(trackingId)) {
      newExpanded.delete(trackingId);
    } else {
      newExpanded.add(trackingId);
    }
    setExpandedRows(newExpanded);
  };



  if (!trackingHistory || trackingHistory.length === 0) {
    return (
      <Card className="kpi-card shadow-kya-card">
        <CardContent className="text-center py-12">
          <TrendingUp className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">Aucun Historique</h3>
          <p className="text-muted-foreground mb-4">
            Aucun suivi n'a encore été enregistré pour cette installation.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Informations de l'installation */}
      <Card className="kpi-card shadow-kya-card">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5 text-kya-primary" />
            Informations Installation
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center gap-2">
              <Building2 className="h-4 w-4 text-gray-500" />
              <div>
                <p className="text-sm text-gray-500">Numéro</p>
                <p className="font-medium">{installation.installation_number || 'Non défini'}</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <MapPin className="h-4 w-4 text-gray-500" />
              <div>
                <p className="text-sm text-gray-500">Localisation</p>
                <p className="font-medium">{installation.site_location || 'Non définie'}</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Phone className="h-4 w-4 text-gray-500" />
              <div>
                <p className="text-sm text-gray-500">Type de produit</p>
                <p className="font-medium">{installation.product_type || 'Non défini'}</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Table d'historique */}
      <Card className="kpi-card shadow-kya-card">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            Historique des suivis
          </CardTitle>
          <CardDescription>
            Évolution de la progression de l'installation ({trackingHistory.length} entrées)
          </CardDescription>
        </CardHeader>
        <CardContent>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="text-left py-3 px-4 font-semibold text-sm text-gray-600 w-8"></th>
                <th className="text-left py-3 px-4 font-semibold text-sm text-gray-600">Date</th>
                <th className="text-center py-3 px-4 font-semibold text-sm text-gray-600">Progression Globale</th>
                <th className="text-left py-3 px-4 font-semibold text-sm text-gray-600">Saisi par</th>
                <th className="text-left py-3 px-4 font-semibold text-sm text-gray-600">Notes</th>
              </tr>
            </thead>
            <tbody>
              {trackingHistory.map((tracking, index) => (
                <React.Fragment key={tracking.id}>
                  <tr 
                    className={`border-b border-gray-100 hover:bg-gray-50 cursor-pointer transition-colors ${
                      index === 0 ? 'bg-kya-primary/5' : ''
                    }`}
                    onClick={() => toggleRow(tracking.id)}
                  >
                    <td className="py-4 px-4">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 w-6 p-0"
                        onClick={(e) => {
                          e.stopPropagation();
                          toggleRow(tracking.id);
                        }}
                      >
                        {expandedRows.has(tracking.id) ? (
                          <ChevronDown className="h-4 w-4" />
                        ) : (
                          <ChevronRight className="h-4 w-4" />
                        )}
                      </Button>
                    </td>
                    <td className="py-4 px-4">
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-gray-400" />
                        <div>
                          <div className="font-medium text-sm">
                            {format(new Date(tracking.tracking_date), 'dd/MM/yyyy', { locale: fr })}
                          </div>
                          <div className="text-xs text-gray-500">
                            {format(new Date(tracking.created_at), 'HH:mm', { locale: fr })}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      <div className="flex items-center justify-center">
                        <div className="w-24">
                          <Progress value={tracking.global_progress || 0} className="h-2" />
                          <p className="text-center text-sm font-medium mt-1">
                            {tracking.global_progress || 0}%
                          </p>
                        </div>
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4 text-gray-400" />
                        <span className="text-sm font-medium">{tracking.created_by_name || 'N/A'}</span>
                      </div>
                    </td>
                    <td className="py-4 px-4">
                      <div className="max-w-xs">
                        <p className="text-sm text-gray-600 truncate" title={tracking.daily_comments || ''}>
                          {tracking.daily_comments || 'Aucune note'}
                        </p>
                      </div>
                    </td>
                  </tr>
                  
                  {expandedRows.has(tracking.id) && (
                    <tr className="bg-gray-50/50">
                      <td colSpan={5} className="py-6 px-4">
                        <div className="space-y-4">
                          {/* Progression Spécifique selon le Type */}
                          {installation.product_type === 'KYA-SoP' ? (
                            <div>
                              <h4 className="font-medium mb-4">Progression KYA-SoP</h4>
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div className="space-y-4">
                                  <div>
                                    <div className="flex justify-between items-center mb-2">
                                      <span className="text-sm font-medium">Travaux de chaudronnerie</span>
                                      <span className="text-sm font-bold">{tracking.metalwork_progress || 0}%</span>
                                    </div>
                                    <Progress value={tracking.metalwork_progress || 0} className="h-2" />
                                  </div>
                                  <div>
                                    <div className="flex justify-between items-center mb-2">
                                      <span className="text-sm font-medium">Fouilles</span>
                                      <span className="text-sm font-bold">{tracking.excavation_progress || 0}%</span>
                                    </div>
                                    <Progress value={tracking.excavation_progress || 0} className="h-2" />
                                  </div>
                                  <div>
                                    <div className="flex justify-between items-center mb-2">
                                      <span className="text-sm font-medium">Pose des supports PV</span>
                                      <span className="text-sm font-bold">{tracking.pv_supports_progress || 0}%</span>
                                    </div>
                                    <Progress value={tracking.pv_supports_progress || 0} className="h-2" />
                                  </div>
                                  <div>
                                    <div className="flex justify-between items-center mb-2">
                                      <span className="text-sm font-medium">Câblage des modules</span>
                                      <span className="text-sm font-bold">{tracking.modules_wiring_progress || 0}%</span>
                                    </div>
                                    <Progress value={tracking.modules_wiring_progress || 0} className="h-2" />
                                  </div>
                                  <div>
                                    <div className="flex justify-between items-center mb-2">
                                      <span className="text-sm font-medium">Câbles PV-Onduleur</span>
                                      <span className="text-sm font-bold">{tracking.pv_inverter_cables_progress || 0}%</span>
                                    </div>
                                    <Progress value={tracking.pv_inverter_cables_progress || 0} className="h-2" />
                                  </div>
                                  <div>
                                    <div className="flex justify-between items-center mb-2">
                                      <span className="text-sm font-medium">Câblage des onduleurs</span>
                                      <span className="text-sm font-bold">{tracking.inverters_wiring_progress || 0}%</span>
                                    </div>
                                    <Progress value={tracking.inverters_wiring_progress || 0} className="h-2" />
                                  </div>
                                </div>
                                <div className="space-y-4">
                                  <div>
                                    <div className="flex justify-between items-center mb-2">
                                      <span className="text-sm font-medium">Câblage des batteries</span>
                                      <span className="text-sm font-bold">{tracking.batteries_wiring_progress || 0}%</span>
                                    </div>
                                    <Progress value={tracking.batteries_wiring_progress || 0} className="h-2" />
                                  </div>
                                  <div>
                                    <div className="flex justify-between items-center mb-2">
                                      <span className="text-sm font-medium">Boîtiers AC/DC</span>
                                      <span className="text-sm font-bold">{tracking.ac_dc_boxes_progress || 0}%</span>
                                    </div>
                                    <Progress value={tracking.ac_dc_boxes_progress || 0} className="h-2" />
                                  </div>
                                  <div>
                                    <div className="flex justify-between items-center mb-2">
                                      <span className="text-sm font-medium">Séparation des charges</span>
                                      <span className="text-sm font-bold">{tracking.load_separation_progress || 0}%</span>
                                    </div>
                                    <Progress value={tracking.load_separation_progress || 0} className="h-2" />
                                  </div>
                                  <div>
                                    <div className="flex justify-between items-center mb-2">
                                      <span className="text-sm font-medium">Connexion batterie-onduleur</span>
                                      <span className="text-sm font-bold">{tracking.battery_inverter_connection_progress || 0}%</span>
                                    </div>
                                    <Progress value={tracking.battery_inverter_connection_progress || 0} className="h-2" />
                                  </div>
                                  <div>
                                    <div className="flex justify-between items-center mb-2">
                                      <span className="text-sm font-medium">Mise à la terre</span>
                                      <span className="text-sm font-bold">{tracking.grounding_progress || 0}%</span>
                                    </div>
                                    <Progress value={tracking.grounding_progress || 0} className="h-2" />
                                  </div>
                                </div>
                              </div>
                            </div>
                          ) : installation.product_type === 'Lampadaire' ? (
                            <div>
                              <h4 className="font-medium mb-4">Progression Lampadaire</h4>
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div className="space-y-4">
                                  <div>
                                    <div className="flex justify-between items-center mb-2">
                                      <span className="text-sm font-medium">Travaux de chaudronnerie</span>
                                      <span className="text-sm font-bold">{tracking.metalwork_progress || 0}%</span>
                                    </div>
                                    <Progress value={tracking.metalwork_progress || 0} className="h-2" />
                                  </div>
                                  <div>
                                    <div className="flex justify-between items-center mb-2">
                                      <span className="text-sm font-medium">Fouilles</span>
                                      <span className="text-sm font-bold">{tracking.excavation_progress || 0}%</span>
                                    </div>
                                    <Progress value={tracking.excavation_progress || 0} className="h-2" />
                                  </div>
                                </div>
                                <div className="space-y-4">
                                  <div>
                                    <div className="flex justify-between items-center mb-2">
                                      <span className="text-sm font-medium">Installation du mât</span>
                                      <span className="text-sm font-bold">{tracking.pole_installation_progress || 0}%</span>
                                    </div>
                                    <Progress value={tracking.pole_installation_progress || 0} className="h-2" />
                                  </div>
                                  <div>
                                    <div className="flex justify-between items-center mb-2">
                                      <span className="text-sm font-medium">Installation du luminaire</span>
                                      <span className="text-sm font-bold">{tracking.lamp_installation_progress || 0}%</span>
                                    </div>
                                    <Progress value={tracking.lamp_installation_progress || 0} className="h-2" />
                                  </div>
                                </div>
                              </div>
                            </div>
                          ) : null}

                          {/* Commentaire du jour */}
                          {tracking.daily_comments && (
                            <div>
                              <h4 className="text-sm font-semibold text-gray-700 mb-3 flex items-center gap-2">
                                <MessageSquare className="h-4 w-4" />
                                Commentaire du jour
                              </h4>
                              <div className="bg-white rounded-lg p-4 border">
                                <p className="text-sm text-gray-700">{tracking.daily_comments}</p>
                              </div>
                            </div>
                          )}
                        </div>
                      </td>
                    </tr>
                  )}
                </React.Fragment>
              ))}
            </tbody>
          </table>
        </div>
      </CardContent>
    </Card>
  </div>
  );
}
