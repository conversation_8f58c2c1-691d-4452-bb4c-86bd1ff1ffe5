'use client';

import { useState } from 'react';
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  User, 
  Mail, 
  Phone, 
  Calendar, 
  Building, 
  Edit,
  UserCheck,
  UserX,
  MapPin,
  Clock
} from 'lucide-react';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import { PersonEntityManager } from './entity-assignment-manager';
import type { PersonWithEntities } from '../types';

interface PersonCardProps {
  person: PersonWithEntities;
  onClose: () => void;
  onEdit: () => void;
}

export function PersonCard({ person, onClose, onEdit }: PersonCardProps) {
  const [showEntityManager, setShowEntityManager] = useState(false);

  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  const formatDate = (date: Date | string) => {
    return format(new Date(date), "PPP", { locale: fr });
  };

  const activeEntities = person.entities?.filter(e => !e.endDate) || [];
  const inactiveEntities = person.entities?.filter(e => e.endDate) || [];

  return (
    <>
      <Dialog open onOpenChange={onClose}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center justify-between">
              <span>Détails de la personne</span>
              <Button onClick={onEdit} size="sm">
                <Edit className="h-4 w-4 mr-2" />
                Modifier
              </Button>
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-6">
            {/* Header with Avatar and Basic Info */}
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-start gap-4">
                  <Avatar className="h-20 w-20">
                    <AvatarImage src="" alt={person.fullName} />
                    <AvatarFallback className="text-lg">
                      {getInitials(person.firstName, person.lastName)}
                    </AvatarFallback>
                  </Avatar>
                  
                  <div className="flex-1 space-y-2">
                    <div className="flex items-center gap-2">
                      <h2 className="text-2xl font-bold">{person.fullName}</h2>
                      <Badge variant={person.isActive ? 'default' : 'secondary'}>
                        {person.isActive ? (
                          <>
                            <UserCheck className="h-3 w-3 mr-1" />
                            Actif
                          </>
                        ) : (
                          <>
                            <UserX className="h-3 w-3 mr-1" />
                            Inactif
                          </>
                        )}
                      </Badge>
                    </div>
                    
                    {person.position && (
                      <p className="text-lg text-muted-foreground">{person.position}</p>
                    )}
                    
                    {person.primaryEntity && (
                      <div className="flex items-center gap-2 text-sm">
                        <Building className="h-4 w-4" />
                        <span>Entité principale: {person.primaryEntity.name}</span>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Personal Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User className="h-5 w-5" />
                    Informations personnelles
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 gap-3">
                    <div className="flex items-center gap-3">
                      <User className="h-4 w-4 text-muted-foreground" />
                      <div>
                        <p className="text-sm font-medium">Nom complet</p>
                        <p className="text-sm text-muted-foreground">{person.fullName}</p>
                      </div>
                    </div>
                    
                    {person.email && (
                      <div className="flex items-center gap-3">
                        <Mail className="h-4 w-4 text-muted-foreground" />
                        <div>
                          <p className="text-sm font-medium">Email</p>
                          <p className="text-sm text-muted-foreground">{person.email}</p>
                        </div>
                      </div>
                    )}
                    
                    {person.phone && (
                      <div className="flex items-center gap-3">
                        <Phone className="h-4 w-4 text-muted-foreground" />
                        <div>
                          <p className="text-sm font-medium">Téléphone</p>
                          <p className="text-sm text-muted-foreground">{person.phone}</p>
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Professional Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Building className="h-5 w-5" />
                    Informations professionnelles
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 gap-3">
                    {person.employeeId && (
                      <div className="flex items-center gap-3">
                        <User className="h-4 w-4 text-muted-foreground" />
                        <div>
                          <p className="text-sm font-medium">Matricule</p>
                          <p className="text-sm text-muted-foreground">{person.employeeId}</p>
                        </div>
                      </div>
                    )}
                    
                    {person.position && (
                      <div className="flex items-center gap-3">
                        <MapPin className="h-4 w-4 text-muted-foreground" />
                        <div>
                          <p className="text-sm font-medium">Poste</p>
                          <p className="text-sm text-muted-foreground">{person.position}</p>
                        </div>
                      </div>
                    )}
                    
                    {person.hireDate && (
                      <div className="flex items-center gap-3">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        <div>
                          <p className="text-sm font-medium">Date d'embauche</p>
                          <p className="text-sm text-muted-foreground">
                            {formatDate(person.hireDate)}
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Entity Assignments */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span className="flex items-center gap-2">
                    <Building className="h-5 w-5" />
                    Assignations d'entités
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowEntityManager(true)}
                  >
                    <Edit className="h-4 w-4 mr-2" />
                    Gérer
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Active Assignments */}
                {activeEntities.length > 0 && (
                  <div>
                    <h4 className="font-medium text-sm mb-3 flex items-center gap-2">
                      <UserCheck className="h-4 w-4 text-green-600" />
                      Assignations actives ({activeEntities.length})
                    </h4>
                    <div className="space-y-3">
                      {activeEntities.map((assignment) => (
                        <div key={assignment.id} className="border rounded-lg p-4">
                          <div className="flex items-start justify-between">
                            <div className="space-y-1">
                              <div className="flex items-center gap-2">
                                <h5 className="font-medium">{assignment.entity?.name}</h5>
                                {assignment.isPrimary && (
                                  <Badge variant="default" className="text-xs">
                                    Principal
                                  </Badge>
                                )}
                              </div>
                              {assignment.roleInEntity && (
                                <p className="text-sm text-muted-foreground">
                                  Rôle: {assignment.roleInEntity}
                                </p>
                              )}
                              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                                <Clock className="h-3 w-3" />
                                Depuis le {formatDate(assignment.startDate)}
                              </div>
                            </div>
                            <Badge variant="outline" className="text-xs">
                              {assignment.entity?.type}
                            </Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Inactive Assignments */}
                {inactiveEntities.length > 0 && (
                  <>
                    {activeEntities.length > 0 && <Separator />}
                    <div>
                      <h4 className="font-medium text-sm mb-3 flex items-center gap-2">
                        <UserX className="h-4 w-4 text-gray-500" />
                        Assignations passées ({inactiveEntities.length})
                      </h4>
                      <div className="space-y-3">
                        {inactiveEntities.map((assignment) => (
                          <div key={assignment.id} className="border rounded-lg p-4 bg-muted/50">
                            <div className="flex items-start justify-between">
                              <div className="space-y-1">
                                <h5 className="font-medium text-muted-foreground">
                                  {assignment.entity?.name}
                                </h5>
                                {assignment.roleInEntity && (
                                  <p className="text-sm text-muted-foreground">
                                    Rôle: {assignment.roleInEntity}
                                  </p>
                                )}
                                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                                  <Clock className="h-3 w-3" />
                                  Du {formatDate(assignment.startDate)} au{' '}
                                  {assignment.endDate && formatDate(assignment.endDate)}
                                </div>
                              </div>
                              <Badge variant="secondary" className="text-xs">
                                Terminé
                              </Badge>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </>
                )}

                {/* No Assignments */}
                {activeEntities.length === 0 && inactiveEntities.length === 0 && (
                  <div className="text-center py-8 text-muted-foreground">
                    <Building className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>Aucune assignation d'entité</p>
                    <Button
                      variant="outline"
                      size="sm"
                      className="mt-2"
                      onClick={() => setShowEntityManager(true)}
                    >
                      Ajouter une assignation
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Metadata */}
            {person.metadata && Object.keys(person.metadata).length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Informations supplémentaires</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {Object.entries(person.metadata).map(([key, value]) => (
                      <div key={key} className="space-y-1">
                        <p className="text-sm font-medium capitalize">
                          {key.replace(/([A-Z])/g, ' $1').trim()}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                        </p>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Timestamps */}
            <Card>
              <CardHeader>
                <CardTitle>Informations système</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="font-medium">Créé le</p>
                      <p className="text-muted-foreground">{formatDate(person.createdAt)}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="font-medium">Modifié le</p>
                      <p className="text-muted-foreground">{formatDate(person.updatedAt)}</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </DialogContent>
      </Dialog>

      {/* Entity Manager Modal */}
      {showEntityManager && (
        <PersonEntityManager
          person={person}
          onClose={() => setShowEntityManager(false)}
        />
      )}
    </>
  );
}
