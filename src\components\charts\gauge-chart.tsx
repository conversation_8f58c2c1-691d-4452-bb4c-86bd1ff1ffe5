import { useMemo } from 'react';
import { cn } from '@/lib/utils';

export interface GaugeChartData {
  value: number;
  max: number;
  min?: number;
  label?: string;
  unit?: string;
  color?: string;
  segments?: Array<{
    min: number;
    max: number;
    color: string;
    label?: string;
  }>;
}

interface GaugeChartProps {
  data: GaugeChartData;
  size?: number;
  strokeWidth?: number;
  className?: string;
  showValue?: boolean;
  showLabel?: boolean;
  showPercentage?: boolean;
  startAngle?: number;
  endAngle?: number;
  animated?: boolean;
}

const defaultColors = {
  primary: '#1ca18c',
  secondary: '#f99d32',
  success: '#10b981',
  warning: '#f59e0b',
  danger: '#ef4444',
  background: '#f3f4f6',
};

export function GaugeChart({
  data,
  size = 200,
  strokeWidth = 20,
  className,
  showValue = true,
  showLabel = true,
  showPercentage = false,
  startAngle = -90,
  endAngle = 90,
  animated = true,
}: GaugeChartProps) {
  const { value, max, min = 0, label, unit = '', color, segments } = data;
  
  const processedData = useMemo(() => {
    const normalizedValue = Math.max(min, Math.min(max, value));
    const percentage = ((normalizedValue - min) / (max - min)) * 100;
    const angleRange = endAngle - startAngle;
    const valueAngle = startAngle + (percentage / 100) * angleRange;
    
    return {
      normalizedValue,
      percentage,
      valueAngle,
      angleRange,
    };
  }, [value, max, min, startAngle, endAngle]);

  const radius = (size - strokeWidth) / 2;
  const circumference = 2 * Math.PI * radius;
  const center = size / 2;

  // Calcul des coordonnées pour l'arc
  const polarToCartesian = (centerX: number, centerY: number, radius: number, angleInDegrees: number) => {
    const angleInRadians = (angleInDegrees - 90) * Math.PI / 180.0;
    return {
      x: centerX + (radius * Math.cos(angleInRadians)),
      y: centerY + (radius * Math.sin(angleInRadians))
    };
  };

  const describeArc = (x: number, y: number, radius: number, startAngle: number, endAngle: number) => {
    const start = polarToCartesian(x, y, radius, endAngle);
    const end = polarToCartesian(x, y, radius, startAngle);
    const largeArcFlag = endAngle - startAngle <= 180 ? "0" : "1";
    
    return [
      "M", start.x, start.y, 
      "A", radius, radius, 0, largeArcFlag, 0, end.x, end.y
    ].join(" ");
  };

  // Couleur basée sur la valeur ou les segments
  const getValueColor = () => {
    if (color) return color;
    
    if (segments) {
      const segment = segments.find(s => value >= s.min && value <= s.max);
      if (segment) return segment.color;
    }
    
    // Couleur par défaut basée sur le pourcentage
    if (processedData.percentage >= 80) return defaultColors.success;
    if (processedData.percentage >= 60) return defaultColors.primary;
    if (processedData.percentage >= 40) return defaultColors.warning;
    return defaultColors.danger;
  };

  const valueColor = getValueColor();

  if (max <= min) {
    return (
      <div className={cn('flex items-center justify-center', className)} style={{ width: size, height: size }}>
        <div className="text-center text-muted-foreground">
          <div className="text-2xl mb-1">⚠️</div>
          <p className="text-xs">Configuration invalide</p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn('relative flex flex-col items-center', className)}>
      {/* Graphique SVG */}
      <div className="relative" style={{ width: size, height: size }}>
        <svg width={size} height={size} className="transform rotate-0">
          {/* Arc de fond */}
          <path
            d={describeArc(center, center, radius, startAngle, endAngle)}
            fill="none"
            stroke={defaultColors.background}
            strokeWidth={strokeWidth}
            strokeLinecap="round"
          />
          
          {/* Segments colorés (si définis) */}
          {segments && segments.map((segment, index) => {
            const segmentStartAngle = startAngle + ((segment.min - min) / (max - min)) * processedData.angleRange;
            const segmentEndAngle = startAngle + ((segment.max - min) / (max - min)) * processedData.angleRange;
            
            return (
              <path
                key={index}
                d={describeArc(center, center, radius, segmentStartAngle, segmentEndAngle)}
                fill="none"
                stroke={segment.color}
                strokeWidth={strokeWidth / 2}
                strokeLinecap="round"
                opacity={0.3}
              />
            );
          })}
          
          {/* Arc de valeur */}
          <path
            d={describeArc(center, center, radius, startAngle, processedData.valueAngle)}
            fill="none"
            stroke={valueColor}
            strokeWidth={strokeWidth}
            strokeLinecap="round"
            className={animated ? 'transition-all duration-1000 ease-out' : ''}
            style={{
              filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))',
            }}
          />
          
          {/* Indicateur de valeur */}
          <circle
            cx={polarToCartesian(center, center, radius, processedData.valueAngle).x}
            cy={polarToCartesian(center, center, radius, processedData.valueAngle).y}
            r={strokeWidth / 3}
            fill={valueColor}
            className="drop-shadow-sm"
          />
        </svg>
        
        {/* Texte central */}
        <div className="absolute inset-0 flex flex-col items-center justify-center text-center">
          {showValue && (
            <div className="text-2xl font-bold text-foreground">
              {processedData.normalizedValue.toLocaleString('fr-FR')}{unit}
            </div>
          )}
          {showPercentage && (
            <div className="text-sm text-muted-foreground">
              {processedData.percentage.toFixed(1)}%
            </div>
          )}
          {showLabel && label && (
            <div className="text-xs text-muted-foreground mt-1">
              {label}
            </div>
          )}
        </div>
      </div>
      
      {/* Légende des segments */}
      {segments && (
        <div className="mt-4 flex flex-wrap gap-2 justify-center">
          {segments.map((segment, index) => (
            <div key={index} className="flex items-center space-x-1 text-xs">
              <div
                className="w-2 h-2 rounded-full"
                style={{ backgroundColor: segment.color }}
              />
              <span className="text-muted-foreground">
                {segment.label || `${segment.min}-${segment.max}`}
              </span>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

// Composant de gauge semi-circulaire
interface SemiCircleGaugeProps extends Omit<GaugeChartProps, 'startAngle' | 'endAngle'> {}

export function SemiCircleGauge(props: SemiCircleGaugeProps) {
  return (
    <GaugeChart
      {...props}
      startAngle={-90}
      endAngle={90}
    />
  );
}

// Composant de gauge circulaire complète
interface CircleGaugeProps extends Omit<GaugeChartProps, 'startAngle' | 'endAngle'> {}

export function CircleGauge(props: CircleGaugeProps) {
  return (
    <GaugeChart
      {...props}
      startAngle={0}
      endAngle={360}
    />
  );
}

// Composant de mini gauge
interface MiniGaugeProps {
  data: GaugeChartData;
  size?: number;
  className?: string;
}

export function MiniGauge({ 
  data, 
  size = 60, 
  className 
}: MiniGaugeProps) {
  return (
    <GaugeChart
      data={data}
      size={size}
      strokeWidth={size / 8}
      showLabel={false}
      showPercentage={false}
      animated={false}
      className={className}
    />
  );
}

// Hook pour créer des données de gauge avec segments automatiques
export function useGaugeWithSegments(
  value: number,
  max: number,
  min: number = 0,
  segmentCount: number = 3
): GaugeChartData {
  return useMemo(() => {
    const range = max - min;
    const segmentSize = range / segmentCount;
    
    const segments = Array.from({ length: segmentCount }, (_, index) => {
      const segmentMin = min + (index * segmentSize);
      const segmentMax = min + ((index + 1) * segmentSize);
      
      let color = defaultColors.success;
      if (index === 0) color = defaultColors.danger;
      else if (index === 1) color = defaultColors.warning;
      else if (index === segmentCount - 1) color = defaultColors.success;
      
      return {
        min: segmentMin,
        max: segmentMax,
        color,
        label: `${segmentMin.toFixed(0)}-${segmentMax.toFixed(0)}`,
      };
    });
    
    return {
      value,
      max,
      min,
      segments,
    };
  }, [value, max, min, segmentCount]);
}

// Utilitaire pour créer des segments personnalisés
export function createGaugeSegments(
  ranges: Array<{ min: number; max: number; color: string; label?: string }>
): GaugeChartData['segments'] {
  return ranges.map(range => ({
    min: range.min,
    max: range.max,
    color: range.color,
    label: range.label || `${range.min}-${range.max}`,
  }));
}
