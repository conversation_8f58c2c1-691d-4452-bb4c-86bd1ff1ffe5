// Installation Feature Exports - KYA Dashboards

// Types
export type {
  Installation,
  InstallationTracking,
  CreateInstallationData,
  UpdateInstallationData,
  InstallationTrackingData,
  InstallationStats,
  InstallationKPIs,
  InstallationSearchParams,
  ProductType,
  InstallationStatus,
  ReceptionStatus,
  FunctioningState,
  CommissioningTestStatus,
  InstallationFilters,
  DashboardTabConfig,
  InstallationDashboardTab,
  ProgressStep
} from './types';

// Services
export { InstallationService } from './services/installation-service';

// Hooks
export {
  useInstallations,
  useInstallation,
  useInstallationMutations,
  useInstallationTracking,
  useInstallationStats,
  useInstallationKPIs,
  useGenerateInstallationNumber,
  useInstallationRealtime,
  useInstallationForm,
  useInstallationFilters
} from './hooks/use-installations';

// Actions
export {
  createInstallationAction,
  updateInstallationAction,
  deleteInstallationAction,
  updateInstallationTrackingAction,
  generateInstallationNumberAction,
  bulkUpdateInstallationsAction,
  submitInstallationFormAction,
  submitTrackingFormAction
} from './actions';

// Components
export { InstallationDashboard } from './components/installation-dashboard';
export { InstallationOverview } from './components/installation-overview';
export { InstallationDailyTracking } from './components/installation-daily-tracking';
export { InstallationTrackingForm } from './components/installation-tracking-form';
export { InstallationAlerts } from './components/installation-alerts';
export { InstallationTeamManagement } from './components/installation-team-management';
export { InstallationHistory } from './components/installation-history';

// Utility Components
export {
  InstallationStatusBadge,
  ProductTypeBadge,
  ProgressBar
} from './components/installation-dashboard';

// Constants
export { KYA_SOP_STEPS } from './types';
