-- Script pour corriger les erreurs dans le schéma de base de données

-- 1. Supprimer l'index incorrect (person_id n'existe pas dans auth_users)
DROP INDEX IF EXISTS idx_auth_users_person_id;

-- 2. <PERSON><PERSON><PERSON> les index corrects pour user_profiles
CREATE INDEX IF NOT EXISTS idx_user_profiles_auth_user_id ON user_profiles(auth_user_id);
CREATE INDEX IF NOT EXISTS idx_user_profiles_person_id ON user_profiles(person_id);

-- 3. Vérifier la structure des tables principales
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_name IN ('auth_users', 'user_profiles', 'user_roles', 'roles')
AND table_schema = 'public'
ORDER BY table_name, ordinal_position;

-- 4. Vérifier les contraintes de clés étrangères
SELECT 
    tc.table_name, 
    kcu.column_name, 
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name 
FROM information_schema.table_constraints AS tc 
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY' 
AND tc.table_name IN ('auth_users', 'user_profiles', 'user_roles')
ORDER BY tc.table_name;

-- 5. Test de la requête utilisateurs avec jointures
SELECT 
    au.id,
    au.email,
    au.is_active,
    up.display_name,
    r.name as role_name
FROM auth_users au
LEFT JOIN user_profiles up ON au.id = up.auth_user_id
LEFT JOIN user_roles ur ON au.id = ur.user_id AND ur.is_active = true
LEFT JOIN roles r ON ur.role_id = r.id
WHERE au.is_active = true
LIMIT 5;
