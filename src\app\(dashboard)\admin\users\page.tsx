import { UserList } from "@/features/users/components/user-list";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { PlusCircle } from "lucide-react";

export default function AdminUsersPage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Gestion des Utilisateurs</h1>
          <p className="mt-2 text-muted-foreground">
            <PERSON><PERSON><PERSON>, modifiez et gérez les utilisateurs de l'application.
          </p>
        </div>
        <Button>
          <PlusCircle className="mr-2 h-4 w-4" />
          Ajouter un utilisateur
        </Button>
      </div>
      
      <UserList />
    </div>
  );
}