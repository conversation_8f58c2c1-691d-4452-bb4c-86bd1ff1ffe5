import { ReactNode } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { TrendingUp, TrendingDown, Minus } from 'lucide-react';

export type KpiVariant = 'default' | 'success' | 'warning' | 'error' | 'info';

interface KpiCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon?: ReactNode;
  trend?: {
    value: number;
    isPositive: boolean;
    period?: string;
  };
  variant?: KpiVariant;
  className?: string;
  isLoading?: boolean;
  badge?: {
    text: string;
    variant?: 'default' | 'secondary' | 'destructive' | 'outline';
  };
  onClick?: () => void;
}

const variantClasses = {
  default: {
    border: 'border-l-muted-foreground/50',
    iconContainer: 'bg-muted',
    icon: 'text-muted-foreground',
  },
  success: {
    border: 'border-l-success',
    iconContainer: 'bg-success/10',
    icon: 'text-success',
  },
  warning: {
    border: 'border-l-warning',
    iconContainer: 'bg-warning/10',
    icon: 'text-warning',
  },
  error: {
    border: 'border-l-destructive',
    iconContainer: 'bg-destructive/10',
    icon: 'text-destructive',
  },
  info: {
    border: 'border-l-info',
    iconContainer: 'bg-info/10',
    icon: 'text-info',
  },
};

export function KpiCard({
  title,
  value,
  subtitle,
  icon,
  trend,
  variant = 'default',
  className,
  isLoading = false,
  badge,
  onClick,
}: KpiCardProps) {
  const styles = variantClasses[variant];

  if (isLoading) {
    return (
      <Card className={cn('transition-all duration-300', className)}>
        <CardContent className="p-4">
          <div className="flex items-center space-x-4">
            <div className="h-12 w-12 bg-gray-200 rounded-full animate-pulse"></div>
            <div className="animate-pulse space-y-2 flex-1">
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              <div className="h-8 bg-gray-200 rounded w-1/3"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2"></div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const formatValue = (val: string | number) => {
    if (typeof val === 'number') {
      return val.toLocaleString('fr-FR');
    }
    return val;
  };

  return (
    <Card
      className={cn(
        'bg-card transition-shadow duration-300 hover:shadow-md border-l-2',
        styles.border,
        onClick && 'cursor-pointer',
        className
      )}
      onClick={onClick}
    >
      <CardContent className="p-4 flex items-center space-x-4">
        {icon && (
          <div className={cn('w-14 h-14 rounded-full flex items-center justify-center', styles.iconContainer)}>
            <div className={cn('w-7 h-7', styles.icon)}>
              {icon}
            </div>
          </div>
        )}
        <div className="flex-1 space-y-0.5">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm font-medium uppercase tracking-wider text-muted-foreground">
              {title}
            </CardTitle>
            {badge && (
              <Badge variant={badge.variant || 'outline'} className="text-xs">
                {badge.text}
              </Badge>
            )}
          </div>

          <div className="text-4xl font-bold text-foreground">
            {formatValue(value)}
          </div>

          {subtitle && (
            <p className="text-sm text-muted-foreground">{subtitle}</p>
          )}

          {trend && (
            <div className="flex items-center space-x-2 pt-1">
              <div className={cn(
                'flex items-center text-sm font-medium',
                trend.isPositive ? 'text-success' : 'text-destructive'
              )}>
                {trend.isPositive ? (
                  <TrendingUp className="h-4 w-4 mr-1" />
                ) : (
                  <TrendingDown className="h-4 w-4 mr-1" />
                )}
                {Math.abs(trend.value)}%
              </div>
              {trend.period && (
                <span className="text-sm text-muted-foreground">
                  {trend.period}
                </span>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

// Composant pour afficher une grille de KPI cards
interface KpiGridProps {
  kpis: Array<Omit<KpiCardProps, 'className'>>;
  columns?: number;
  className?: string;
  isLoading?: boolean;
}

export function KpiGrid({ 
  kpis, 
  columns = 4, 
  className,
  isLoading = false 
}: KpiGridProps) {
  const gridCols = {
    1: 'grid-cols-1',
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
    5: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5',
    6: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6',
  };

  if (isLoading) {
    return (
      <div className={cn('grid gap-4', gridCols[columns as keyof typeof gridCols], className)}>
        {[...Array(columns)].map((_, i) => (
          <KpiCard
            key={i}
            title=""
            value=""
            isLoading={true}
          />
        ))}
      </div>
    );
  }

  return (
    <div className={cn('grid gap-4', gridCols[columns as keyof typeof gridCols], className)}>
      {kpis.map((kpi, index) => (
        <KpiCard key={index} {...kpi} />
      ))}
    </div>
  );
}

// Composant KPI avec comparaison
interface ComparisonKpiCardProps extends Omit<KpiCardProps, 'trend'> {
  currentValue: number;
  previousValue: number;
  comparisonPeriod: string;
  showPercentage?: boolean;
}

export function ComparisonKpiCard({
  currentValue,
  previousValue,
  comparisonPeriod,
  showPercentage = true,
  ...props
}: ComparisonKpiCardProps) {
  const difference = currentValue - previousValue;
  const percentageChange = previousValue !== 0 
    ? (difference / previousValue) * 100 
    : 0;

  const trend = {
    value: showPercentage ? Math.abs(percentageChange) : Math.abs(difference),
    isPositive: difference >= 0,
    period: comparisonPeriod,
  };

  return (
    <KpiCard
      {...props}
      value={currentValue}
      trend={trend}
    />
  );
}

// Hook pour calculer les KPIs
export function useKpiCalculations<T>(
  data: T[],
  calculations: Record<string, (data: T[]) => number | string>
) {
  const kpis = Object.entries(calculations).reduce((acc, [key, calc]) => {
    acc[key] = calc(data);
    return acc;
  }, {} as Record<string, number | string>);

  return kpis;
}

// Composant KPI avec graphique sparkline (mini graphique)
interface SparklineKpiCardProps extends KpiCardProps {
  sparklineData?: number[];
  sparklineColor?: string;
}

export function SparklineKpiCard({
  sparklineData,
  sparklineColor = '#1ca18c',
  ...props
}: SparklineKpiCardProps) {
  return (
    <div className="relative">
      <KpiCard {...props} />
      {sparklineData && sparklineData.length > 0 && (
        <div className="absolute bottom-2 right-2 w-16 h-8">
          <svg width="100%" height="100%" className="overflow-visible">
            <polyline
              fill="none"
              stroke={sparklineColor}
              strokeWidth="1.5"
              points={sparklineData
                .map((value, index) => {
                  const x = (index / (sparklineData.length - 1)) * 64;
                  const max = Math.max(...sparklineData);
                  const min = Math.min(...sparklineData);
                  const y = 32 - ((value - min) / (max - min)) * 32;
                  return `${x},${y}`;
                })
                .join(' ')}
            />
          </svg>
        </div>
      )}
    </div>
  );
}
