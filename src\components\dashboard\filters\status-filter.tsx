import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';
import { 
  Filter, 
  X, 
  Search, 
  CheckCircle, 
  Clock, 
  AlertTriangle, 
  XCircle,
  Pause,
  Play
} from 'lucide-react';

export interface StatusOption {
  value: string;
  label: string;
  color?: string;
  icon?: React.ReactNode;
  count?: number;
  description?: string;
}

interface StatusFilterProps {
  options: StatusOption[];
  value: string[];
  onChange: (selected: string[]) => void;
  className?: string;
  placeholder?: string;
  showSearch?: boolean;
  showCounts?: boolean;
  multiSelect?: boolean;
  disabled?: boolean;
  maxHeight?: string;
}

const defaultIcons = {
  'ACTIVE': <Play className="h-3 w-3" />,
  'COMPLETED': <CheckCircle className="h-3 w-3" />,
  'PENDING': <Clock className="h-3 w-3" />,
  'ERROR': <XCircle className="h-3 w-3" />,
  'WARNING': <AlertTriangle className="h-3 w-3" />,
  'PAUSED': <Pause className="h-3 w-3" />,
};

const defaultColors = {
  'ACTIVE': '#1ca18c',
  'COMPLETED': '#10b981',
  'PENDING': '#f59e0b',
  'ERROR': '#ef4444',
  'WARNING': '#f97316',
  'PAUSED': '#6b7280',
};

export function StatusFilter({
  options,
  value,
  onChange,
  className,
  placeholder = "Filtrer par statut",
  showSearch = true,
  showCounts = true,
  multiSelect = true,
  disabled = false,
  maxHeight = 'max-h-64',
}: StatusFilterProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  const filteredOptions = options.filter(option =>
    option.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
    option.value.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleOptionToggle = (optionValue: string) => {
    if (!multiSelect) {
      onChange([optionValue]);
      setIsOpen(false);
      return;
    }

    const newValue = value.includes(optionValue)
      ? value.filter(v => v !== optionValue)
      : [...value, optionValue];
    
    onChange(newValue);
  };

  const handleSelectAll = () => {
    if (value.length === filteredOptions.length) {
      onChange([]);
    } else {
      onChange(filteredOptions.map(option => option.value));
    }
  };

  const handleClear = () => {
    onChange([]);
  };

  const getSelectedLabels = () => {
    return options
      .filter(option => value.includes(option.value))
      .map(option => option.label);
  };

  const getOptionIcon = (option: StatusOption) => {
    if (option.icon) return option.icon;
    return defaultIcons[option.value as keyof typeof defaultIcons];
  };

  const getOptionColor = (option: StatusOption) => {
    if (option.color) return option.color;
    return defaultColors[option.value as keyof typeof defaultColors] || '#6b7280';
  };

  return (
    <div className={cn('relative', className)}>
      <Button
        variant="outline"
        onClick={() => setIsOpen(!isOpen)}
        disabled={disabled}
        className={cn(
          'justify-start text-left font-normal',
          value.length === 0 && 'text-muted-foreground'
        )}
      >
        <Filter className="mr-2 h-4 w-4" />
        {value.length === 0 ? (
          placeholder
        ) : value.length === 1 ? (
          getSelectedLabels()[0]
        ) : (
          `${value.length} statut${value.length > 1 ? 's' : ''} sélectionné${value.length > 1 ? 's' : ''}`
        )}
        {value.length > 0 && (
          <X 
            className="ml-auto h-4 w-4 hover:text-red-500" 
            onClick={(e) => {
              e.stopPropagation();
              handleClear();
            }}
          />
        )}
      </Button>

      {isOpen && (
        <Card className="absolute top-full left-0 z-50 mt-2 w-72 shadow-lg">
          <CardContent className="p-4 space-y-4">
            {/* Recherche */}
            {showSearch && (
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Rechercher un statut..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            )}

            {/* Actions */}
            {multiSelect && (
              <div className="flex justify-between">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleSelectAll}
                  className="text-xs"
                >
                  {value.length === filteredOptions.length ? 'Tout désélectionner' : 'Tout sélectionner'}
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsOpen(false)}
                  className="text-xs"
                >
                  Fermer
                </Button>
              </div>
            )}

            {/* Options */}
            <div className={cn('space-y-2 overflow-y-auto', maxHeight)}>
              {filteredOptions.length === 0 ? (
                <p className="text-sm text-muted-foreground text-center py-4">
                  Aucun statut trouvé
                </p>
              ) : (
                filteredOptions.map((option) => (
                  <div
                    key={option.value}
                    className="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50 cursor-pointer"
                    onClick={() => handleOptionToggle(option.value)}
                  >
                    {multiSelect ? (
                      <Checkbox
                        checked={value.includes(option.value)}
                        onChange={() => handleOptionToggle(option.value)}
                      />
                    ) : (
                      <div className="w-4 h-4 flex items-center justify-center">
                        {value.includes(option.value) && (
                          <div className="w-2 h-2 rounded-full bg-kya-primary" />
                        )}
                      </div>
                    )}
                    
                    <div className="flex items-center space-x-2 flex-1">
                      <div 
                        className="flex items-center justify-center w-6 h-6 rounded-full"
                        style={{ 
                          backgroundColor: `${getOptionColor(option)}20`,
                          color: getOptionColor(option)
                        }}
                      >
                        {getOptionIcon(option)}
                      </div>
                      
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">{option.label}</span>
                          {showCounts && option.count !== undefined && (
                            <Badge variant="outline" className="text-xs">
                              {option.count}
                            </Badge>
                          )}
                        </div>
                        {option.description && (
                          <p className="text-xs text-muted-foreground">
                            {option.description}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

// Composant pour afficher les statuts actifs sélectionnés
interface ActiveStatusFiltersProps {
  options: StatusOption[];
  selected: string[];
  onRemove: (value: string) => void;
  className?: string;
}

export function ActiveStatusFilters({ 
  options, 
  selected, 
  onRemove, 
  className 
}: ActiveStatusFiltersProps) {
  if (selected.length === 0) return null;

  return (
    <div className={cn('flex flex-wrap gap-2', className)}>
      {selected.map((value) => {
        const option = options.find(opt => opt.value === value);
        if (!option) return null;

        return (
          <Badge
            key={value}
            variant="secondary"
            className="flex items-center gap-1 pr-1"
            style={{ 
              borderColor: option.color || defaultColors[value as keyof typeof defaultColors],
              color: option.color || defaultColors[value as keyof typeof defaultColors]
            }}
          >
            {option.icon || defaultIcons[value as keyof typeof defaultIcons]}
            <span className="text-xs">{option.label}</span>
            <Button
              variant="ghost"
              size="sm"
              className="h-4 w-4 p-0 hover:bg-transparent"
              onClick={() => onRemove(value)}
            >
              <X className="h-3 w-3" />
            </Button>
          </Badge>
        );
      })}
    </div>
  );
}

// Composant de filtre de statut rapide (boutons uniquement)
interface QuickStatusFilterProps {
  options: StatusOption[];
  value: string[];
  onChange: (selected: string[]) => void;
  className?: string;
  maxVisible?: number;
}

export function QuickStatusFilter({ 
  options, 
  value, 
  onChange, 
  className,
  maxVisible = 4
}: QuickStatusFilterProps) {
  const visibleOptions = options.slice(0, maxVisible);
  const hasMore = options.length > maxVisible;

  const handleToggle = (optionValue: string) => {
    const newValue = value.includes(optionValue)
      ? value.filter(v => v !== optionValue)
      : [...value, optionValue];
    
    onChange(newValue);
  };

  return (
    <div className={cn('flex gap-1 flex-wrap', className)}>
      {visibleOptions.map((option) => (
        <Button
          key={option.value}
          variant={value.includes(option.value) ? 'default' : 'outline'}
          size="sm"
          onClick={() => handleToggle(option.value)}
          className="text-xs px-2 flex items-center gap-1"
          style={value.includes(option.value) ? {
            backgroundColor: option.color || defaultColors[option.value as keyof typeof defaultColors],
            borderColor: option.color || defaultColors[option.value as keyof typeof defaultColors]
          } : {}}
        >
          {option.icon || defaultIcons[option.value as keyof typeof defaultIcons]}
          {option.label}
          {option.count !== undefined && (
            <Badge variant="secondary" className="ml-1 text-xs">
              {option.count}
            </Badge>
          )}
        </Button>
      ))}
      
      {hasMore && (
        <Button variant="outline" size="sm" className="text-xs px-2">
          +{options.length - maxVisible}
        </Button>
      )}
    </div>
  );
}

// Hook pour gérer les filtres de statut
export function useStatusFilter(initialSelected: string[] = []) {
  const [selected, setSelected] = useState<string[]>(initialSelected);

  const toggle = (value: string) => {
    setSelected(prev => 
      prev.includes(value) 
        ? prev.filter(v => v !== value)
        : [...prev, value]
    );
  };

  const select = (value: string) => {
    setSelected([value]);
  };

  const selectMultiple = (values: string[]) => {
    setSelected(values);
  };

  const clear = () => {
    setSelected([]);
  };

  const isSelected = (value: string) => {
    return selected.includes(value);
  };

  return {
    selected,
    setSelected,
    toggle,
    select,
    selectMultiple,
    clear,
    isSelected,
  };
}
