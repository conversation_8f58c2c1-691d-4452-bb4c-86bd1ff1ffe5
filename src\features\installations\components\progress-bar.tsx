'use client';

import { Progress } from '@/components/ui/progress';
import { cn } from '@/lib/utils';

interface ProgressBarProps {
  value: number;
  max?: number;
  className?: string;
  showLabel?: boolean;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'success' | 'warning' | 'danger';
}

const SIZE_CONFIG = {
  sm: 'h-2',
  md: 'h-3',
  lg: 'h-4',
};

const VARIANT_CONFIG = {
  default: 'bg-kya-primary',
  success: 'bg-green-500',
  warning: 'bg-yellow-500',
  danger: 'bg-red-500',
};

export function ProgressBar({ 
  value, 
  max = 100, 
  className, 
  showLabel = true,
  size = 'md',
  variant = 'default'
}: ProgressBarProps) {
  const percentage = Math.min(Math.max((value / max) * 100, 0), 100);
  
  // Determine variant based on percentage if not explicitly set
  const effectiveVariant = variant === 'default' 
    ? percentage >= 80 ? 'success' 
      : percentage >= 60 ? 'default'
      : percentage >= 30 ? 'warning' 
      : 'danger'
    : variant;

  return (
    <div className={cn('w-full', className)}>
      <div className="flex items-center justify-between mb-1">
        {showLabel && (
          <span className="text-sm font-medium text-gray-700">
            {Math.round(percentage)}%
          </span>
        )}
      </div>
      <div className={cn(
        'w-full bg-gray-200 rounded-full overflow-hidden',
        SIZE_CONFIG[size]
      )}>
        <div
          className={cn(
            'h-full transition-all duration-300 ease-in-out rounded-full',
            VARIANT_CONFIG[effectiveVariant]
          )}
          style={{ width: `${percentage}%` }}
        />
      </div>
    </div>
  );
}
