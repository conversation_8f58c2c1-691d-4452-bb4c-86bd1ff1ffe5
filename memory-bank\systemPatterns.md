# System Patterns: kya-dashboards

## Coding Patterns
- **Next.js App Router**: Using the latest App Router pattern with page.tsx and layout.tsx structure
- **TypeScript**: Strict typing for all components and functions
- **React Components**: Functional components with TypeScript typing

## Architecture Patterns
- **Component Structure**: Standard Next.js component hierarchy
- **Styling**: TailwindCSS utility-first approach
- **Font System**: Using Geist fonts with variable font support

## Testing Patterns
- Not yet established

## Deployment Patterns
- Standard Next.js build and deployment process
- Development using Turbopack for improved performance

[2025-07-23 10:17:53] - Initial system patterns documentation
