-- Migration Script: Fix Admin Permissions
-- This script adds missing permissions and ensures SUPER_ADMIN has proper access
-- Run this on your existing database to fix permission issues

BEGIN;

-- 1. Add missing permissions that are used in the code
INSERT INTO permissions (name, display_name, resource, action, is_system_permission) VALUES
('admin.users.read', 'Consulter les utilisateurs (Admin)', 'admin_users', 'read', true),
('admin.users.manage', 'Gérer les utilisateurs (Admin)', 'admin_users', 'manage', true),
('admin.rbac.manage', 'Gérer les rôles et permissions (Admin)', 'admin_rbac', 'manage', true),
('admin.entities.manage', 'G<PERSON>rer les entités (Admin)', 'admin_entities', 'manage', true),
('admin.persons.read', 'Consulter les personnes (Admin)', 'admin_persons', 'read', true),
('admin.persons.manage', '<PERSON><PERSON><PERSON> les personnes (Admin)', 'admin_persons', 'manage', true),
('admin.persons.assign', 'G<PERSON>rer les assignations de personnes (Admin)', 'admin_persons', 'assign', true)
ON CONFLICT (name) DO NOTHING;

-- 2. Ensure SUPER_ADMIN has ALL permissions (including new ones)
-- First, get all existing permissions and assign them to SUPER_ADMIN
INSERT INTO roles_permissions (role_id, permission_id)
SELECT 
    (SELECT id FROM roles WHERE name = 'SUPER_ADMIN'),
    p.id
FROM permissions p
WHERE NOT EXISTS (
    SELECT 1 FROM roles_permissions rp 
    WHERE rp.role_id = (SELECT id FROM roles WHERE name = 'SUPER_ADMIN') 
    AND rp.permission_id = p.id
);

-- 3. Ensure DIRECTOR has the new admin permissions
INSERT INTO roles_permissions (role_id, permission_id)
SELECT
    (SELECT id FROM roles WHERE name = 'DIRECTOR'),
    p.id
FROM permissions p
WHERE p.name IN (
    'admin.users.read',
    'admin.users.manage',
    'admin.entities.manage',
    'admin.persons.read',
    'admin.persons.manage',
    'admin.persons.assign',
    'admin.rbac.manage'
)
ON CONFLICT (role_id, permission_id) DO NOTHING;

-- 4. Verification: Show current permissions for SUPER_ADMIN
SELECT 
    'SUPER_ADMIN Permissions:' as info,
    COUNT(*) as total_permissions
FROM roles_permissions rp
JOIN roles r ON rp.role_id = r.id
WHERE r.name = 'SUPER_ADMIN';

-- 5. Verification: List all SUPER_ADMIN permissions
SELECT 
    r.name as role_name,
    p.name as permission_name,
    p.display_name as permission_display_name
FROM roles_permissions rp
JOIN roles r ON rp.role_id = r.id
JOIN permissions p ON rp.permission_id = p.id
WHERE r.name = 'SUPER_ADMIN'
ORDER BY p.name;

-- 6. Verification: Show permissions count by role
SELECT 
    r.name as role_name,
    r.level,
    COUNT(p.id) as permission_count
FROM roles r
LEFT JOIN roles_permissions rp ON r.id = rp.role_id
LEFT JOIN permissions p ON rp.permission_id = p.id
GROUP BY r.id, r.name, r.level
ORDER BY r.level DESC;

COMMIT;

-- 7. Final check: Test if specific permissions exist
SELECT 
    CASE 
        WHEN EXISTS(SELECT 1 FROM permissions WHERE name = 'admin.users.read') 
        THEN '✓ admin.users.read exists'
        ELSE '✗ admin.users.read missing'
    END as check_1,
    CASE 
        WHEN EXISTS(SELECT 1 FROM permissions WHERE name = 'admin.users.manage') 
        THEN '✓ admin.users.manage exists'
        ELSE '✗ admin.users.manage missing'
    END as check_2,
    CASE 
        WHEN EXISTS(SELECT 1 FROM permissions WHERE name = 'admin.rbac.manage') 
        THEN '✓ admin.rbac.manage exists'
        ELSE '✗ admin.rbac.manage missing'
    END as check_3,
    CASE 
        WHEN EXISTS(SELECT 1 FROM permissions WHERE name = 'admin.entities.manage') 
        THEN '✓ admin.entities.manage exists'
        ELSE '✗ admin.entities.manage missing'
    END as check_4;
