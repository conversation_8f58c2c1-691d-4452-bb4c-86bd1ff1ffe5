// Installation Domain Types - KYA Dashboards

// Enums and Constants
export type ProductType = 'KYA-SoP' | 'Lampadaire';

export type InstallationStatus = 
  | 'PLANNING' 
  | 'IN_PROGRESS' 
  | 'TESTING' 
  | 'COMPLETED' 
  | 'ON_HOLD' 
  | 'CANCELLED';

export type ReceptionStatus = 
  | 'PENDING' 
  | 'APPROVED_WITH_RESERVES' 
  | 'APPROVED_WITHOUT_RESERVES' 
  | 'REJECTED';

export type FunctioningState = 
  | 'EXCELLENT' 
  | 'GOOD' 
  | 'FAIR' 
  | 'POOR' 
  | 'NOT_FUNCTIONAL';

export type CommissioningTestStatus = 
  | 'NOT_STARTED' 
  | 'IN_PROGRESS' 
  | 'PASSED_WITH_RESERVES' 
  | 'PASSED_WITHOUT_RESERVES' 
  | 'FAILED';

// Import shared types from central location
// Client and Project types are now in @/types/shared
import type { Client, Project } from '@/types/shared';

// Main Installation Interface
export interface Installation {
  id: string;
  name: string;
  client_id: string;
  project_id?: string;
  product_type: ProductType;
  
  // Installation specific fields
  installation_number: string;
  site_location: string;
  gps_coordinates?: string;
  
  // Equipment specifications (conditional based on product_type)
  peak_power?: string;              // KYA-SoP only
  inverter_specs?: string;          // KYA-SoP only
  battery_capacity?: string;        // KYA-SoP only
  equipment_description?: string;
  
  // Team composition
  team_leader_id?: string;
  team_members: string[];           // Array of person IDs
  
  // Planning
  planned_start_date?: Date;
  planned_end_date?: Date;
  actual_start_date?: Date;
  actual_end_date?: Date;
  total_duration_days?: number;
  
  // Reception and delivery
  technical_reception_date?: Date;
  technical_reception_status?: ReceptionStatus;
  technical_reception_notes?: string;
  provisional_reception_date?: Date;
  provisional_reception_status?: ReceptionStatus;
  provisional_reception_notes?: string;
  general_functioning_state?: FunctioningState;
  
  // Status
  status: InstallationStatus;
  comments?: string;

  // Progress fields (from latest tracking)
  global_progress?: number;
  metalwork_progress?: number;
  excavation_progress?: number;
  pv_supports_progress?: number;
  modules_wiring_progress?: number;
  pv_inverter_cables_progress?: number;
  inverters_wiring_progress?: number;
  batteries_wiring_progress?: number;
  ac_dc_boxes_progress?: number;
  load_separation_progress?: number;
  battery_inverter_connection_progress?: number;
  grounding_progress?: number;
  pole_installation_progress?: number;
  lamp_installation_progress?: number;

  // Audit fields
  created_at: Date;
  updated_at: Date;
  created_by: string;
  updated_by?: string;

  // Relations (populated when needed)
  client?: Client;
  project?: Project;
  team_leader?: import('@/types/entities').Person;
  latest_tracking?: InstallationTracking | InstallationTracking[];
}

// Installation Tracking Interface
export interface InstallationTracking {
  id: string;
  installation_id: string;
  tracking_date: Date;
  
  // Common progress fields
  execution_file_progress: number;
  global_progress: number;
  
  // KYA-SoP specific progress fields (nullable for Lampadaire)
  metalwork_progress?: number;
  excavation_progress?: number;
  pv_supports_progress?: number;
  modules_wiring_progress?: number;
  pv_inverter_cables_progress?: number;
  inverters_wiring_progress?: number;
  batteries_wiring_progress?: number;
  ac_dc_boxes_progress?: number;
  load_separation_progress?: number;
  battery_inverter_connection_progress?: number;
  grounding_progress?: number;
  
  // Lampadaire specific progress fields (nullable for KYA-SoP)
  pole_installation_progress?: number;
  lamp_installation_progress?: number;
  
  // Testing and commissioning
  commissioning_test_status?: CommissioningTestStatus;
  commissioning_test_notes?: string;
  commissioning_test_date?: Date;
  
  // Daily tracking fields
  daily_comments?: string;
  issues_encountered?: string;
  next_actions?: string;
  hours_worked?: number;
  weather_conditions?: string;
  
  // Audit fields
  created_at: Date;
  updated_at: Date;
  created_by: string;
  updated_by?: string;
}

// Form Data Types
export interface CreateInstallationData {
  name: string;
  client_id: string;
  project_id?: string;
  product_type: ProductType;
  installation_number: string;
  site_location: string;
  gps_coordinates?: string;
  peak_power?: string;
  inverter_specs?: string;
  battery_capacity?: string;
  equipment_description?: string;
  team_leader_id?: string;
  team_members?: string[];
  planned_start_date?: Date;
  planned_end_date?: Date;
  comments?: string;
}

export interface UpdateInstallationData extends Partial<CreateInstallationData> {
  status?: InstallationStatus;
  actual_start_date?: Date;
  actual_end_date?: Date;
  total_duration_days?: number;
  technical_reception_date?: Date;
  technical_reception_status?: ReceptionStatus;
  technical_reception_notes?: string;
  provisional_reception_date?: Date;
  provisional_reception_status?: ReceptionStatus;
  provisional_reception_notes?: string;
  general_functioning_state?: FunctioningState;
}

export interface InstallationTrackingData {
  execution_file_progress?: number;
  global_progress?: number;
  
  // KYA-SoP specific
  metalwork_progress?: number;
  excavation_progress?: number;
  pv_supports_progress?: number;
  modules_wiring_progress?: number;
  pv_inverter_cables_progress?: number;
  inverters_wiring_progress?: number;
  batteries_wiring_progress?: number;
  ac_dc_boxes_progress?: number;
  load_separation_progress?: number;
  battery_inverter_connection_progress?: number;
  grounding_progress?: number;
  
  // Lampadaire specific
  pole_installation_progress?: number;
  lamp_installation_progress?: number;
  
  // Testing
  commissioning_test_status?: CommissioningTestStatus;
  commissioning_test_notes?: string;
  commissioning_test_date?: Date;
  
  // Daily tracking
  daily_comments?: string;
  issues_encountered?: string;
  next_actions?: string;
  hours_worked?: number;
  weather_conditions?: string;
}

// Statistics and Dashboard Types
export interface InstallationStats {
  total_installations: number;
  active_installations: number;
  completed_installations: number;
  average_progress: number;
  overdue_installations: number;
  this_month_completed: number;
}

export interface InstallationKPIs {
  stats: InstallationStats;
  progress_by_type: Record<ProductType, number>;
  team_performance: Array<{
    team_leader_id: string;
    team_leader_name: string;
    installations_count: number;
    average_progress: number;
    completion_rate: number;
  }>;
  recent_completions: Installation[];
  overdue_installations: Installation[];
}

// Progress Step Definitions
export interface ProgressStep {
  key: string;
  label: string;
  category: 'PREPARATION' | 'CONSTRUCTION' | 'INSTALLATION' | 'TESTING' | 'FINALIZATION';
  order: number;
  required_for: ProductType[];
  description?: string;
}

export const KYA_SOP_STEPS: ProgressStep[] = [
  { key: 'execution_file_progress', label: 'Execution File', category: 'PREPARATION', order: 1, required_for: ['KYA-SoP', 'Lampadaire'] },
  { key: 'metalwork_progress', label: 'Metalwork', category: 'CONSTRUCTION', order: 2, required_for: ['KYA-SoP', 'Lampadaire'] },
  { key: 'excavation_progress', label: 'Excavation', category: 'CONSTRUCTION', order: 3, required_for: ['KYA-SoP', 'Lampadaire'] },
  { key: 'pv_supports_progress', label: 'PV Supports', category: 'INSTALLATION', order: 4, required_for: ['KYA-SoP'] },
  { key: 'modules_wiring_progress', label: 'Modules & Wiring', category: 'INSTALLATION', order: 5, required_for: ['KYA-SoP'] },
  { key: 'pv_inverter_cables_progress', label: 'PV-Inverter Cables', category: 'INSTALLATION', order: 6, required_for: ['KYA-SoP'] },
  { key: 'inverters_wiring_progress', label: 'Inverters & Wiring', category: 'INSTALLATION', order: 7, required_for: ['KYA-SoP'] },
  { key: 'batteries_wiring_progress', label: 'Batteries & Wiring', category: 'INSTALLATION', order: 8, required_for: ['KYA-SoP'] },
  { key: 'ac_dc_boxes_progress', label: 'AC-DC Boxes', category: 'INSTALLATION', order: 9, required_for: ['KYA-SoP'] },
  { key: 'load_separation_progress', label: 'Load Separation', category: 'INSTALLATION', order: 10, required_for: ['KYA-SoP'] },
  { key: 'battery_inverter_connection_progress', label: 'Battery-Inverter Connection', category: 'INSTALLATION', order: 11, required_for: ['KYA-SoP'] },
  { key: 'grounding_progress', label: 'Grounding', category: 'INSTALLATION', order: 12, required_for: ['KYA-SoP'] },
  { key: 'pole_installation_progress', label: 'Pole Installation', category: 'INSTALLATION', order: 4, required_for: ['Lampadaire'] },
  { key: 'lamp_installation_progress', label: 'Lamp Installation', category: 'INSTALLATION', order: 5, required_for: ['Lampadaire'] },
];

// Filter and Search Types
export interface InstallationFilters {
  product_type?: ProductType;
  status?: InstallationStatus;
  team_leader_id?: string;
  client_id?: string;
  date_range?: {
    start: Date;
    end: Date;
  };
  progress_range?: {
    min: number;
    max: number;
  };
}

export interface InstallationSearchParams {
  query?: string;
  filters?: InstallationFilters;
  sort_by?: 'name' | 'created_at' | 'planned_start_date' | 'global_progress';
  sort_order?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

// Dashboard Tab Types
export type InstallationDashboardTab = 
  | 'overview' 
  | 'daily-tracking' 
  | 'alerts-decisions' 
  | 'team-management' 
  | 'history-performance';

export interface DashboardTabConfig {
  id: InstallationDashboardTab;
  label: string;
  icon: string;
  permissions: string[];
  component: string;
}
