'use client';


import { KpiCard } from '@/components/dashboard/kpi-card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ResponsiveContainer, LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip } from 'recharts';
import {
  AlertTriangle,
  Clock,
  TrendingDown,
  CheckCircle,
  XCircle,
  Calendar,
  User,
  Eye
} from 'lucide-react';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import { useCalculatedAlerts } from '../../hooks/use-calculated-alerts';
import { Installation } from '../../types';

interface AlertesTabProps {
  installations: Installation[];
}

export function AlertesTab({ installations }: AlertesTabProps) {
  const { alerts, kpis, analytics, isLoading } = useCalculatedAlerts(installations);

  const handleInstallationClick = (installation: Installation) => {
    // Navigation disabled - alerts will show inline details
    console.log('Installation clicked from alert:', installation.name);
  };
  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {[...Array(3)].map((_, i) => (
            <KpiCard
              key={i}
              title=""
              value=""
              isLoading={true}
            />
          ))}
        </div>
        <div className="border rounded-lg bg-card p-6">
          <div className="animate-pulse space-y-3">
            <div className="h-4 bg-muted rounded w-1/3"></div>
            <div className="h-32 bg-muted rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  const getAlertSeverity = (type: string) => {
    switch (type) {
      case 'overdue':
        return { color: 'var(--error)', textColor: 'var(--error-foreground)' };
      case 'stagnant':
        return { color: 'var(--warning)', textColor: 'var(--warning-foreground)' };
      case 'low_progress':
        return { color: 'var(--info)', textColor: 'var(--info-foreground)' };
      default:
        return { color: 'var(--muted)', textColor: 'var(--muted-foreground)' };
    }
  };

  const overdueInstallations = alerts.filter(a => a.type === 'overdue');
  const stagnantInstallations = alerts.filter(a => a.type === 'stagnant');
  const lowProgressInstallations = alerts.filter(a => a.type === 'low_progress');

  return (
    <div className="space-y-4">
      {/* KPI Cards des Alertes */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <KpiCard
          title="En Retard"
          value={kpis.overdue_count}
          subtitle="Dépassent la date prévue"
          icon={<AlertTriangle />}
          variant={kpis.overdue_count > 0 ? "error" : "success"}
        />

        <KpiCard
          title="Sans Mise à Jour"
          value={kpis.stagnant_count}
          subtitle="Plus de 7 jours sans suivi"
          icon={<Clock />}
          variant={kpis.stagnant_count > 0 ? "warning" : "success"}
        />

        <KpiCard
          title="Progression Faible"
          value={kpis.low_progress_count}
          subtitle="Moins de 25% de progression"
          icon={<TrendingDown />}
          variant={kpis.low_progress_count > 0 ? "warning" : "success"}
        />
      </div>

      {/* Liste des Alertes */}
      <div className="border rounded-lg overflow-hidden bg-card">
        <div className="p-3 border-b bg-muted/10">
          <h3 className="font-medium text-sm">Alertes Actives ({alerts.length})</h3>
          <p className="text-xs text-muted-foreground">Toutes les alertes nécessitant une attention particulière</p>
        </div>
        <div className="space-y-3 max-h-[500px] overflow-y-auto p-3">
            {alerts.length === 0 ? (
              <div className="text-center py-12">
                <div className="text-green-400 mb-4">
                  <CheckCircle className="h-16 w-16 mx-auto" />
                </div>
                <p className="text-xl font-medium text-green-600 mb-2">
                  Aucune alerte active
                </p>
                <p className="text-sm text-muted-foreground">
                  Toutes les installations sont dans les temps et progressent normalement
                </p>
              </div>
            ) : (
              alerts.map((alert) => {
                const severity = getAlertSeverity(alert.type);
                
                return (
                  <div
                    key={alert.id}
                    className="p-3 rounded-lg border-l-4 bg-card transition-all duration-200 hover:shadow-sm cursor-pointer"
                    style={{ borderLeftColor: severity.color }}
                    onClick={() => handleInstallationClick(alert.installation)}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center space-x-2">
                            <div className="w-1.5 h-1.5 rounded-full" style={{ backgroundColor: severity.color }}></div>
                            <h4 className="font-medium text-sm" style={{ color: severity.textColor }}>
                              {alert.title}
                            </h4>
                          </div>
                          <Badge variant="outline" className="text-xs px-2 py-0.5">
                            {alert.severity === 'high' ? 'Critique' :
                             alert.severity === 'medium' ? 'Moyen' : 'Faible'}
                          </Badge>
                        </div>

                        <p className="text-xs text-muted-foreground mb-2">
                          {alert.description}
                        </p>

                        <div className="flex items-center space-x-3 text-xs text-muted-foreground">
                          <div className="flex items-center space-x-1">
                            <User className="h-3 w-3" />
                            <span>{alert.installation.client?.name || 'Client non défini'}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Calendar className="h-3 w-3" />
                            <span>
                              {alert.date ? format(new Date(alert.date), 'dd/MM/yyyy', { locale: fr }) : 'Date inconnue'}
                            </span>
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-1 ml-3">
                        <Button variant="ghost" size="sm" className="h-7 w-7 p-0 text-success hover:text-success/90">
                          <CheckCircle className="h-3 w-3" />
                        </Button>
                        <Button variant="ghost" size="sm" className="h-7 w-7 p-0 text-destructive hover:text-destructive/90">
                          <XCircle className="h-3 w-3" />
                        </Button>
                        <Button variant="ghost" size="sm" className="h-7 w-7 p-0">
                          <Eye className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </div>
                );
              })
            )}
        </div>
      </div>

      {/* Graphiques d'analyse */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Actions Recommandées */}
        {alerts.length > 0 && (
          <div className="border rounded-lg overflow-hidden bg-card">
            <div className="p-3 border-b bg-muted/10">
              <h3 className="font-medium text-sm">Actions Recommandées</h3>
              <p className="text-xs text-muted-foreground">Suggestions pour résoudre les alertes actives</p>
            </div>
            <div className="space-y-3 p-3">
              {overdueInstallations.length > 0 && (
                <div className="p-3 bg-destructive/10 rounded-lg border border-destructive/20">
                  <div className="flex items-center space-x-2 mb-2">
                    <AlertTriangle className="h-4 w-4 text-destructive" />
                    <h4 className="font-medium text-sm text-destructive-foreground">
                      Installations en retard ({overdueInstallations.length})
                    </h4>
                  </div>
                  <p className="text-xs text-destructive-foreground/90 mb-2">
                    Contactez immédiatement les équipes responsables pour accélérer les travaux.
                  </p>
                  <Button size="sm" variant="destructive" className="h-7 text-xs">
                    Voir les détails
                  </Button>
                </div>
              )}

              {stagnantInstallations.length > 0 && (
                <div className="p-3 bg-warning/10 rounded-lg border border-warning/20">
                  <div className="flex items-center space-x-2 mb-2">
                    <Clock className="h-4 w-4 text-warning" />
                    <h4 className="font-medium text-sm text-warning-foreground">
                      Mises à jour manquantes ({stagnantInstallations.length})
                    </h4>
                  </div>
                  <p className="text-xs text-warning-foreground/90 mb-2">
                    Demandez un rapport de progression aux équipes terrain.
                  </p>
                  <Button size="sm" variant="outline" className="border-warning text-warning hover:bg-warning/10 h-7 text-xs">
                    Envoyer rappel
                  </Button>
                </div>
              )}

              {lowProgressInstallations.length > 0 && (
                <div className="p-3 bg-info/10 rounded-lg border border-info/20">
                  <div className="flex items-center space-x-2 mb-2">
                    <TrendingDown className="h-4 w-4 text-info" />
                    <h4 className="font-medium text-sm text-info-foreground">
                      Progression lente ({lowProgressInstallations.length})
                    </h4>
                  </div>
                  <p className="text-xs text-info-foreground/90 mb-2">
                    Analysez les blocages potentiels et renforcez les équipes si nécessaire.
                  </p>
                  <Button size="sm" variant="outline" className="border-info text-info hover:bg-info/10 h-7 text-xs">
                    Analyser
                  </Button>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Évolution des alertes */}
        <div className="border rounded-lg overflow-hidden bg-card">
          <div className="p-3 border-b bg-muted/10">
            <h3 className="font-medium text-sm">Évolution des Alertes</h3>
            <p className="text-xs text-muted-foreground">Tendance sur les 30 derniers jours</p>
          </div>
          <div className="p-3">
            {analytics.evolutionData.length > 0 ? (
              <ResponsiveContainer width="100%" height={200}>
                <LineChart data={analytics.evolutionData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis />
                  <Tooltip />
                  <Line type="monotone" dataKey="count" stroke="#f99d32" strokeWidth={2} />
                </LineChart>
              </ResponsiveContainer>
            ) : (
              <div className="flex items-center justify-center h-[200px] text-gray-500">
                <div className="text-center">
                  <CheckCircle className="h-10 w-10 mx-auto mb-2 text-green-400" />
                  <p className="text-xs">Aucune donnée d'évolution</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
