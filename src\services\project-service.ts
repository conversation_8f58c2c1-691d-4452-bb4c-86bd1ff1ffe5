// Project Service - KYA Dashboards
// Shared service for project management across all domains

import { createClient as createSupabaseClient } from '@/utils/supabase/client';
import type { Project, CreateProjectData, UpdateProjectData } from '@/types/shared';

export class ProjectService {
  
  static async getAllProjects(): Promise<Project[]> {
    const supabase = createSupabaseClient();
    
    const { data, error } = await supabase
      .from('projects')
      .select(`
        *,
        client:clients(*)
      `)
      .order('name');
    
    if (error) throw error;
    return data || [];
  }
  
  static async getProjectsByClient(clientId: string): Promise<Project[]> {
    const supabase = createSupabaseClient();
    
    const { data, error } = await supabase
      .from('projects')
      .select(`
        *,
        client:clients(*)
      `)
      .eq('client_id', clientId)
      .order('name');
    
    if (error) throw error;
    return data || [];
  }
  
  static async getProjectById(id: string): Promise<Project | null> {
    const supabase = createSupabaseClient();
    
    const { data, error } = await supabase
      .from('projects')
      .select(`
        *,
        client:clients(*)
      `)
      .eq('id', id)
      .single();
    
    if (error) {
      if (error.code === 'PGRST116') return null; // Not found
      throw error;
    }
    
    return data;
  }
  
  static async createProject(data: CreateProjectData): Promise<Project> {
    const supabase = createSupabaseClient();
    
    const { data: project, error } = await supabase
      .from('projects')
      .insert({
        ...data,
        status: data.status || 'ACTIVE',
      })
      .select(`
        *,
        client:clients(*)
      `)
      .single();
    
    if (error) throw error;
    return project;
  }
  
  static async updateProject(id: string, data: UpdateProjectData): Promise<Project> {
    const supabase = createSupabaseClient();
    
    const { data: project, error } = await supabase
      .from('projects')
      .update({
        ...data,
        updated_at: new Date().toISOString(),
      })
      .eq('id', id)
      .select(`
        *,
        client:clients(*)
      `)
      .single();
    
    if (error) throw error;
    return project;
  }
  
  static async deleteProject(id: string): Promise<void> {
    const supabase = createSupabaseClient();
    
    const { error } = await supabase
      .from('projects')
      .delete()
      .eq('id', id);
    
    if (error) throw error;
  }
  
  static async searchProjects(query: string, clientId?: string): Promise<Project[]> {
    const supabase = createSupabaseClient();
    
    let queryBuilder = supabase
      .from('projects')
      .select(`
        *,
        client:clients(*)
      `)
      .or(`name.ilike.%${query}%,description.ilike.%${query}%`)
      .order('name')
      .limit(20);
    
    if (clientId) {
      queryBuilder = queryBuilder.eq('client_id', clientId);
    }
    
    const { data, error } = await queryBuilder;
    
    if (error) throw error;
    return data || [];
  }
}

// Convenience functions for direct use
export const createProject = ProjectService.createProject;
export const updateProject = ProjectService.updateProject;
export const deleteProject = ProjectService.deleteProject;
export const getProjectById = ProjectService.getProjectById;
export const getAllProjects = ProjectService.getAllProjects;
export const getProjectsByClient = ProjectService.getProjectsByClient;
export const searchProjects = ProjectService.searchProjects;
