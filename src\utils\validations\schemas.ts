// Zod Validation Schemas
import { z } from 'zod';

// Base schemas
export const uuidSchema = z.string().uuid('Invalid UUID format');
export const emailSchema = z.string().email('Invalid email format');
export const phoneSchema = z.string().regex(/^[\+]?[1-9][\d]{0,15}$/, 'Invalid phone number format').optional();
export const passwordSchema = z.string().min(8, 'Password must be at least 8 characters').max(128, 'Password too long');

// Auth schemas
export const loginSchema = z.object({
  email: emailSchema,
  password: z.string().min(1, 'Password is required'),
  rememberMe: z.boolean().optional(),
});

export const passwordResetSchema = z.object({
  email: emailSchema,
});

export const passwordChangeSchema = z.object({
  currentPassword: z.string().min(1, 'Current password is required'),
  newPassword: passwordSchema,
  confirmPassword: z.string(),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

export const userProfileSchema = z.object({
  displayName: z.string().min(1, 'Display name is required').max(255, 'Display name too long').optional(),
  avatarUrl: z.string().url('Invalid avatar URL').optional(),
  bio: z.string().max(1000, 'Bio too long').optional(),
  preferences: z.object({
    theme: z.enum(['light', 'dark', 'system']).optional(),
    language: z.string().optional(),
    timezone: z.string().optional(),
    dateFormat: z.string().optional(),
    numberFormat: z.string().optional(),
  }).optional(),
  notificationSettings: z.object({
    email: z.boolean().optional(),
    push: z.boolean().optional(),
    inApp: z.boolean().optional(),
    alerts: z.boolean().optional(),
    reports: z.boolean().optional(),
  }).optional(),
  dashboardConfig: z.record(z.string(), z.any()).optional(),
});

// Entity schemas
export const entityTypeSchema = z.enum(['DIRECTION', 'EQUIPE', 'SOUS_EQUIPE', 'DEPARTEMENT']);

export const createEntitySchema = z.object({
  name: z.string().min(1, 'Name is required').max(255, 'Name too long'),
  type: entityTypeSchema,
  parentId: uuidSchema.optional(),
  managerId: uuidSchema.optional(),
  description: z.string().max(1000, 'Description too long').optional(),
  code: z.string().min(1, 'Code is required').max(50, 'Code too long'),
  metadata: z.record(z.string(), z.any()).optional(),
});

export const updateEntitySchema = z.object({
  name: z.string().min(1, 'Name is required').max(255, 'Name too long').optional(),
  parentId: uuidSchema.optional(),
  managerId: uuidSchema.optional(),
  description: z.string().max(1000, 'Description too long').optional(),
  code: z.string().min(1, 'Code is required').max(50, 'Code too long').optional(),
  isActive: z.boolean().optional(),
  metadata: z.record(z.string(), z.any()).optional(),
});

export const createPersonSchema = z.object({
  firstName: z.string().min(1, 'First name is required').max(100, 'First name too long'),
  lastName: z.string().min(1, 'Last name is required').max(100, 'Last name too long'),
  email: emailSchema.optional(),
  phone: phoneSchema,
  employeeId: z.string().max(50, 'Employee ID too long').optional(),
  position: z.string().max(100, 'Position too long').optional(),
  hireDate: z.date().optional(),
  metadata: z.record(z.string(), z.any()).optional(),
});

export const updatePersonSchema = z.object({
  firstName: z.string().min(1, 'First name is required').max(100, 'First name too long').optional(),
  lastName: z.string().min(1, 'Last name is required').max(100, 'Last name too long').optional(),
  email: emailSchema.optional(),
  phone: phoneSchema,
  employeeId: z.string().max(50, 'Employee ID too long').optional(),
  position: z.string().max(100, 'Position too long').optional(),
  hireDate: z.date().optional(),
  isActive: z.boolean().optional(),
  metadata: z.record(z.string(), z.any()).optional(),
});

export const assignPersonToEntitySchema = z.object({
  entityId: uuidSchema,
  personId: uuidSchema,
  roleInEntity: z.string().max(100, 'Role too long').optional(),
  startDate: z.date().optional(),
  endDate: z.date().optional(),
  isPrimary: z.boolean().optional(),
});

// User schemas
export const createUserSchema = z.object({
  email: emailSchema,
  password: passwordSchema.optional(),
  personId: uuidSchema.optional(),
  profile: userProfileSchema.optional(),
  roles: z.array(uuidSchema).optional(),
  entities: z.array(uuidSchema).optional(),
  sendInvitation: z.boolean().optional(),
});

export const updateUserSchema = z.object({
  email: emailSchema.optional(),
  personId: uuidSchema.optional(),
  isActive: z.boolean().optional(),
  profile: userProfileSchema.optional(),
});

export const userAccessLevelSchema = z.enum(['READ', 'WRITE', 'ADMIN', 'OWNER']);

export const assignRoleSchema = z.object({
  userId: uuidSchema,
  roleId: uuidSchema,
  entityId: uuidSchema.optional(),
  expiresAt: z.date().optional(),
});

export const grantPermissionSchema = z.object({
  userId: uuidSchema,
  permissionId: uuidSchema,
  entityId: uuidSchema.optional(),
  expiresAt: z.date().optional(),
});

export const assignEntitySchema = z.object({
  userId: uuidSchema,
  entityId: uuidSchema,
  accessLevel: userAccessLevelSchema,
});

// RBAC schemas
export const permissionActionSchema = z.enum(['create', 'read', 'update', 'delete', 'manage', 'execute', 'approve']);

export const createRoleSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name too long'),
  displayName: z.string().min(1, 'Display name is required').max(255, 'Display name too long'),
  description: z.string().max(1000, 'Description too long').optional(),
  level: z.number().int().min(0).max(100).optional(),
  permissions: z.array(uuidSchema).optional(),
});

export const updateRoleSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name too long').optional(),
  displayName: z.string().min(1, 'Display name is required').max(255, 'Display name too long').optional(),
  description: z.string().max(1000, 'Description too long').optional(),
  level: z.number().int().min(0).max(100).optional(),
  isActive: z.boolean().optional(),
});

export const createPermissionSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name too long'),
  displayName: z.string().min(1, 'Display name is required').max(255, 'Display name too long'),
  description: z.string().max(1000, 'Description too long').optional(),
  resource: z.string().min(1, 'Resource is required').max(100, 'Resource too long'),
  action: permissionActionSchema,
});

export const updatePermissionSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name too long').optional(),
  displayName: z.string().min(1, 'Display name is required').max(255, 'Display name too long').optional(),
  description: z.string().max(1000, 'Description too long').optional(),
  resource: z.string().min(1, 'Resource is required').max(100, 'Resource too long').optional(),
  action: permissionActionSchema.optional(),
});

export const assignPermissionToRoleSchema = z.object({
  roleId: uuidSchema,
  permissionId: uuidSchema,
});

// Dashboard schemas
export const dashboardTypeSchema = z.enum(['OVERVIEW', 'DIRECTOR', 'MANAGER', 'TEAM', 'PERSONAL', 'CUSTOM']);
export const widgetTypeSchema = z.enum(['CHART', 'TABLE', 'METRIC', 'PROGRESS', 'ALERT', 'LIST', 'CALENDAR', 'MAP', 'TEXT', 'IMAGE']);
export const chartTypeSchema = z.enum(['line', 'bar', 'column', 'pie', 'doughnut', 'area', 'scatter', 'gauge', 'funnel']);
export const dataSourceTypeSchema = z.enum(['DATABASE', 'API', 'STATIC', 'CALCULATED']);
export const aggregationMethodSchema = z.enum(['COUNT', 'SUM', 'AVG', 'MIN', 'MAX', 'DISTINCT']);
export const filterOperatorSchema = z.enum(['eq', 'ne', 'gt', 'gte', 'lt', 'lte', 'in', 'nin', 'like', 'ilike', 'between', 'is_null', 'is_not_null']);
export const filterTypeSchema = z.enum(['string', 'number', 'date', 'boolean', 'array']);
export const alertTypeSchema = z.enum(['SYSTEM', 'PERFORMANCE', 'DEADLINE', 'ERROR', 'WARNING', 'INFO']);
export const alertSeveritySchema = z.enum(['LOW', 'MEDIUM', 'HIGH', 'CRITICAL']);

export const widgetPositionSchema = z.object({
  widgetId: uuidSchema,
  x: z.number().int().min(0),
  y: z.number().int().min(0),
  width: z.number().int().min(1),
  height: z.number().int().min(1),
  minWidth: z.number().int().min(1).optional(),
  minHeight: z.number().int().min(1).optional(),
  maxWidth: z.number().int().min(1).optional(),
  maxHeight: z.number().int().min(1).optional(),
});

export const dashboardLayoutSchema = z.object({
  columns: z.number().int().min(1).max(24),
  rows: z.number().int().min(1).max(100),
  widgets: z.array(widgetPositionSchema),
  responsive: z.object({
    mobile: z.object({
      columns: z.number().int().min(1).max(12).optional(),
      rows: z.number().int().min(1).max(100).optional(),
      widgets: z.array(widgetPositionSchema).optional(),
    }).optional(),
    tablet: z.object({
      columns: z.number().int().min(1).max(16).optional(),
      rows: z.number().int().min(1).max(100).optional(),
      widgets: z.array(widgetPositionSchema).optional(),
    }).optional(),
    desktop: z.object({
      columns: z.number().int().min(1).max(24).optional(),
      rows: z.number().int().min(1).max(100).optional(),
      widgets: z.array(widgetPositionSchema).optional(),
    }).optional(),
  }).optional(),
});

export const dashboardConfigSchema = z.object({
  layout: dashboardLayoutSchema,
  theme: z.string().optional(),
  refreshInterval: z.number().int().min(5).max(3600).optional(),
  defaultFilters: z.record(z.string(), z.any()).optional(),
  permissions: z.object({
    view: z.array(z.string()),
    edit: z.array(z.string()),
    share: z.array(z.string()),
    delete: z.array(z.string()),
  }).optional(),
  metadata: z.record(z.string(), z.any()).optional(),
});

export const createDashboardSchema = z.object({
  name: z.string().min(1, 'Name is required').max(255, 'Name too long'),
  description: z.string().max(1000, 'Description too long').optional(),
  type: dashboardTypeSchema,
  entityId: uuidSchema.optional(),
  config: dashboardConfigSchema.optional(),
  isDefault: z.boolean().optional(),
  isPublic: z.boolean().optional(),
});

export const updateDashboardSchema = z.object({
  name: z.string().min(1, 'Name is required').max(255, 'Name too long').optional(),
  description: z.string().max(1000, 'Description too long').optional(),
  config: dashboardConfigSchema.optional(),
  isDefault: z.boolean().optional(),
  isPublic: z.boolean().optional(),
  isActive: z.boolean().optional(),
});

export const widgetFormattingSchema = z.object({
  numberFormat: z.string().optional(),
  dateFormat: z.string().optional(),
  currency: z.string().optional(),
  precision: z.number().int().min(0).max(10).optional(),
  showPercentage: z.boolean().optional(),
  showTrend: z.boolean().optional(),
});

export const widgetConfigSchema = z.object({
  title: z.string().max(255, 'Title too long').optional(),
  subtitle: z.string().max(255, 'Subtitle too long').optional(),
  chartType: chartTypeSchema.optional(),
  colors: z.array(z.string()).optional(),
  showLegend: z.boolean().optional(),
  showGrid: z.boolean().optional(),
  showTooltip: z.boolean().optional(),
  animation: z.boolean().optional(),
  refreshInterval: z.number().int().min(5).max(3600).optional(),
  maxItems: z.number().int().min(1).max(1000).optional(),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).optional(),
  filters: z.record(z.string(), z.any()).optional(),
  formatting: widgetFormattingSchema.optional(),
});

export const dataFilterSchema = z.object({
  field: z.string().min(1, 'Field is required'),
  operator: filterOperatorSchema,
  value: z.any(),
  type: filterTypeSchema.optional(),
});

export const dataAggregationSchema = z.object({
  method: aggregationMethodSchema,
  field: z.string().min(1, 'Field is required'),
  groupBy: z.array(z.string()).optional(),
  having: z.record(z.string(), z.any()).optional(),
});

export const widgetDataSourceSchema = z.object({
  type: dataSourceTypeSchema,
  query: z.string().optional(),
  endpoint: z.string().url().optional(),
  params: z.record(z.string(), z.any()).optional(),
  aggregation: dataAggregationSchema.optional(),
  filters: z.array(dataFilterSchema).optional(),
  groupBy: z.array(z.string()).optional(),
  orderBy: z.array(z.string()).optional(),
});

export const createWidgetSchema = z.object({
  dashboardId: uuidSchema,
  name: z.string().min(1, 'Name is required').max(255, 'Name too long'),
  type: widgetTypeSchema,
  config: widgetConfigSchema,
  dataSource: widgetDataSourceSchema,
  position: widgetPositionSchema,
});

export const updateWidgetSchema = z.object({
  name: z.string().min(1, 'Name is required').max(255, 'Name too long').optional(),
  config: widgetConfigSchema.optional(),
  dataSource: widgetDataSourceSchema.optional(),
  position: widgetPositionSchema.optional(),
  isVisible: z.boolean().optional(),
});

// Filter schemas
export const entityFiltersSchema = z.object({
  type: z.array(entityTypeSchema).optional(),
  parentId: uuidSchema.optional(),
  managerId: uuidSchema.optional(),
  isActive: z.boolean().optional(),
  searchTerm: z.string().optional(),
  hasMembers: z.boolean().optional(),
});

export const personFiltersSchema = z.object({
  entityId: uuidSchema.optional(),
  isActive: z.boolean().optional(),
  searchTerm: z.string().optional(),
  position: z.string().optional(),
  hireDate: z.object({
    from: z.date().optional(),
    to: z.date().optional(),
  }).optional(),
});

export const userFiltersSchema = z.object({
  isActive: z.boolean().optional(),
  hasRole: z.string().optional(),
  hasPermission: z.string().optional(),
  entityId: uuidSchema.optional(),
  searchTerm: z.string().optional(),
  lastLoginBefore: z.date().optional(),
  lastLoginAfter: z.date().optional(),
  createdBefore: z.date().optional(),
  createdAfter: z.date().optional(),
});

export const roleFiltersSchema = z.object({
  isActive: z.boolean().optional(),
  isSystemRole: z.boolean().optional(),
  level: z.number().int().optional(),
  levelRange: z.object({
    min: z.number().int().optional(),
    max: z.number().int().optional(),
  }).optional(),
  hasPermission: z.string().optional(),
  searchTerm: z.string().optional(),
});

export const permissionFiltersSchema = z.object({
  resource: z.string().optional(),
  action: permissionActionSchema.optional(),
  isSystemPermission: z.boolean().optional(),
  hasRole: z.string().optional(),
  searchTerm: z.string().optional(),
});

export const dashboardFiltersSchema = z.object({
  type: dashboardTypeSchema.optional(),
  entityId: uuidSchema.optional(),
  ownerId: uuidSchema.optional(),
  isDefault: z.boolean().optional(),
  isPublic: z.boolean().optional(),
  isActive: z.boolean().optional(),
  searchTerm: z.string().optional(),
});

export const widgetFiltersSchema = z.object({
  dashboardId: uuidSchema.optional(),
  type: widgetTypeSchema.optional(),
  isVisible: z.boolean().optional(),
  searchTerm: z.string().optional(),
});

// Pagination schema
export const paginationSchema = z.object({
  page: z.number().int().min(1).optional(),
  limit: z.number().int().min(1).max(100).optional(),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).optional(),
});

// Bulk operation schemas
export const bulkOperationSchema = z.object({
  operation: z.enum(['create', 'update', 'delete', 'activate', 'deactivate']),
  ids: z.array(uuidSchema).optional(),
  data: z.any().optional(),
  filters: z.any().optional(),
});

// Invitation schemas
export const createInvitationSchema = z.object({
  email: emailSchema,
  roles: z.array(uuidSchema),
  entities: z.array(uuidSchema).optional(),
  message: z.string().max(1000, 'Message too long').optional(),
  expiresInDays: z.number().int().min(1).max(30).optional(),
});

export const acceptInvitationSchema = z.object({
  token: z.string().min(1, 'Token is required'),
  password: passwordSchema,
  profile: userProfileSchema,
});

// Export all schemas as a single object for easier importing
export const schemas = {
  // Base
  uuid: uuidSchema,
  email: emailSchema,
  phone: phoneSchema,
  password: passwordSchema,
  
  // Auth
  login: loginSchema,
  passwordReset: passwordResetSchema,
  passwordChange: passwordChangeSchema,
  userProfile: userProfileSchema,
  
  // Entities
  entityType: entityTypeSchema,
  createEntity: createEntitySchema,
  updateEntity: updateEntitySchema,
  createPerson: createPersonSchema,
  updatePerson: updatePersonSchema,
  assignPersonToEntity: assignPersonToEntitySchema,
  
  // Users
  createUser: createUserSchema,
  updateUser: updateUserSchema,
  userAccessLevel: userAccessLevelSchema,
  assignRole: assignRoleSchema,
  grantPermission: grantPermissionSchema,
  assignEntity: assignEntitySchema,
  
  // RBAC
  permissionAction: permissionActionSchema,
  createRole: createRoleSchema,
  updateRole: updateRoleSchema,
  createPermission: createPermissionSchema,
  updatePermission: updatePermissionSchema,
  assignPermissionToRole: assignPermissionToRoleSchema,
  
  // Dashboard
  dashboardType: dashboardTypeSchema,
  widgetType: widgetTypeSchema,
  chartType: chartTypeSchema,
  createDashboard: createDashboardSchema,
  updateDashboard: updateDashboardSchema,
  createWidget: createWidgetSchema,
  updateWidget: updateWidgetSchema,
  
  // Filters
  entityFilters: entityFiltersSchema,
  personFilters: personFiltersSchema,
  userFilters: userFiltersSchema,
  roleFilters: roleFiltersSchema,
  permissionFilters: permissionFiltersSchema,
  dashboardFilters: dashboardFiltersSchema,
  widgetFilters: widgetFiltersSchema,
  
  // Utility
  pagination: paginationSchema,
  bulkOperation: bulkOperationSchema,
  createInvitation: createInvitationSchema,
  acceptInvitation: acceptInvitationSchema,
};
