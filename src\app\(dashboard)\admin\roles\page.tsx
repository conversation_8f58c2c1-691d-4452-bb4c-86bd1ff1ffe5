'use client';

import { useState } from 'react';
import { RoleList } from '@/features/rbac/components/role-list';
import { PermissionMatrix } from '@/features/rbac/components/permission-matrix';
import type { Role } from '@/types/rbac';

export default function AdminRolesPage() {
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);

  const handleRoleSelect = (role: Role | null) => {
    setSelectedRole(role);
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold">Role & Permission Management</h1>
        <p className="mt-2 text-muted-foreground">
          Define roles and assign granular permissions.
        </p>
      </div>
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-1">
          <RoleList onRoleSelect={handleRoleSelect} selectedRoleId={selectedRole?.id} />
        </div>
        <div className="lg:col-span-2">
          {selectedRole ? (
            <PermissionMatrix selectedRoleId={selectedRole.id} />
          ) : (
            <div className="flex items-center justify-center h-full bg-card border rounded-md">
              <p className="text-muted-foreground">Select a role to manage its permissions.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}