'use client';

import { MapView, MapMarker } from '@/components/dashboard/map-view';
import type { Installation } from '../types';
import { getInstallationProgress } from '../utils/tracking-utils';

interface InstallationMapProps {
  installations: Installation[];
  title?: string;
  height?: string;
  className?: string;
  onInstallationClick?: (installation: Installation) => void;
  showControls?: boolean;
  showLegend?: boolean;
  noWrapper?: boolean;
}

// Fonction pour convertir les installations en marqueurs de carte
function convertInstallationsToMapMarkers(
  installations: Installation[],
  onInstallationClick?: (installation: Installation) => void
): MapMarker[] {
  return installations
    .filter(installation => installation.gps_coordinates)
    .map(installation => {
      const [lat, lng] = installation.gps_coordinates!
        .replace(/[()]/g, '')
        .split(',')
        .map(coord => parseFloat(coord.trim()));

      return {
        id: installation.id,
        latitude: lat,
        longitude: lng,
        title: installation.name,
        description: `${installation.product_type} - ${installation.site_location}`,
        status: mapInstallationStatusToMarkerStatus(installation.status),
        type: 'installation' as const,
        metadata: {
          'Numéro': installation.installation_number,
          'Type': installation.product_type,
          'Statut': getStatusLabel(installation.status),
          'Progression': `${getInstallationProgress(installation.latest_tracking)}%`,
          'Client': installation.client?.name || 'Non défini',
          'Chef d\'équipe': installation.team_leader
            ? `${installation.team_leader.firstName} ${installation.team_leader.lastName}`
            : 'Non assigné',
        },
        onClick: () => onInstallationClick?.(installation),
      };
    });
}

// Fonction pour mapper les statuts d'installation aux statuts de marqueur
function mapInstallationStatusToMarkerStatus(status: string): 'active' | 'completed' | 'pending' | 'error' {
  switch (status) {
    case 'COMPLETED':
      return 'completed';
    case 'IN_PROGRESS':
    case 'TESTING':
      return 'active';
    case 'PLANNING':
      return 'pending';
    case 'ON_HOLD':
    case 'CANCELLED':
      return 'error';
    default:
      return 'pending';
  }
}

// Fonction pour obtenir le label français du statut
function getStatusLabel(status: string): string {
  const statusLabels: Record<string, string> = {
    'PLANNING': 'Planification',
    'IN_PROGRESS': 'En cours',
    'TESTING': 'Tests',
    'COMPLETED': 'Terminé',
    'ON_HOLD': 'En attente',
    'CANCELLED': 'Annulé',
  };
  return statusLabels[status] || status;
}

export function InstallationMap({
  installations,
  title = "Carte des Installations",
  height = "400px",
  className,
  onInstallationClick,
  showControls = true,
  showLegend = true,
  noWrapper = false,
}: InstallationMapProps) {
  const markers = convertInstallationsToMapMarkers(installations, onInstallationClick);

  // Calculer le centre de la carte basé sur les installations
  const center = installations.length > 0 && installations.some(i => i.gps_coordinates)
    ? calculateMapCenter(installations.filter(i => i.gps_coordinates))
    : { lat: 48.8566, lng: 2.3522 }; // Paris par défaut

  const handleMarkerClick = (marker: MapMarker) => {
    if (onInstallationClick) {
      const installation = installations.find(i => i.id === marker.id);
      if (installation) {
        onInstallationClick(installation);
      }
    }
  };

  return (
    <MapView
      title={title}
      markers={markers}
      center={center}
      height={height}
      className={className}
      onMarkerClick={handleMarkerClick}
      showControls={showControls}
      showLegend={showLegend}
      noWrapper={noWrapper}
    />
  );
}

// Fonction pour calculer le centre de la carte
function calculateMapCenter(installations: Installation[]): { lat: number; lng: number } {
  const validCoordinates = installations
    .filter(i => i.gps_coordinates)
    .map(i => {
      const [lat, lng] = i.gps_coordinates!
        .replace(/[()]/g, '')
        .split(',')
        .map(coord => parseFloat(coord.trim()));
      return { lat, lng };
    })
    .filter(coord => !isNaN(coord.lat) && !isNaN(coord.lng));

  if (validCoordinates.length === 0) {
    return { lat: 48.8566, lng: 2.3522 }; // Paris par défaut
  }

  const avgLat = validCoordinates.reduce((sum, coord) => sum + coord.lat, 0) / validCoordinates.length;
  const avgLng = validCoordinates.reduce((sum, coord) => sum + coord.lng, 0) / validCoordinates.length;

  return { lat: avgLat, lng: avgLng };
}

// Hook pour les statistiques géographiques
export function useInstallationMapStats(installations: Installation[]) {
  const installationsWithCoords = installations.filter(i => i.gps_coordinates);
  
  const stats = {
    total: installations.length,
    withCoordinates: installationsWithCoords.length,
    withoutCoordinates: installations.length - installationsWithCoords.length,
    byStatus: installations.reduce((acc, installation) => {
      acc[installation.status] = (acc[installation.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>),
    byProductType: installations.reduce((acc, installation) => {
      acc[installation.product_type] = (acc[installation.product_type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>),
  };

  return stats;
}
