import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';
import { 
  Filter, 
  X, 
  RotateCcw, 
  Search,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { DateFilter, DateRange } from './date-filter';
import { StatusFilter, StatusOption } from './status-filter';

export interface GlobalFilterConfig {
  clients?: {
    enabled: boolean;
    options: Array<{ value: string; label: string; count?: number }>;
    placeholder?: string;
  };
  projects?: {
    enabled: boolean;
    options: Array<{ value: string; label: string; count?: number }>;
    placeholder?: string;
  };
  clientTypes?: {
    enabled: boolean;
    options: Array<{ value: string; label: string; count?: number }>;
    placeholder?: string;
  };
  types?: {
    enabled: boolean;
    options: Array<{ value: string; label: string; count?: number }>;
    placeholder?: string;
  };
  statuses?: {
    enabled: boolean;
    options: StatusOption[];
    placeholder?: string;
  };
  dateRange?: {
    enabled: boolean;
    placeholder?: string;
  };
  search?: {
    enabled: boolean;
    placeholder?: string;
  };
}

export interface GlobalFilterValues {
  clients: string[];
  projects: string[];
  clientTypes: string[];
  types: string[];
  statuses: string[];
  dateRange?: DateRange;
  search: string;
}

interface GlobalFiltersProps {
  config: GlobalFilterConfig;
  values: GlobalFilterValues;
  onChange: (values: GlobalFilterValues) => void;
  className?: string;
  collapsible?: boolean;
  defaultCollapsed?: boolean;
  showActiveCount?: boolean;
  onReset?: () => void;
}

export function GlobalFilters({
  config,
  values,
  onChange,
  className,
  collapsible = false,
  defaultCollapsed = false,
  showActiveCount = true,
  onReset,
}: GlobalFiltersProps) {
  const [isCollapsed, setIsCollapsed] = useState(defaultCollapsed);

  const updateFilter = <K extends keyof GlobalFilterValues>(
    key: K,
    value: GlobalFilterValues[K]
  ) => {
    onChange({ ...values, [key]: value });
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (values.clients.length > 0) count++;
    if (values.projects.length > 0) count++;
    if (values.clientTypes.length > 0) count++;
    if (values.types.length > 0) count++;
    if (values.statuses.length > 0) count++;
    if (values.dateRange) count++;
    if (values.search.trim()) count++;
    return count;
  };

  const handleReset = () => {
    const resetValues: GlobalFilterValues = {
      clients: [],
      projects: [],
      clientTypes: [],
      types: [],
      statuses: [],
      dateRange: undefined,
      search: '',
    };
    onChange(resetValues);
    onReset?.();
  };

  const activeCount = getActiveFiltersCount();

  return (
    <Card className={cn('w-full bg-white/80 backdrop-blur-sm border-primary/20 shadow-sm', className)}>
      <CardHeader className="pb-2 pt-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Filter className="h-4 w-4 text-primary" />
            <CardTitle className="text-base font-medium">Filtres Globaux</CardTitle>
            {showActiveCount && activeCount > 0 && (
              <Badge variant="secondary" className="ml-2 text-xs">
                {activeCount} actif{activeCount > 1 ? 's' : ''}
              </Badge>
            )}
          </div>
          
          <div className="flex items-center space-x-2">
            {activeCount > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleReset}
                className="text-xs h-7 px-2"
              >
                <RotateCcw className="h-3 w-3 mr-1" />
                Réinitialiser
              </Button>
            )}
            
            {collapsible && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsCollapsed(!isCollapsed)}
                className="h-7 w-7 p-0"
              >
                {isCollapsed ? (
                  <ChevronDown className="h-3 w-3" />
                ) : (
                  <ChevronUp className="h-3 w-3" />
                )}
              </Button>
            )}
          </div>
        </div>
      </CardHeader>

      {!isCollapsed && (
        <CardContent className="space-y-3 pt-2">
          {/* Ligne principale des filtres - Layout équidistant */}
          <div className="w-full flex flex-col lg:flex-row gap-4 lg:gap-6">
            {/* Recherche */}
            {config.search?.enabled && (
              <div className="flex-1 space-y-2">
                <Label className="text-xs font-medium text-muted-foreground">Recherche</Label>
                <div className="relative">
                  <Search className="absolute left-2.5 top-1/2 transform -translate-y-1/2 h-3.5 w-3.5 text-muted-foreground" />
                  <Input
                    placeholder={config.search.placeholder || "Rechercher..."}
                    value={values.search}
                    onChange={(e) => updateFilter('search', e.target.value)}
                    className="pl-8 h-9 text-sm border-muted-foreground/20 focus:border-kya-primary w-full"
                  />
                </div>
              </div>
            )}

            {/* Clients */}
            {config.clients?.enabled && (
              <div className="flex-1 space-y-2">
                <Label className="text-xs font-medium text-muted-foreground">Client</Label>
                <StatusFilter
                  options={config.clients.options.map(opt => ({
                    value: opt.value,
                    label: opt.label,
                    count: opt.count,
                  }))}
                  value={values.clients}
                  onChange={(selected) => updateFilter('clients', selected)}
                  placeholder={config.clients.placeholder || "Tous les clients"}
                  showCounts={true}
                />
              </div>
            )}

            {/* Projets */}
            {config.projects?.enabled && (
              <div className="flex-1 space-y-2">
                <Label className="text-xs font-medium text-muted-foreground">Projet</Label>
                <StatusFilter
                  options={config.projects.options.map(opt => ({
                    value: opt.value,
                    label: opt.label,
                    count: opt.count,
                  }))}
                  value={values.projects}
                  onChange={(selected) => updateFilter('projects', selected)}
                  placeholder={config.projects.placeholder || "Tous les projets"}
                  showCounts={true}
                />
              </div>
            )}

            {/* Types de client */}
            {config.clientTypes?.enabled && (
                <div className="flex-1 space-y-2">
                    <Label className="text-xs font-medium text-muted-foreground">Type de Client</Label>
                    <StatusFilter
                        options={config.clientTypes.options.map(opt => ({
                            value: opt.value,
                            label: opt.label,
                            count: opt.count,
                        }))}
                        value={values.clientTypes}
                        onChange={(selected) => updateFilter('clientTypes', selected)}
                        placeholder={config.clientTypes.placeholder || "Tous les types de client"}
                        showCounts={true}
                    />
                </div>
            )}

            {/* Types */}
            {config.types?.enabled && (
              <div className="flex-1 space-y-2">
                <Label className="text-xs font-medium text-muted-foreground">Type</Label>
                <StatusFilter
                  options={config.types.options.map(opt => ({
                    value: opt.value,
                    label: opt.label,
                    count: opt.count,
                  }))}
                  value={values.types}
                  onChange={(selected) => updateFilter('types', selected)}
                  placeholder={config.types.placeholder || "Tous les types"}
                  showCounts={true}
                />
              </div>
            )}

            {/* Statuts */}
            {config.statuses?.enabled && (
              <div className="flex-1 space-y-2">
                <Label className="text-xs font-medium text-muted-foreground">Statut</Label>
                <StatusFilter
                  options={config.statuses.options}
                  value={values.statuses}
                  onChange={(selected) => updateFilter('statuses', selected)}
                  placeholder={config.statuses.placeholder || "Tous les statuts"}
                  showCounts={true}
                />
              </div>
            )}

            {/* Période */}
            {config.dateRange?.enabled && (
              <div className="flex-1 space-y-2">
                <Label className="text-xs font-medium text-muted-foreground">Période</Label>
                <DateFilter
                  value={values.dateRange}
                  onChange={(range) => updateFilter('dateRange', range)}
                  placeholder={config.dateRange.placeholder || "Toutes les dates"}
                />
              </div>
            )}
          </div>

          {/* Filtres actifs */}
          {activeCount > 0 && (
            <div className="pt-4 border-t border-gray-200">
              <div className="flex items-center justify-between mb-3">
                <Label className="text-sm font-medium text-gray-700">Filtres actifs</Label>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleReset}
                  className="text-xs text-red-600 hover:text-red-700 hover:bg-red-50"
                >
                  <X className="h-4 w-4 mr-1" />
                  Tout effacer
                </Button>
              </div>

              <div className="flex flex-wrap gap-2">
                {/* Badges des filtres actifs */}
                {values.search.trim() && (
                  <Badge variant="outline" className="flex items-center gap-1 pr-1">
                    <Search className="h-3 w-3" />
                    <span className="text-xs">"{values.search}"</span>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-4 w-4 p-0 hover:bg-transparent"
                      onClick={() => updateFilter('search', '')}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                )}

                {values.clients.map(client => {
                  const option = config.clients?.options.find(opt => opt.value === client);
                  return option ? (
                    <Badge key={client} variant="outline" className="flex items-center gap-1 pr-1">
                      <span className="text-xs">Client: {option.label}</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-4 w-4 p-0 hover:bg-transparent"
                        onClick={() => updateFilter('clients', values.clients.filter(c => c !== client))}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </Badge>
                  ) : null;
                })}

                {values.projects.map(project => {
                  const option = config.projects?.options.find(opt => opt.value === project);
                  return option ? (
                    <Badge key={project} variant="outline" className="flex items-center gap-1 pr-1">
                      <span className="text-xs">Projet: {option.label}</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-4 w-4 p-0 hover:bg-transparent"
                        onClick={() => updateFilter('projects', values.projects.filter(p => p !== project))}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </Badge>
                  ) : null;
                })}

                {values.clientTypes.map(type => {
                  const option = config.clientTypes?.options.find(opt => opt.value === type);
                  return option ? (
                    <Badge key={type} variant="outline" className="flex items-center gap-1 pr-1">
                      <span className="text-xs">Type Client: {option.label}</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-4 w-4 p-0 hover:bg-transparent"
                        onClick={() => updateFilter('clientTypes', values.clientTypes.filter(t => t !== type))}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </Badge>
                  ) : null;
                })}

                {values.types.map(type => {
                  const option = config.types?.options.find(opt => opt.value === type);
                  return option ? (
                    <Badge key={type} variant="outline" className="flex items-center gap-1 pr-1">
                      <span className="text-xs">Type: {option.label}</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-4 w-4 p-0 hover:bg-transparent"
                        onClick={() => updateFilter('types', values.types.filter(t => t !== type))}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </Badge>
                  ) : null;
                })}

                {values.statuses.map(status => {
                  const option = config.statuses?.options.find(opt => opt.value === status);
                  return option ? (
                    <Badge key={status} variant="outline" className="flex items-center gap-1 pr-1">
                      {option.icon}
                      <span className="text-xs">{option.label}</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-4 w-4 p-0 hover:bg-transparent"
                        onClick={() => updateFilter('statuses', values.statuses.filter(s => s !== status))}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </Badge>
                  ) : null;
                })}

                {values.dateRange && (
                  <Badge variant="outline" className="flex items-center gap-1 pr-1">
                    <span className="text-xs">
                      {values.dateRange.label || 'Période personnalisée'}
                    </span>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-4 w-4 p-0 hover:bg-transparent"
                      onClick={() => updateFilter('dateRange', undefined)}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                )}
              </div>
            </div>
          )}
        </CardContent>
      )}
    </Card>
  );
}

// Hook pour gérer les filtres globaux
export function useGlobalFilters(initialValues?: Partial<GlobalFilterValues>) {
  const [filters, setFilters] = useState<GlobalFilterValues>({
    clients: [],
    projects: [],
    clientTypes: [],
    types: [],
    statuses: [],
    dateRange: undefined,
    search: '',
    ...initialValues,
  });

  const updateFilter = <K extends keyof GlobalFilterValues>(
    key: K,
    value: GlobalFilterValues[K]
  ) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const reset = () => {
    setFilters({
      clients: [],
      projects: [],
      clientTypes: [],
      types: [],
      statuses: [],
      dateRange: undefined,
      search: '',
    });
  };

  const hasActiveFilters = () => {
    return (
      filters.clients.length > 0 ||
      filters.projects.length > 0 ||
      filters.clientTypes.length > 0 ||
      filters.types.length > 0 ||
      filters.statuses.length > 0 ||
      !!filters.dateRange ||
      filters.search.trim().length > 0
    );
  };

  return {
    filters,
    setFilters,
    updateFilter,
    reset,
    hasActiveFilters,
  };
}
