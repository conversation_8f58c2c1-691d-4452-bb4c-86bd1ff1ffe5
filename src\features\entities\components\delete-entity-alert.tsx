'use client';

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { deleteEntityAction } from '../actions';
import { toast } from 'sonner';

interface DeleteEntityAlertProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  entityId: string;
}

export function DeleteEntityAlert({ isOpen, onClose, onSuccess, entityId }: DeleteEntityAlertProps) {
  
  const handleDelete = async () => {
    const result = await deleteEntityAction(entityId);
    if (result.error) {
      toast.error('Erreur', { description: result.error });
    } else {
      toast.success('Succès', { description: 'Entité supprimée avec succès.' });
      onSuccess();
    }
    onClose();
  };

  return (
    <AlertDialog open={isOpen} onOpenChange={onClose}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Êtes-vous sûr de vouloir supprimer cette entité ?</AlertDialogTitle>
          <AlertDialogDescription>
            Cette action est irréversible. La suppression de cette entité peut affecter les entités enfants et les utilisateurs associés.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Annuler</AlertDialogCancel>
          <AlertDialogAction onClick={handleDelete} className="bg-destructive hover:bg-destructive/90">
            Supprimer
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}