-- KYA Dashboards - Mi<PERSON> Script
-- This script modifies the existing database schema to the new, refactored structure.
-- Run this ONCE on your existing database.

-- Step 1: Add new columns to `user_profiles`
-- We add them first to be able to migrate data before dropping old columns.
ALTER TABLE public.user_profiles ADD COLUMN IF NOT EXISTS auth_user_id UUID REFERENCES public.auth_users(id) ON DELETE CASCADE;
ALTER TABLE public.user_profiles ADD COLUMN IF NOT EXISTS person_id UUID REFERENCES public.persons(id) ON DELETE SET NULL;

-- Create unique constraint for the new foreign keys
ALTER TABLE public.user_profiles ADD CONSTRAINT user_profiles_auth_user_id_key UNIQUE (auth_user_id);
ALTER TABLE public.user_profiles ADD CONSTRAINT user_profiles_person_id_key UNIQUE (person_id);

-- Step 2: Migrate existing data (if any)
-- This query populates the new columns in `user_profiles` from the old columns in `auth_users`.
UPDATE public.user_profiles up
SET 
  auth_user_id = au.id,
  person_id = au.person_id
FROM public.auth_users au
WHERE up.id = au.user_profile_id;

-- Step 3: Remove old columns from `auth_users`
-- We drop the constraints first, then the columns.
ALTER TABLE public.auth_users DROP CONSTRAINT IF EXISTS auth_users_person_id_fkey;
ALTER TABLE public.auth_users DROP CONSTRAINT IF EXISTS auth_users_user_profile_id_fkey;

ALTER TABLE public.auth_users DROP COLUMN IF EXISTS person_id;
ALTER TABLE public.auth_users DROP COLUMN IF EXISTS user_profile_id;
ALTER TABLE public.auth_users DROP COLUMN IF EXISTS email_verified;

-- Step 4: Update foreign key in `auth_users` for user_profiles
-- The primary key of user_profiles is now its own UUID, not a direct link to auth_users.
-- The link is now FROM user_profiles TO auth_users.
-- We need to ensure the ID of user_profiles is correctly associated.
-- The previous logic assumed user_profiles.id was the same as auth_users.user_profile_id
-- We will now link them via the new auth_user_id column.
-- Let's ensure the user_profile.id is correctly linked to auth_users.id
-- This part is tricky without knowing the exact state of the data.
-- The migration in Step 2 should handle the linking.
-- Let's just make sure auth_users.id is correctly referenced in user_profiles.auth_user_id.

-- Let's assume after migration, we want user_profiles.id to be the main reference for a profile.
-- The auth_users table will now be simpler.

-- Final check: The schema should now be updated.
-- You can verify by checking the columns in `auth_users` and `user_profiles`.

COMMENT ON COLUMN public.user_profiles.auth_user_id IS 'Links the profile to a specific login account.';
COMMENT ON COLUMN public.user_profiles.person_id IS 'Links the profile to a specific person record in the HR system.';