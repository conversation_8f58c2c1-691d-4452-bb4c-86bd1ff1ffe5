import { useMemo } from 'react';
import { 
  LineChart as RechartsLineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid,
  ResponsiveContainer,
  Area,
  AreaChart as RechartsAreaChart
} from 'recharts';
import { 
  ChartContainer, 
  ChartTooltip, 
  ChartTooltipContent, 
  ChartLegend, 
  ChartLegendContent,
  type ChartConfig 
} from '@/components/ui/chart';
import { cn } from '@/lib/utils';

export interface LineChartData {
  label: string;
  value: number;
  [key: string]: any;
}

interface LineChartProps {
  data: LineChartData[];
  className?: string;
  showTooltip?: boolean;
  showLegend?: boolean;
  showGrid?: boolean;
  showDots?: boolean;
  config?: ChartConfig;
  dataKey?: string;
  xAxisKey?: string;
  strokeWidth?: number;
  curve?: 'linear' | 'monotone' | 'step';
  margin?: {
    top?: number;
    right?: number;
    bottom?: number;
    left?: number;
  };
  onPointClick?: (data: LineChartData, index: number) => void;
}

const defaultColors = [
  'hsl(var(--chart-1))', // KYA Primary
  'hsl(var(--chart-2))', // KYA Secondary  
  'hsl(var(--chart-3))', // KYA Accent
  'hsl(var(--chart-4))', // KYA Brown
  'hsl(var(--chart-5))', // Additional colors
];

export function LineChart({
  data,
  className,
  showTooltip = true,
  showLegend = false,
  showGrid = true,
  showDots = true,
  config,
  dataKey = 'value',
  xAxisKey = 'label',
  strokeWidth = 2,
  curve = 'monotone',
  margin = { top: 20, right: 30, left: 20, bottom: 5 },
  onPointClick,
}: LineChartProps) {
  const chartConfig = useMemo(() => {
    if (config) return config;
    
    return {
      [dataKey]: {
        label: 'Valeur',
        color: defaultColors[0],
      },
    } as ChartConfig;
  }, [config, dataKey]);

  if (data.length === 0) {
    return (
      <div className={cn('flex items-center justify-center h-[300px]', className)}>
        <div className="text-center text-muted-foreground">
          <div className="text-4xl mb-2">📈</div>
          <p>Aucune donnée disponible</p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn('w-full', className)}>
      <ChartContainer config={chartConfig} className="h-[300px]">
        <RechartsLineChart
          data={data}
          margin={margin}
          onClick={onPointClick}
        >
          {showGrid && <CartesianGrid strokeDasharray="3 3" />}
          <XAxis 
            dataKey={xAxisKey}
            tickLine={false}
            tickMargin={10}
            axisLine={false}
            fontSize={12}
          />
          <YAxis
            tickLine={false}
            tickMargin={10}
            axisLine={false}
            fontSize={12}
            tickFormatter={(value) => value.toLocaleString('fr-FR')}
          />
          
          {showTooltip && (
            <ChartTooltip 
              content={
                <ChartTooltipContent 
                  formatter={(value, name) => [
                    `${Number(value).toLocaleString('fr-FR')}`,
                    name
                  ]}
                />
              } 
            />
          )}
          
          {showLegend && (
            <ChartLegend content={<ChartLegendContent />} />
          )}
          
          <Line 
            type={curve}
            dataKey={dataKey} 
            stroke={`var(--color-${dataKey})`}
            strokeWidth={strokeWidth}
            dot={showDots ? { r: 4 } : false}
            activeDot={{ r: 6 }}
            className="drop-shadow-sm"
          />
        </RechartsLineChart>
      </ChartContainer>
    </div>
  );
}

// Composant Area Chart (graphique en aires)
interface AreaChartProps extends Omit<LineChartProps, 'showDots' | 'strokeWidth'> {
  fillOpacity?: number;
  gradient?: boolean;
}

export function AreaChart({
  data,
  className,
  showTooltip = true,
  showLegend = false,
  showGrid = true,
  config,
  dataKey = 'value',
  xAxisKey = 'label',
  curve = 'monotone',
  margin = { top: 20, right: 30, left: 20, bottom: 5 },
  fillOpacity = 0.3,
  gradient = true,
  onPointClick,
}: AreaChartProps) {
  const chartConfig = useMemo(() => {
    if (config) return config;
    
    return {
      [dataKey]: {
        label: 'Valeur',
        color: defaultColors[0],
      },
    } as ChartConfig;
  }, [config, dataKey]);

  if (data.length === 0) {
    return (
      <div className={cn('flex items-center justify-center h-[300px]', className)}>
        <div className="text-center text-muted-foreground">
          <div className="text-4xl mb-2">📈</div>
          <p>Aucune donnée disponible</p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn('w-full', className)}>
      <ChartContainer config={chartConfig} className="h-[300px]">
        <RechartsAreaChart
          data={data}
          margin={margin}
          onClick={onPointClick}
        >
          {gradient && (
            <defs>
              <linearGradient id={`gradient-${dataKey}`} x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor={`var(--color-${dataKey})`} stopOpacity={0.8}/>
                <stop offset="95%" stopColor={`var(--color-${dataKey})`} stopOpacity={0.1}/>
              </linearGradient>
            </defs>
          )}
          
          {showGrid && <CartesianGrid strokeDasharray="3 3" />}
          <XAxis 
            dataKey={xAxisKey}
            tickLine={false}
            tickMargin={10}
            axisLine={false}
            fontSize={12}
          />
          <YAxis
            tickLine={false}
            tickMargin={10}
            axisLine={false}
            fontSize={12}
            tickFormatter={(value) => value.toLocaleString('fr-FR')}
          />
          
          {showTooltip && (
            <ChartTooltip 
              content={
                <ChartTooltipContent 
                  formatter={(value, name) => [
                    `${Number(value).toLocaleString('fr-FR')}`,
                    name
                  ]}
                />
              } 
            />
          )}
          
          {showLegend && (
            <ChartLegend content={<ChartLegendContent />} />
          )}
          
          <Area 
            type={curve}
            dataKey={dataKey} 
            stroke={`var(--color-${dataKey})`}
            fill={gradient ? `url(#gradient-${dataKey})` : `var(--color-${dataKey})`}
            fillOpacity={fillOpacity}
            strokeWidth={2}
            dot={{ r: 4 }}
            activeDot={{ r: 6 }}
            className="drop-shadow-sm"
          />
        </RechartsAreaChart>
      </ChartContainer>
    </div>
  );
}

// Hook pour préparer les données du graphique
export function useLineChartData<T>(
  data: T[],
  config: {
    labelKey: keyof T;
    valueKey: keyof T;
  }
) {
  return useMemo(() => {
    return data.map((item) => ({
      label: String(item[config.labelKey]),
      value: Number(item[config.valueKey]),
      ...item,
    }));
  }, [data, config]);
}

// Composant de mini graphique linéaire
interface MiniLineChartProps {
  data: LineChartData[];
  height?: number;
  color?: string;
  className?: string;
  showArea?: boolean;
}

export function MiniLineChart({ 
  data, 
  height = 40, 
  color = 'hsl(var(--chart-1))',
  className,
  showArea = false
}: MiniLineChartProps) {
  const config = {
    value: {
      label: 'Valeur',
      color: color,
    },
  };

  if (showArea) {
    return (
      <div className={cn('w-full', className)} style={{ height }}>
        <AreaChart
          data={data}
          showTooltip={false}
          showLegend={false}
          showGrid={false}
          config={config}
          margin={{ top: 5, right: 5, left: 5, bottom: 5 }}
          fillOpacity={0.4}
          gradient={false}
        />
      </div>
    );
  }

  return (
    <div className={cn('w-full', className)} style={{ height }}>
      <LineChart
        data={data}
        showTooltip={false}
        showLegend={false}
        showGrid={false}
        showDots={false}
        config={config}
        margin={{ top: 5, right: 5, left: 5, bottom: 5 }}
        strokeWidth={1.5}
      />
    </div>
  );
}
