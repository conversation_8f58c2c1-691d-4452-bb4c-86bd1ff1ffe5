import { createClient } from '@/utils/supabase/server';
import { PersonService } from './person-service';
import { keysToCamel, keysToSnake } from '@/utils/case-converter';
import type { PersonWithEntities, CreatePersonData } from '../types';

/**
 * Service for synchronizing persons with users.
 * Handles the relationship between auth_users and persons tables.
 */
export class PersonUserSyncService {
  /**
   * Creates a person record when a new user is created.
   * This should be called from the user creation process.
   */
  static async createPersonFromUser(
    userId: string,
    userData: {
      email: string;
      displayName?: string;
      firstName?: string;
      lastName?: string;
    },
    createdBy: string
  ): Promise<PersonWithEntities | null> {
    try {
      // Extract first and last name from display name if not provided
      let firstName = userData.firstName;
      let lastName = userData.lastName;
      
      if (!firstName || !lastName) {
        if (userData.displayName) {
          const nameParts = userData.displayName.trim().split(' ');
          firstName = firstName || nameParts[0] || '';
          lastName = lastName || (nameParts.length > 1 ? nameParts.slice(1).join(' ') : '');
        } else {
          // Fallback to email prefix
          const emailPrefix = userData.email.split('@')[0];
          firstName = firstName || emailPrefix;
          lastName = lastName || '';
        }
      }

      const personData: CreatePersonData = {
        firstName: firstName || 'Utilisateur',
        lastName: lastName || 'Système',
        email: userData.email,
        metadata: {
          createdFromUser: true,
          userId: userId,
        },
      };

      const person = await PersonService.createPerson(personData, createdBy);
      
      // Update user_profiles to link to the person
      await this.linkUserToPerson(userId, person.id);
      
      return person;
    } catch (error) {
      console.error('Error creating person from user:', error);
      return null;
    }
  }

  /**
   * Links a user profile to a person record.
   */
  static async linkUserToPerson(userId: string, personId: string): Promise<void> {
    const supabase = await createClient();
    
    const { error } = await supabase
      .from('user_profiles')
      .update({ person_id: personId })
      .eq('auth_user_id', userId);

    if (error) {
      throw new Error(`Failed to link user to person: ${error.message}`);
    }
  }

  /**
   * Unlinks a user profile from a person record.
   */
  static async unlinkUserFromPerson(userId: string): Promise<void> {
    const supabase = await createClient();
    
    const { error } = await supabase
      .from('user_profiles')
      .update({ person_id: null })
      .eq('auth_user_id', userId);

    if (error) {
      throw new Error(`Failed to unlink user from person: ${error.message}`);
    }
  }

  /**
   * Gets the person associated with a user.
   */
  static async getPersonByUserId(userId: string): Promise<PersonWithEntities | null> {
    const supabase = await createClient();
    
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('person_id')
      .eq('auth_user_id', userId)
      .single();

    if (profileError || !profile?.person_id) {
      return null;
    }

    return await PersonService.getPersonById(profile.person_id);
  }

  /**
   * Gets the user associated with a person.
   */
  static async getUserByPersonId(personId: string): Promise<any | null> {
    const supabase = await createClient();
    
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select(`
        auth_user_id,
        auth_users!auth_user_id (
          id,
          supabase_id,
          email,
          is_active,
          created_at,
          updated_at
        )
      `)
      .eq('person_id', personId)
      .single();

    if (profileError || !profile) {
      return null;
    }

    return keysToCamel(profile.auth_users);
  }

  /**
   * Synchronizes person data with user data.
   * Updates person information when user information changes.
   */
  static async syncPersonWithUser(
    userId: string,
    userData: {
      email?: string;
      displayName?: string;
    },
    updatedBy: string
  ): Promise<PersonWithEntities | null> {
    try {
      const person = await this.getPersonByUserId(userId);
      
      if (!person) {
        return null;
      }

      const updateData: any = {};
      
      // Update email if changed
      if (userData.email && userData.email !== person.email) {
        updateData.email = userData.email;
      }

      // Update name if display name changed and person doesn't have custom names
      if (userData.displayName && person.metadata?.createdFromUser) {
        const nameParts = userData.displayName.trim().split(' ');
        const firstName = nameParts[0] || '';
        const lastName = nameParts.length > 1 ? nameParts.slice(1).join(' ') : '';
        
        if (firstName !== person.firstName || lastName !== person.lastName) {
          updateData.firstName = firstName;
          updateData.lastName = lastName;
        }
      }

      // Update metadata
      updateData.metadata = {
        ...person.metadata,
        lastSyncedAt: new Date().toISOString(),
      };

      if (Object.keys(updateData).length > 0) {
        return await PersonService.updatePerson(person.id, updateData, updatedBy);
      }

      return person;
    } catch (error) {
      console.error('Error syncing person with user:', error);
      return null;
    }
  }

  /**
   * Gets all persons that are linked to users.
   */
  static async getPersonsWithUsers(): Promise<Array<PersonWithEntities & { user?: any }>> {
    const supabase = await createClient();
    
    const { data, error } = await supabase
      .from('persons')
      .select(`
        *,
        entities_persons!person_id (
          id,
          entity_id,
          role_in_entity,
          start_date,
          end_date,
          is_primary,
          created_at,
          updated_at,
          entities (
            id,
            name,
            type,
            code
          )
        ),
        user_profiles!person_id (
          auth_user_id,
          auth_users!auth_user_id (
            id,
            supabase_id,
            email,
            is_active,
            created_at,
            updated_at
          )
        )
      `)
      .eq('is_active', true);

    if (error) {
      throw new Error(`Failed to fetch persons with users: ${error.message}`);
    }

    return (data || []).map((person: any) => {
      const camelPerson = keysToCamel(person) as any;
      
      // Transform entities_persons to entities
      const entities = (camelPerson.entitiesPersons || []).map((ep: any) => ({
        ...ep,
        entity: ep.entities
      }));
      
      // Find primary entity
      const primaryEntityAssignment = entities.find((e: any) => e.isPrimary);
      const primaryEntity = primaryEntityAssignment?.entity;
      
      // Get user data
      const userProfile = camelPerson.userProfiles?.[0];
      const user = userProfile?.authUsers;

      return {
        ...camelPerson,
        fullName: `${camelPerson.firstName} ${camelPerson.lastName}`,
        displayName: camelPerson.firstName + ' ' + camelPerson.lastName,
        entities,
        primaryEntity,
        user: user ? keysToCamel(user) : undefined,
        entitiesPersons: undefined, // Remove the raw relation
        userProfiles: undefined, // Remove the raw relation
      };
    });
  }

  /**
   * Gets all users that are not linked to persons.
   */
  static async getUsersWithoutPersons(): Promise<any[]> {
    const supabase = await createClient();
    
    const { data, error } = await supabase
      .from('auth_users')
      .select(`
        *,
        user_profiles!auth_user_id (
          id,
          person_id,
          display_name
        )
      `)
      .eq('is_active', true)
      .is('user_profiles.person_id', null);

    if (error) {
      throw new Error(`Failed to fetch users without persons: ${error.message}`);
    }

    return (data || []).map((user: any) => keysToCamel(user));
  }
}
