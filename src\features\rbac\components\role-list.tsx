'use client';

import { useState, useEffect, useTransition } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Textarea } from '@/components/ui/textarea';
import { Plus, Search, Edit, Trash2 } from 'lucide-react';
import { toast } from 'sonner';
import type { Role } from '@/types/rbac';
import { getRolesAction, createRoleAction, updateRoleAction, deleteRoleAction } from '../actions';
import { createRoleSchema, updateRoleSchema } from '../utils/validation';

interface RoleListProps {
  onRoleSelect?: (role: Role | null) => void;
  selectedRoleId?: string;
}

export function RoleList({ onRoleSelect, selectedRoleId }: RoleListProps) {
  const [roles, setRoles] = useState<Role[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);
  const [isPending, startTransition] = useTransition();

  const createForm = useForm<z.infer<typeof createRoleSchema>>({
    resolver: zodResolver(createRoleSchema),
    defaultValues: { name: '', description: '' },
  });

  const editForm = useForm<z.infer<typeof updateRoleSchema>>({
    resolver: zodResolver(updateRoleSchema),
  });

  const fetchRoles = async () => {
    setIsLoading(true);
    setError(null);
    const result = await getRolesAction();
    if (result.data) {
      setRoles(result.data);
    } else {
      setError(result.error || 'Failed to fetch roles.');
      toast.error(result.error || 'Failed to fetch roles.');
    }
    setIsLoading(false);
  };

  useEffect(() => {
    fetchRoles();
  }, []);

  const filteredRoles = roles.filter(role =>
    role.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleCreateRole = (values: z.infer<typeof createRoleSchema>) => {
    startTransition(async () => {
      const result = await createRoleAction(values);
      if (result.error) {
        toast.error(`Error: ${result.error}`);
      } else {
        toast.success('Role created successfully.');
        await fetchRoles();
        setIsCreateDialogOpen(false);
        createForm.reset();
      }
    });
  };

  const handleUpdateRole = (values: z.infer<typeof updateRoleSchema>) => {
    if (!selectedRole) return;
    startTransition(async () => {
      const result = await updateRoleAction(selectedRole.id, values);
      if (result.error) {
        toast.error(`Error: ${result.error}`);
      } else {
        toast.success('Role updated successfully.');
        await fetchRoles();
        setIsEditDialogOpen(false);
      }
    });
  };

  const handleDeleteRole = () => {
    if (!selectedRole) return;
    startTransition(async () => {
      const result = await deleteRoleAction(selectedRole.id);
      if (result.error) {
        toast.error(`Error: ${result.error}`);
      } else {
        toast.success('Role deleted successfully.');
        await fetchRoles();
        setIsDeleteDialogOpen(false);
        onRoleSelect?.(null);
      }
    });
  };

  const openEditDialog = (role: Role) => {
    setSelectedRole(role);
    editForm.reset({ name: role.name, description: role.description });
    setIsEditDialogOpen(true);
  };

  const openDeleteDialog = (role: Role) => {
    setSelectedRole(role);
    setIsDeleteDialogOpen(true);
  };

  if (isLoading) return <Card><CardHeader><CardTitle>Loading...</CardTitle></CardHeader></Card>;
  if (error) return <Card><CardHeader><CardTitle className="text-destructive">Error: {error}</CardTitle></CardHeader></Card>;
  
  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Roles</CardTitle>
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild><Button size="sm"><Plus className="h-4 w-4 mr-2" />Add Role</Button></DialogTrigger>
            <DialogContent>
              <DialogHeader><DialogTitle>Add New Role</DialogTitle></DialogHeader>
              <Form {...createForm}>
                <form onSubmit={createForm.handleSubmit(handleCreateRole)} className="space-y-4">
                  <FormField control={createForm.control} name="name" render={({ field }) => (
                    <FormItem><FormLabel>Name</FormLabel><FormControl><Input placeholder="e.g., editor" {...field} /></FormControl><FormMessage /></FormItem>
                  )} />
                  <FormField control={createForm.control} name="description" render={({ field }) => (
                    <FormItem><FormLabel>Description</FormLabel><FormControl><Textarea placeholder="Role description..." {...field} /></FormControl><FormMessage /></FormItem>
                  )} />
                  <div className="flex justify-end space-x-2">
                    <Button type="button" variant="outline" onClick={() => setIsCreateDialogOpen(false)}>Cancel</Button>
                    <Button type="submit" disabled={isPending}>{isPending ? "Adding..." : "Add"}</Button>
                  </div>
                </form>
              </Form>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        <div className="relative mb-4">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input placeholder="Search roles..." onChange={(e) => setSearchTerm(e.target.value)} className="pl-8" />
        </div>
        <div className="border rounded-md">
          <Table>
            <TableHeader><TableRow><TableHead>Role</TableHead><TableHead className="text-right">Actions</TableHead></TableRow></TableHeader>
            <TableBody>
              {filteredRoles.map((role) => (
                <TableRow key={role.id} onClick={() => onRoleSelect?.(role)} className={selectedRoleId === role.id ? "bg-muted/50" : "cursor-pointer"}>
                  <TableCell>
                    <div className="font-medium">{role.name}</div>
                    <div className="text-sm text-muted-foreground">{role.description}</div>
                  </TableCell>
                  <TableCell className="text-right">
                    <Button variant="ghost" size="icon" onClick={(e) => { e.stopPropagation(); openEditDialog(role); }}>
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="icon" onClick={(e) => { e.stopPropagation(); openDeleteDialog(role); }}>
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </CardContent>

      {/* Edit Role Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader><DialogTitle>Edit Role</DialogTitle></DialogHeader>
          <Form {...editForm}>
            <form onSubmit={editForm.handleSubmit(handleUpdateRole)} className="space-y-4">
              <FormField control={editForm.control} name="name" render={({ field }) => (
                <FormItem><FormLabel>Name</FormLabel><FormControl><Input {...field} /></FormControl><FormMessage /></FormItem>
              )} />
              <FormField control={editForm.control} name="description" render={({ field }) => (
                <FormItem><FormLabel>Description</FormLabel><FormControl><Textarea {...field} /></FormControl><FormMessage /></FormItem>
              )} />
              <div className="flex justify-end space-x-2">
                <Button type="button" variant="outline" onClick={() => setIsEditDialogOpen(false)}>Cancel</Button>
                <Button type="submit" disabled={isPending}>{isPending ? "Saving..." : "Save"}</Button>
              </div>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Delete Role Alert */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader><AlertDialogTitle>Are you sure?</AlertDialogTitle></AlertDialogHeader>
          <AlertDialogDescription>
            This action cannot be undone. This will permanently delete the role and revoke its permissions from all users.
          </AlertDialogDescription>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteRole} disabled={isPending}>
              {isPending ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Card>
  );
}
