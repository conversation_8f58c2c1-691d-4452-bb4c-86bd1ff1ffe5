import { createClient } from '@/utils/supabase/client';
import type { User } from '@/types/users';

export interface AuditLogEntry {
  id: string;
  userId: string;
  action: string;
  resource: string;
  resourceId: string;
  entityId?: string;
  oldValues?: Record<string, any>;
  newValues?: Record<string, any>;
  metadata?: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  timestamp: Date;
  user?: User;
}

export interface CreateAuditLogData {
  userId: string;
  action: string;
  resource: string;
  resourceId: string;
  entityId?: string;
  oldValues?: Record<string, any>;
  newValues?: Record<string, any>;
  metadata?: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
}

/**
 * AuditService provides static methods for creating and retrieving audit trail logs.
 * This class is intended for server-side use only.
 */
export class AuditService {
  /**
   * Create a new audit log entry.
   */
  static async log(
    action: string,
    resource: string,
    resourceId: string,
    details: {
      userId: string;
      entityId?: string;
      oldValues?: Record<string, any>;
      newValues?: Record<string, any>;
      metadata?: Record<string, any>;
      ipAddress?: string;
      userAgent?: string;
    }
  ): Promise<void> {
    try {
      const supabase = createClient(); // Correctly awaiting the async function
      const { error } = await supabase
        .from('audit_logs')
        .insert({
          user_id: details.userId,
          action,
          resource_type: resource,
          resource_id: resourceId,
          old_values: details.oldValues,
          new_values: details.newValues,
          ip_address: details.ipAddress,
          user_agent: details.userAgent,
        });

      if (error) {
        console.error(`Failed to create audit log: ${error.message}`);
        // We log the error but don't throw, as auditing might not be a critical failure path.
      }
    } catch (error) {
      console.error('Error in AuditService.log:', error);
    }
  }
}
