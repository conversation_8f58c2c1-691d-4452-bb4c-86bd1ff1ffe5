// Script de test pour vérifier le service utilisateur
// Exécutez avec: node src/test-user-service.js

const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Variables d\'environnement Supabase manquantes');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function testUserQuery() {
  console.log('🧪 Test de la requête utilisateurs...');
  
  try {
    // Test de la requête utilisateurs avec jointures
    const { data, error } = await supabase
      .from('auth_users')
      .select(`
        *,
        user_profiles (
          id,
          display_name,
          avatar_url,
          bio,
          preferences,
          notification_settings,
          dashboard_config,
          created_at,
          updated_at
        ),
        user_roles (
          id,
          granted_at,
          expires_at,
          is_active,
          roles (
            id,
            name,
            display_name,
            description,
            level,
            is_system_role
          )
        )
      `)
      .eq('is_active', true)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('❌ Erreur:', error);
      return;
    }

    console.log('✅ Requête réussie!');
    console.log(`📊 Nombre d'utilisateurs trouvés: ${data.length}`);
    
    if (data.length > 0) {
      console.log('👤 Premier utilisateur:');
      const user = data[0];
      console.log(`  - Email: ${user.email}`);
      console.log(`  - Profil: ${user.user_profiles?.[0]?.display_name || 'Aucun'}`);
      console.log(`  - Rôles: ${user.user_roles?.map(ur => ur.roles?.name).join(', ') || 'Aucun'}`);
    }

  } catch (err) {
    console.error('💥 Erreur inattendue:', err);
  }
}

async function testPermissionCheck() {
  console.log('\n🔐 Test de vérification des permissions...');
  
  try {
    // Test de la fonction RPC
    const { data, error } = await supabase.rpc('check_user_permission', {
      p_user_id: '550e8400-e29b-41d4-a716-************', // UUID de test
      p_permission_name: 'admin.access'
    });

    if (error) {
      console.error('❌ Erreur RPC:', error);
      return;
    }

    console.log('✅ Fonction RPC fonctionne!');
    console.log(`🔑 Permission admin.access: ${data ? 'Accordée' : 'Refusée'}`);

  } catch (err) {
    console.error('💥 Erreur RPC:', err);
  }
}

async function main() {
  console.log('🚀 Démarrage des tests...\n');
  
  await testUserQuery();
  await testPermissionCheck();
  
  console.log('\n✨ Tests terminés!');
}

main().catch(console.error);
