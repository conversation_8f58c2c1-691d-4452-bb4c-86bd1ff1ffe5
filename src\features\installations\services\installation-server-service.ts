// Installation Server Service - KYA Dashboards
// This service is for server-side operations only (Server Components, API routes, etc.)

import { createClient } from '@/utils/supabase/server';
import type { 
  Installation, 
  InstallationTracking, 
  CreateInstallationData, 
  UpdateInstallationData, 
  InstallationTrackingData,
  InstallationStats,
  InstallationKPIs,
  InstallationSearchParams,
  ProductType
} from '../types';

export class InstallationServerService {
  
  // Main Installation CRUD Operations
  static async createInstallation(data: CreateInstallationData, userId: string): Promise<Installation> {
    const supabase = await createClient();
    
    const installationData = {
      ...data,
      created_by: userId,
      status: 'PLANNING' as const
    };
    
    const { data: installation, error } = await supabase
      .from('installations')
      .insert(installationData)
      .select(`
        *,
        client:clients(*),
        project:projects(*),
        team_leader:persons(*)
      `)
      .single();
    
    if (error) throw error;
    return installation;
  }
  
  static async updateInstallation(id: string, data: UpdateInstallationData, userId: string): Promise<Installation> {
    const supabase = await createClient();
    
    const updateData = {
      ...data,
      updated_by: userId,
      updated_at: new Date().toISOString()
    };
    
    const { data: installation, error } = await supabase
      .from('installations')
      .update(updateData)
      .eq('id', id)
      .select(`
        *,
        client:clients(*),
        project:projects(*),
        team_leader:persons(*)
      `)
      .single();
    
    if (error) throw error;
    return installation;
  }
  
  static async getInstallation(id: string): Promise<Installation | null> {
    const supabase = await createClient();
    
    const { data: installation, error } = await supabase
      .from('installations')
      .select(`
        *,
        client:clients(*),
        project:projects(*),
        team_leader:persons(*),
        latest_tracking:installations_tracking(*)
      `)
      .eq('id', id)
      .single();
    
    if (error) return null;
    return installation;
  }
  
  static async getInstallations(params: InstallationSearchParams = {}): Promise<Installation[]> {
    const supabase = await createClient();
    
    let query = supabase
      .from('installations')
      .select(`
        *,
        client:clients(*),
        project:projects(*),
        team_leader:persons(*),
        latest_tracking:installations_tracking(
          global_progress,
          tracking_date,
          daily_comments
        )
      `);
    
    // Apply filters
    if (params.filters?.product_type) {
      query = query.eq('product_type', params.filters.product_type);
    }
    
    if (params.filters?.status) {
      query = query.eq('status', params.filters.status);
    }
    
    if (params.filters?.team_leader_id) {
      query = query.eq('team_leader_id', params.filters.team_leader_id);
    }
    
    if (params.filters?.client_id) {
      query = query.eq('client_id', params.filters.client_id);
    }
    
    if (params.filters?.date_range) {
      query = query
        .gte('planned_start_date', params.filters.date_range.start.toISOString())
        .lte('planned_end_date', params.filters.date_range.end.toISOString());
    }
    
    // Apply search
    if (params.query) {
      query = query.or(`name.ilike.%${params.query}%,installation_number.ilike.%${params.query}%`);
    }
    
    // Apply sorting
    const sortBy = params.sort_by || 'created_at';
    const sortOrder = params.sort_order || 'desc';
    query = query.order(sortBy, { ascending: sortOrder === 'asc' });
    
    // Apply pagination
    if (params.page && params.limit) {
      const from = (params.page - 1) * params.limit;
      const to = from + params.limit - 1;
      query = query.range(from, to);
    }
    
    const { data: installations, error } = await query;
    
    if (error) throw error;
    return installations || [];
  }
  
  static async deleteInstallation(id: string): Promise<void> {
    const supabase = await createClient();
    
    const { error } = await supabase
      .from('installations')
      .delete()
      .eq('id', id);
    
    if (error) throw error;
  }
  
  // Installation Tracking Operations
  static async updateDailyTracking(
    installationId: string, 
    data: InstallationTrackingData, 
    userId: string
  ): Promise<InstallationTracking> {
    const supabase = await createClient();
    const today = new Date().toISOString().split('T')[0];
    
    // Check if tracking exists for today
    const { data: existingTracking } = await supabase
      .from('installations_tracking')
      .select('*')
      .eq('installation_id', installationId)
      .eq('tracking_date', today)
      .single();
    
    if (existingTracking) {
      // Update existing tracking (same day)
      const { data: tracking, error } = await supabase
        .from('installations_tracking')
        .update({
          ...data,
          updated_by: userId,
          updated_at: new Date().toISOString()
        })
        .eq('installation_id', installationId)
        .eq('tracking_date', today)
        .select()
        .single();
      
      if (error) throw error;
      return tracking;
    } else {
      // Create new tracking (different day)
      const { data: tracking, error } = await supabase
        .from('installations_tracking')
        .insert({
          installation_id: installationId,
          ...data,
          tracking_date: today,
          created_by: userId
        })
        .select()
        .single();
      
      if (error) throw error;
      return tracking;
    }
  }
  
  static async getTodayTracking(installationId: string): Promise<InstallationTracking | null> {
    const supabase = await createClient();
    const today = new Date().toISOString().split('T')[0];
    
    const { data: tracking, error } = await supabase
      .from('installations_tracking')
      .select('*')
      .eq('installation_id', installationId)
      .eq('tracking_date', today)
      .single();
    
    if (error) return null;
    return tracking;
  }
  
  static async getTrackingHistory(installationId: string): Promise<InstallationTracking[]> {
    const supabase = await createClient();
    
    const { data: trackings, error } = await supabase
      .from('installations_tracking')
      .select('*')
      .eq('installation_id', installationId)
      .order('tracking_date', { ascending: false });
    
    if (error) throw error;
    return trackings || [];
  }
  
  // Statistics and KPIs
  static async getInstallationStats(teamLeaderId?: string, entityId?: string): Promise<InstallationStats> {
    const supabase = await createClient();

    try {
      // Build query with optional filters
      let query = supabase
        .from('installations')
        .select(`
          *,
          installations_tracking(global_progress)
        `);

      if (teamLeaderId) {
        query = query.eq('team_leader_id', teamLeaderId);
      }

      if (entityId) {
        query = query.eq('entity_id', entityId);
      }

      const { data: installations, error } = await query;

      if (error) throw error;

      const now = new Date();
      const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

      const total_installations = installations?.length || 0;
      const completed_installations = installations?.filter(i => i.status === 'COMPLETED').length || 0;
      const active_installations = installations?.filter(i =>
        i.status === 'IN_PROGRESS' || i.status === 'PLANNING'
      ).length || 0;

      const overdue_installations = installations?.filter(i =>
        i.planned_end_date && new Date(i.planned_end_date) < now && i.status !== 'COMPLETED'
      ).length || 0;

      const this_month_completed = installations?.filter(i =>
        i.actual_end_date && new Date(i.actual_end_date) >= startOfMonth
      ).length || 0;

      // Calculate average progress from latest tracking
      const installationsWithProgress = installations?.filter(i =>
        i.installations_tracking && i.installations_tracking.length > 0
      ) || [];

      const average_progress = installationsWithProgress.length > 0
        ? installationsWithProgress.reduce((sum, i) => {
            const latestTracking = i.installations_tracking[0];
            return sum + (latestTracking?.global_progress || 0);
          }, 0) / installationsWithProgress.length
        : 0;

      return {
        total_installations,
        active_installations,
        completed_installations,
        average_progress,
        overdue_installations,
        this_month_completed
      };
    } catch (error) {
      console.error('Error fetching installation stats:', error);
      // Return empty stats instead of mock data
      return {
        total_installations: 0,
        active_installations: 0,
        completed_installations: 0,
        average_progress: 0,
        overdue_installations: 0,
        this_month_completed: 0
      };
    }
  }
  
  // Utility functions
  static async generateInstallationNumber(): Promise<string> {
    const supabase = await createClient();
    const year = new Date().getFullYear();
    const prefix = `INST-${year}-`;
    
    // Get the last installation number for this year
    const { data: lastInstallation } = await supabase
      .from('installations')
      .select('installation_number')
      .like('installation_number', `${prefix}%`)
      .order('installation_number', { ascending: false })
      .limit(1)
      .single();
    
    let nextNumber = 1;
    if (lastInstallation) {
      const lastNumber = parseInt(lastInstallation.installation_number.split('-')[2]);
      nextNumber = lastNumber + 1;
    }
    
    return `${prefix}${nextNumber.toString().padStart(3, '0')}`;
  }
  
  static getProgressStepsForProduct(productType: ProductType): string[] {
    const commonSteps = ['execution_file_progress', 'metalwork_progress', 'excavation_progress'];
    
    if (productType === 'KYA-SoP') {
      return [
        ...commonSteps,
        'pv_supports_progress',
        'modules_wiring_progress',
        'pv_inverter_cables_progress',
        'inverters_wiring_progress',
        'batteries_wiring_progress',
        'ac_dc_boxes_progress',
        'load_separation_progress',
        'battery_inverter_connection_progress',
        'grounding_progress'
      ];
    } else if (productType === 'Lampadaire') {
      return [
        ...commonSteps,
        'pole_installation_progress',
        'lamp_installation_progress'
      ];
    }
    
    return commonSteps;
  }

  static async getLatestTracking(installationId: string): Promise<any | null> {
    const supabase = await createClient();

    try {
      const { data, error } = await supabase
        .from('installations_tracking')
        .select('*')
        .eq('installation_id', installationId)
        .order('tracking_date', { ascending: false })
        .limit(1)
        .single();

      if (error && error.code !== 'PGRST116') {
        console.error('Error fetching latest tracking:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error fetching latest tracking:', error);
      return null;
    }
  }
}
