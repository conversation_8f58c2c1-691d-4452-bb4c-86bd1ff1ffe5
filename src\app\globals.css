@import "tailwindcss";
@import "tw-animate-css";
@import "../styles/leaflet.css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: var(--kya-primary);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.55 0.15 170 / 0.1);
  --secondary-foreground: var(--kya-primary);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: var(--kya-secondary);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: var(--kya-primary);
  --chart-2: var(--kya-secondary);
  --chart-3: var(--kya-accent);
  --chart-4: var(--kya-brown);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: var(--kya-primary);
  --sidebar-primary-foreground: var(--kya-white);
  --sidebar-accent: oklch(0.55 0.15 170 / 0.1);
  --sidebar-accent-foreground: var(--kya-primary);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);

  /* Couleurs de marque KYA */
  --kya-primary: oklch(0.55 0.15 170); /* #1ca18c - Vert principal */
  --kya-secondary: oklch(0.75 0.15 45); /* #f99d32 - Orange */
  --kya-accent: oklch(0.85 0.15 85); /* #e8e748 - Jaune */
  --kya-brown: oklch(0.35 0.08 35); /* #875028 - Marron */
  --kya-white: oklch(1 0 0); /* #ffffff - Blanc */

  /* Couleurs fonctionnelles basées sur la marque */
  --success: var(--kya-primary);
  --warning: var(--kya-secondary);
  --info: oklch(0.6 0.15 220);
  --error: oklch(0.577 0.245 27.325);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  /* Gradients de marque KYA */
  .gradient-kya-primary {
    background: linear-gradient(135deg, oklch(var(--kya-primary)), oklch(var(--kya-primary) / 0.8));
  }

  .gradient-kya-secondary {
    background: linear-gradient(135deg, oklch(var(--kya-secondary)), oklch(var(--kya-secondary) / 0.8));
  }

  .gradient-kya-accent {
    background: linear-gradient(135deg, oklch(var(--kya-accent)), oklch(var(--kya-accent) / 0.8));
  }

  /* Ombres personnalisées */
  .shadow-kya-card {
    box-shadow: 0 4px 6px -1px rgba(28, 161, 140, 0.1), 0 2px 4px -1px rgba(28, 161, 140, 0.06);
  }

  .shadow-kya-card-hover {
    box-shadow: 0 10px 15px -3px rgba(28, 161, 140, 0.1), 0 4px 6px -2px rgba(28, 161, 140, 0.05);
  }

  /* Animations personnalisées */
  .animate-pulse-kya {
    animation: pulse-kya 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  @keyframes pulse-kya {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.8;
    }
  }

  /* Classes utilitaires pour les KPI cards */
  .kpi-card {
    @apply bg-white rounded-xl transition-all duration-300;
    box-shadow: 0 4px 6px -1px rgba(28, 161, 140, 0.1), 0 2px 4px -1px rgba(28, 161, 140, 0.06);
  }

  .kpi-card:hover {
    box-shadow: 0 10px 15px -3px rgba(28, 161, 140, 0.1), 0 4px 6px -2px rgba(28, 161, 140, 0.05);
  }

  .kpi-card-critical {
    @apply border-l-4 border-red-500 bg-red-50/50;
  }

  .kpi-card-warning {
    @apply border-l-4 border-orange-500 bg-orange-50/50;
  }

  .kpi-card-success {
    @apply border-l-4 border-green-500 bg-green-50/50;
  }

  .kpi-card-info {
    @apply border-l-4 border-blue-500 bg-blue-50/50;
  }

  /* Styles compacts pour les filtres */
  .filters-compact {
    @apply py-2 px-4;
  }

  .filters-compact .filter-item {
    @apply py-1 px-2 text-sm;
  }

  .filters-compact .filter-label {
    @apply text-xs font-medium;
  }

  /* Styles pour les cartes Leaflet */
  .leaflet-container {
    z-index: 1 !important;
  }

  .leaflet-control-container {
    z-index: 2 !important;
  }

  .leaflet-popup {
    z-index: 3 !important;
  }
}
