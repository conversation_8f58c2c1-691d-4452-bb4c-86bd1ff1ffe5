// Dynamic Commercial Page - KYA Dashboards
import { Suspense } from 'react';
import { notFound } from 'next/navigation';
import { getEntityWithMappingAction } from '@/features/entities/actions';
import { Card, CardContent } from '@/components/ui/card';
import type { Metadata } from 'next';

// Placeholder component for commercial dashboard
function CommercialDashboard({ entityId }: { entityId: string }) {
  return (
    <div className="space-y-6">
      <Card>
        <CardContent className="pt-6">
          <h2 className="text-xl font-semibold mb-4">Commercial Dashboard</h2>
          <p className="text-muted-foreground">
            Module commercial pour l'entité {entityId}
          </p>
          <div className="mt-4 p-4 bg-green-50 rounded-lg">
            <p className="text-sm text-green-700">
              🚧 Ce module sera implémenté dans une version future.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

// Loading component
function DashboardLoading() {
  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <div className="h-8 bg-gray-200 rounded w-1/3 animate-pulse"></div>
        <div className="h-4 bg-gray-200 rounded w-1/2 animate-pulse"></div>
      </div>
      <Card>
        <CardContent className="pt-6">
          <div className="animate-pulse space-y-4">
            <div className="h-6 bg-gray-200 rounded w-1/4"></div>
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-32 bg-gray-200 rounded w-full"></div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

interface PageProps {
  params: {
    entityId: string;
  };
}

export default async function DynamicCommercialPage({ params }: PageProps) {
  const { entityId } = params;

  const result = await getEntityWithMappingAction(entityId);

  if (!result.data || !result.data.featureMapping ||
      result.data.featureMapping.businessModule !== 'commercial') {
    notFound();
  }

  const entityWithMapping = result.data;

  return (
    <div className="container mx-auto py-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">
          Commercial - {entityWithMapping.name}
        </h1>
        <p className="text-muted-foreground">
          Gestion commerciale pour {entityWithMapping.name}
        </p>
      </div>
      
      <Suspense fallback={<DashboardLoading />}>
        <CommercialDashboard entityId={entityId} />
      </Suspense>
    </div>
  );
}

export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const { entityId } = params;
  
  try {
    const result = await getEntityWithMappingAction(entityId);

    if (!result.data) {
      return {
        title: 'Entité non trouvée | KYA Dashboards',
        description: 'L\'entité demandée n\'existe pas.',
      };
    }

    return {
      title: `Commercial - ${result.data.name} | KYA Dashboards`,
      description: `Gestion commerciale pour ${result.data.name}`,
    };
  } catch (error) {
    return {
      title: 'Erreur | KYA Dashboards',
      description: 'Une erreur est survenue lors du chargement de la page.',
    };
  }
}
