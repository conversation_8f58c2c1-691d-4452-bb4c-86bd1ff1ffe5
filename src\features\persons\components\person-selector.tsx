'use client';

import { useState, useEffect } from 'react';
import { Check, ChevronsUpDown, User, Search } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { getPersonsAction } from '../actions';
import type { PersonWithEntities } from '../types';

interface PersonSelectorProps {
  value?: string;
  onValueChange: (value: string | undefined) => void;
  placeholder?: string;
  disabled?: boolean;
  multiple?: boolean;
  values?: string[];
  onValuesChange?: (values: string[]) => void;
  entityId?: string; // Filter persons by entity
  excludeIds?: string[]; // Exclude specific person IDs
  className?: string;
}

export function PersonSelector({
  value,
  onValueChange,
  placeholder = "Sélectionner une personne...",
  disabled = false,
  multiple = false,
  values = [],
  onValuesChange,
  entityId,
  excludeIds = [],
  className,
}: PersonSelectorProps) {
  const [open, setOpen] = useState(false);
  const [persons, setPersons] = useState<PersonWithEntities[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  // Load persons
  useEffect(() => {
    const loadPersons = async () => {
      setIsLoading(true);
      try {
        const filters = {
          isActive: true,
          ...(entityId && { entityId }),
          ...(searchTerm && { search: searchTerm }),
        };

        const result = await getPersonsAction(filters, { field: 'lastName', direction: 'asc' }, { page: 1, limit: 100 });
        
        if (result.data) {
          let filteredPersons = result.data.data;
          
          // Exclude specific IDs
          if (excludeIds.length > 0) {
            filteredPersons = filteredPersons.filter(person => !excludeIds.includes(person.id));
          }
          
          setPersons(filteredPersons);
        }
      } catch (error) {
        console.error('Error loading persons:', error);
        setPersons([]);
      } finally {
        setIsLoading(false);
      }
    };

    loadPersons();
  }, [entityId, excludeIds, searchTerm]);

  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  const getSelectedPerson = (personId: string) => {
    return persons.find(person => person.id === personId);
  };

  const getSelectedPersons = () => {
    return persons.filter(person => values.includes(person.id));
  };

  const handleSelect = (personId: string) => {
    if (multiple && onValuesChange) {
      const newValues = values.includes(personId)
        ? values.filter(id => id !== personId)
        : [...values, personId];
      onValuesChange(newValues);
    } else {
      onValueChange(value === personId ? undefined : personId);
      setOpen(false);
    }
  };

  const selectedPerson = value ? getSelectedPerson(value) : null;
  const selectedPersons = multiple ? getSelectedPersons() : [];

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn("justify-between", className)}
          disabled={disabled}
        >
          <div className="flex items-center gap-2 flex-1 min-w-0">
            {multiple ? (
              selectedPersons.length > 0 ? (
                <div className="flex items-center gap-1 flex-wrap">
                  {selectedPersons.slice(0, 2).map((person) => (
                    <Badge key={person.id} variant="secondary" className="text-xs">
                      {person.fullName}
                    </Badge>
                  ))}
                  {selectedPersons.length > 2 && (
                    <Badge variant="outline" className="text-xs">
                      +{selectedPersons.length - 2}
                    </Badge>
                  )}
                </div>
              ) : (
                <span className="text-muted-foreground">{placeholder}</span>
              )
            ) : selectedPerson ? (
              <div className="flex items-center gap-2">
                <Avatar className="h-6 w-6">
                  <AvatarImage src="" alt={selectedPerson.fullName} />
                  <AvatarFallback className="text-xs">
                    {getInitials(selectedPerson.firstName, selectedPerson.lastName)}
                  </AvatarFallback>
                </Avatar>
                <span className="truncate">{selectedPerson.fullName}</span>
                {selectedPerson.position && (
                  <Badge variant="outline" className="text-xs">
                    {selectedPerson.position}
                  </Badge>
                )}
              </div>
            ) : (
              <span className="text-muted-foreground">{placeholder}</span>
            )}
          </div>
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[400px] p-0">
        <Command>
          <div className="flex items-center border-b px-3">
            <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
            <input
              placeholder="Rechercher une personne..."
              className="flex h-11 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <div className="max-h-[300px] overflow-y-auto">
            {isLoading ? (
              <div className="py-6 text-center text-sm">Chargement...</div>
            ) : persons.length === 0 ? (
              <CommandEmpty>Aucune personne trouvée.</CommandEmpty>
            ) : (
              <CommandGroup>
                {persons.map((person) => {
                  const isSelected = multiple 
                    ? values.includes(person.id)
                    : value === person.id;

                  return (
                    <CommandItem
                      key={person.id}
                      value={person.id}
                      onSelect={() => handleSelect(person.id)}
                      className="flex items-center gap-2 p-2"
                    >
                      <div className="flex items-center gap-2 flex-1 min-w-0">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src="" alt={person.fullName} />
                          <AvatarFallback className="text-xs">
                            {getInitials(person.firstName, person.lastName)}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1 min-w-0">
                          <div className="font-medium truncate">{person.fullName}</div>
                          <div className="text-sm text-muted-foreground flex items-center gap-2">
                            {person.position && (
                              <span className="truncate">{person.position}</span>
                            )}
                            {person.employeeId && (
                              <Badge variant="outline" className="text-xs">
                                {person.employeeId}
                              </Badge>
                            )}
                          </div>
                          {person.primaryEntity && (
                            <div className="text-xs text-muted-foreground">
                              {person.primaryEntity.name}
                            </div>
                          )}
                        </div>
                      </div>
                      <Check
                        className={cn(
                          "h-4 w-4",
                          isSelected ? "opacity-100" : "opacity-0"
                        )}
                      />
                    </CommandItem>
                  );
                })}
              </CommandGroup>
            )}
          </div>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
