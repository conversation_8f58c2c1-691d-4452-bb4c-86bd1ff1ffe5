import { ReactNode } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { AlertTriangle, Clock, CheckCircle, XCircle } from 'lucide-react';

export type AlertSeverity = 'low' | 'medium' | 'high' | 'critical';
export type AlertStatus = 'active' | 'acknowledged' | 'resolved' | 'dismissed';

interface AlertCardProps {
  id: string;
  title: string;
  description: string;
  severity: AlertSeverity;
  status: AlertStatus;
  timestamp: Date;
  source?: string;
  actions?: Array<{
    label: string;
    variant?: 'default' | 'destructive' | 'outline' | 'secondary';
    onClick: () => void;
  }>;
  metadata?: Record<string, any>;
  className?: string;
}

const severityConfig = {
  low: {
    icon: Clock,
    color: 'text-blue-600',
    bgColor: 'bg-blue-50',
    borderColor: 'border-blue-200',
    badgeVariant: 'secondary' as const,
  },
  medium: {
    icon: AlertTriangle,
    color: 'text-yellow-600',
    bgColor: 'bg-yellow-50',
    borderColor: 'border-yellow-200',
    badgeVariant: 'outline' as const,
  },
  high: {
    icon: AlertTriangle,
    color: 'text-orange-600',
    bgColor: 'bg-orange-50',
    borderColor: 'border-orange-200',
    badgeVariant: 'destructive' as const,
  },
  critical: {
    icon: XCircle,
    color: 'text-red-600',
    bgColor: 'bg-red-50',
    borderColor: 'border-red-200',
    badgeVariant: 'destructive' as const,
  },
};

const statusConfig = {
  active: { label: 'Actif', color: 'text-red-600' },
  acknowledged: { label: 'Accusé', color: 'text-yellow-600' },
  resolved: { label: 'Résolu', color: 'text-green-600' },
  dismissed: { label: 'Ignoré', color: 'text-gray-600' },
};

export function AlertCard({
  id,
  title,
  description,
  severity,
  status,
  timestamp,
  source,
  actions = [],
  metadata,
  className,
}: AlertCardProps) {
  const config = severityConfig[severity];
  const statusInfo = statusConfig[status];
  const Icon = config.icon;

  const formatTimestamp = (date: Date) => {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'À l\'instant';
    if (diffInMinutes < 60) return `Il y a ${diffInMinutes}min`;
    if (diffInMinutes < 1440) return `Il y a ${Math.floor(diffInMinutes / 60)}h`;
    return date.toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  return (
    <Card className={cn(
      'transition-all duration-200 hover:shadow-md',
      config.bgColor,
      config.borderColor,
      'border-l-4',
      status === 'active' && 'animate-pulse-kya',
      className
    )}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-2">
            <Icon className={cn('h-5 w-5', config.color)} />
            <CardTitle className="text-base font-semibold">{title}</CardTitle>
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant={config.badgeVariant} className="text-xs">
              {severity.toUpperCase()}
            </Badge>
            <Badge variant="outline" className={cn('text-xs', statusInfo.color)}>
              {statusInfo.label}
            </Badge>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        <p className="text-sm text-gray-700">{description}</p>
        
        <div className="flex items-center justify-between text-xs text-gray-500">
          <span>{formatTimestamp(timestamp)}</span>
          {source && <span>Source: {source}</span>}
        </div>
        
        {metadata && Object.keys(metadata).length > 0 && (
          <div className="bg-white/50 rounded-lg p-3 space-y-1">
            {Object.entries(metadata).map(([key, value]) => (
              <div key={key} className="flex justify-between text-xs">
                <span className="font-medium text-gray-600">{key}:</span>
                <span className="text-gray-800">{String(value)}</span>
              </div>
            ))}
          </div>
        )}
        
        {actions.length > 0 && (
          <div className="flex flex-wrap gap-2 pt-2">
            {actions.map((action, index) => (
              <Button
                key={index}
                size="sm"
                variant={action.variant || 'outline'}
                onClick={action.onClick}
                className="text-xs"
              >
                {action.label}
              </Button>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}

// Composant pour afficher une liste d'alertes
interface AlertListProps {
  alerts: Array<Omit<AlertCardProps, 'className'>>;
  className?: string;
  maxHeight?: string;
}

export function AlertList({ alerts, className, maxHeight = 'max-h-96' }: AlertListProps) {
  if (alerts.length === 0) {
    return (
      <Card className={cn('p-8 text-center', className)}>
        <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-gray-700 mb-2">Aucune alerte</h3>
        <p className="text-sm text-gray-500">Tout fonctionne normalement</p>
      </Card>
    );
  }

  return (
    <div className={cn('space-y-4', maxHeight, 'overflow-y-auto', className)}>
      {alerts.map((alert) => (
        <AlertCard key={alert.id} {...alert} />
      ))}
    </div>
  );
}

// Hook pour gérer les alertes
export function useAlerts() {
  // Cette logique sera implémentée avec les données réelles
  return {
    alerts: [],
    acknowledgeAlert: (id: string) => {},
    resolveAlert: (id: string) => {},
    dismissAlert: (id: string) => {},
  };
}
