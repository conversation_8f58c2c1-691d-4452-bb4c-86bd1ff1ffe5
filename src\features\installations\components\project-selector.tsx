'use client';

import { useState, useEffect } from 'react';
import { Check, ChevronsUpDown, Plus, FolderOpen, Calendar } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Badge } from '@/components/ui/badge';
import { useProjectsByClient } from '@/hooks/use-projects';
import { CreateProjectModal } from './create-project-modal';
import type { Project } from '@/types/shared';

interface ProjectSelectorProps {
  value: string;
  onValueChange: (value: string) => void;
  clientId: string;
  disabled?: boolean;
}

export function ProjectSelector({ value, onValueChange, clientId, disabled }: ProjectSelectorProps) {
  const [open, setOpen] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const { data: projects = [], isLoading } = useProjectsByClient(clientId);
  const selectedProject = projects.find((project) => project.id === value);

  // Reset selection when client changes
  useEffect(() => {
    if (value && !projects.find(p => p.id === value)) {
      onValueChange('');
    }
  }, [clientId, projects, value, onValueChange]);

  const getStatusBadge = (status: string) => {
    const variants = {
      ACTIVE: { color: 'bg-green-100 text-green-700', label: 'Actif' },
      COMPLETED: { color: 'bg-blue-100 text-blue-700', label: 'Terminé' },
      CANCELLED: { color: 'bg-red-100 text-red-700', label: 'Annulé' },
      ON_HOLD: { color: 'bg-yellow-100 text-yellow-700', label: 'En attente' },
    };
    
    const config = variants[status as keyof typeof variants] || variants.ACTIVE;
    
    return (
      <Badge variant="secondary" className={config.color}>
        {config.label}
      </Badge>
    );
  };

  const formatDate = (date: Date | string | null) => {
    if (!date) return null;
    return new Date(date).toLocaleDateString('fr-FR', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const handleProjectCreated = (newProject: Project) => {
    onValueChange(newProject.id);
    setShowCreateModal(false);
  };

  const isDisabled = disabled || !clientId;

  return (
    <>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between bg-white"
            disabled={isDisabled}
          >
            {selectedProject ? (
              <div className="flex items-center gap-2 flex-1 min-w-0">
                <FolderOpen className="h-4 w-4" />
                <span className="truncate">{selectedProject.name}</span>
                {getStatusBadge(selectedProject.status)}
              </div>
            ) : (
              <span className="text-muted-foreground">
                {!clientId ? 'Sélectionner d\'abord un client' : 'Sélectionner un projet...'}
              </span>
            )}
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0" align="start">
          <Command>
            <CommandInput placeholder="Rechercher un projet..." />
            <CommandList>
              <CommandEmpty>
                <div className="text-center py-4">
                  <p className="text-sm text-muted-foreground mb-3">
                    {!clientId 
                      ? 'Sélectionnez d\'abord un client'
                      : 'Aucun projet trouvé pour ce client'
                    }
                  </p>
                  {clientId && (
                    <Button
                      size="sm"
                      onClick={() => {
                        setOpen(false);
                        setShowCreateModal(true);
                      }}
                      className="bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Créer un nouveau projet
                    </Button>
                  )}
                </div>
              </CommandEmpty>
              
              <CommandGroup>
                {clientId && (
                  <CommandItem
                    onSelect={() => {
                      setOpen(false);
                      setShowCreateModal(true);
                    }}
                    className="border-b"
                  >
                    <Plus className="mr-2 h-4 w-4 text-green-500" />
                    <span className="text-green-600 font-medium">Créer un nouveau projet</span>
                  </CommandItem>
                )}
                
                <CommandItem
                  onSelect={() => {
                    onValueChange('');
                    setOpen(false);
                  }}
                  className={cn(
                    "border-b",
                    !value && "bg-accent"
                  )}
                >
                  <Check
                    className={cn(
                      'mr-2 h-4 w-4',
                      !value ? 'opacity-100' : 'opacity-0'
                    )}
                  />
                  <span className="text-muted-foreground italic">Aucun projet spécifique</span>
                </CommandItem>
                
                {projects.map((project) => (
                  <CommandItem
                    key={project.id}
                    value={`${project.name} ${project.description || ''}`}
                    onSelect={() => {
                      onValueChange(project.id === value ? '' : project.id);
                      setOpen(false);
                    }}
                  >
                    <Check
                      className={cn(
                        'mr-2 h-4 w-4',
                        value === project.id ? 'opacity-100' : 'opacity-0'
                      )}
                    />
                    <div className="flex items-center gap-2 flex-1 min-w-0">
                      <FolderOpen className="h-4 w-4" />
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2">
                          <span className="font-medium truncate">{project.name}</span>
                          {getStatusBadge(project.status)}
                        </div>
                        <div className="flex items-center gap-4 mt-1">
                          {project.description && (
                            <p className="text-xs text-muted-foreground truncate">
                              {project.description}
                            </p>
                          )}
                          {project.start_date && (
                            <div className="flex items-center gap-1 text-xs text-muted-foreground">
                              <Calendar className="h-3 w-3" />
                              {formatDate(project.start_date)}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>

      <CreateProjectModal
        open={showCreateModal}
        onOpenChange={setShowCreateModal}
        onProjectCreated={handleProjectCreated}
        clientId={clientId}
      />
    </>
  );
}
