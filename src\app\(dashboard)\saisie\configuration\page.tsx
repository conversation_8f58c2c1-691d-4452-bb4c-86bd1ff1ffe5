// Page de Configuration Partagée - KYA Dashboards
'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Cog, 
  Users, 
  Building2, 
  FolderOpen, 
  Plus, 
  Settings,
  Database,
  UserCheck,
  MapPin,
  Calendar,
  FileText,
  Zap
} from 'lucide-react';

// Types pour les modules de configuration
interface ConfigModule {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  href: string;
  status: 'active' | 'development' | 'planned';
  count?: number;
  features: string[];
}

// Données des modules de configuration
const configModules: ConfigModule[] = [
  {
    id: 'clients',
    title: 'Gestion des Clients',
    description: 'Créez et gérez vos clients (particuliers et institutions)',
    icon: Users,
    href: '/saisie/configuration/clients',
    status: 'active',
    count: 25,
    features: [
      'Création de clients',
      'Types : Particulier / Institution',
      'Informations de contact',
      'Adresses et coordonnées GPS',
      'Historique des projets'
    ]
  },
  {
    id: 'projects',
    title: 'Gestion des Projets',
    description: 'Organisez vos projets par client avec suivi budgétaire',
    icon: FolderOpen,
    href: '/saisie/configuration/projects',
    status: 'active',
    count: 18,
    features: [
      'Association aux clients',
      'Suivi budgétaire',
      'Dates de projet',
      'Statuts de progression',
      'Documentation associée'
    ]
  },
  {
    id: 'persons',
    title: 'Annuaire des Personnes',
    description: 'Base de données des personnes (équipes, contacts, etc.)',
    icon: UserCheck,
    href: '/saisie/configuration/persons',
    status: 'development',
    count: 42,
    features: [
      'Profils complets',
      'Compétences et rôles',
      'Informations de contact',
      'Affectations aux équipes',
      'Historique des missions'
    ]
  },
  {
    id: 'locations',
    title: 'Gestion des Sites',
    description: 'Référentiel des sites et localisations',
    icon: MapPin,
    href: '/saisie/configuration/locations',
    status: 'planned',
    features: [
      'Coordonnées GPS',
      'Descriptions détaillées',
      'Photos et documents',
      'Conditions d\'accès',
      'Historique des interventions'
    ]
  },
  {
    id: 'equipment',
    title: 'Matériel et Équipements',
    description: 'Inventaire du matériel et équipements techniques',
    icon: Settings,
    href: '/saisie/configuration/equipment',
    status: 'planned',
    features: [
      'Catalogue des équipements',
      'Spécifications techniques',
      'Suivi des stocks',
      'Maintenance préventive',
      'Historique d\'utilisation'
    ]
  },
  {
    id: 'templates',
    title: 'Modèles et Templates',
    description: 'Templates pour installations et configurations type',
    icon: FileText,
    href: '/saisie/configuration/templates',
    status: 'planned',
    features: [
      'Templates d\'installation',
      'Configurations prédéfinies',
      'Checklists type',
      'Procédures standard',
      'Documentation technique'
    ]
  }
];

export default function ConfigurationPage() {
  const [selectedStatus, setSelectedStatus] = useState<string>('all');

  // Filtrage des modules par statut
  const filteredModules = configModules.filter(module => 
    selectedStatus === 'all' || module.status === selectedStatus
  );

  // Fonction pour obtenir le badge de statut
  const getStatusBadge = (status: ConfigModule['status']) => {
    const variants = {
      active: { color: 'bg-green-100 text-green-700', label: 'Actif' },
      development: { color: 'bg-blue-100 text-blue-700', label: 'En Développement' },
      planned: { color: 'bg-gray-100 text-gray-700', label: 'Planifié' },
    };
    
    const config = variants[status];
    return (
      <Badge variant="secondary" className={config.color}>
        {config.label}
      </Badge>
    );
  };

  // Statistiques
  const stats = {
    total: configModules.length,
    active: configModules.filter(m => m.status === 'active').length,
    development: configModules.filter(m => m.status === 'development').length,
    planned: configModules.filter(m => m.status === 'planned').length,
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg">
              <Cog className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Configuration Partagée</h1>
              <p className="text-muted-foreground">
                Gérez les éléments partagés entre tous les modules métier
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Database className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Total Modules</p>
                <p className="text-2xl font-bold">{stats.total}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <Zap className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Actifs</p>
                <p className="text-2xl font-bold">{stats.active}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Settings className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">En Développement</p>
                <p className="text-2xl font-bold">{stats.development}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-gray-100 rounded-lg">
                <Calendar className="h-5 w-5 text-gray-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Planifiés</p>
                <p className="text-2xl font-bold">{stats.planned}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filtres */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Modules de Configuration</CardTitle>
              <CardDescription>
                Accédez aux différents modules de configuration partagée
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant={selectedStatus === 'all' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedStatus('all')}
              >
                Tous
              </Button>
              <Button
                variant={selectedStatus === 'active' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedStatus('active')}
              >
                Actifs
              </Button>
              <Button
                variant={selectedStatus === 'development' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedStatus('development')}
              >
                En Développement
              </Button>
              <Button
                variant={selectedStatus === 'planned' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedStatus('planned')}
              >
                Planifiés
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Grille des modules */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredModules.map((module) => {
              const IconComponent = module.icon;
              const isActive = module.status === 'active';
              
              return (
                <Card 
                  key={module.id} 
                  className={`transition-all duration-200 hover:shadow-lg ${
                    isActive 
                      ? 'border-blue-200 hover:border-blue-300' 
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-3">
                        <div className={`p-2 rounded-lg ${
                          isActive 
                            ? 'bg-blue-100' 
                            : 'bg-gray-100'
                        }`}>
                          <IconComponent className={`h-5 w-5 ${
                            isActive 
                              ? 'text-blue-600' 
                              : 'text-gray-600'
                          }`} />
                        </div>
                        <div>
                          <CardTitle className="text-lg">{module.title}</CardTitle>
                          {module.count && (
                            <p className="text-sm text-muted-foreground">
                              {module.count} éléments
                            </p>
                          )}
                        </div>
                      </div>
                      {getStatusBadge(module.status)}
                    </div>
                    <CardDescription className="mt-2">
                      {module.description}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="space-y-3">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground mb-2">
                          Fonctionnalités :
                        </p>
                        <ul className="text-sm space-y-1">
                          {module.features.slice(0, 3).map((feature, index) => (
                            <li key={index} className="flex items-center gap-2">
                              <div className="w-1.5 h-1.5 bg-blue-500 rounded-full" />
                              {feature}
                            </li>
                          ))}
                          {module.features.length > 3 && (
                            <li className="text-muted-foreground">
                              +{module.features.length - 3} autres...
                            </li>
                          )}
                        </ul>
                      </div>
                      
                      <div className="pt-2">
                        {isActive ? (
                          <Button asChild className="w-full">
                            <Link href={module.href}>
                              Accéder au Module
                            </Link>
                          </Button>
                        ) : (
                          <Button variant="outline" className="w-full" disabled>
                            {module.status === 'development' ? 'En Développement' : 'Bientôt Disponible'}
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
