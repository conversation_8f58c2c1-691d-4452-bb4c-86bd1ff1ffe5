-- Vue pour les détails complets d'installation
-- Cette vue simplifie l'accès aux données complètes d'une installation

CREATE OR REPLACE VIEW installation_details_complete AS
SELECT 
    -- Installation de base
    i.id,
    i.name,
    i.client_id,
    i.project_id,
    i.product_type,
    i.installation_number,
    i.site_location,
    i.gps_coordinates,
    
    -- Spécifications équipement
    i.peak_power,
    i.inverter_specs,
    i.battery_capacity,
    i.equipment_description,
    
    -- Équipe
    i.team_leader_id,
    i.team_members,
    
    -- Planification
    i.planned_start_date,
    i.planned_end_date,
    i.actual_start_date,
    i.actual_end_date,
    i.total_duration_days,
    
    -- Réception technique
    i.technical_reception_date,
    i.technical_reception_status,
    i.technical_reception_notes,
    
    -- Réception provisoire
    i.provisional_reception_date,
    i.provisional_reception_status,
    i.provisional_reception_notes,
    
    -- État général
    i.general_functioning_state,
    
    -- Statut et commentaires
    i.status,
    i.comments,
    
    -- Audit
    i.created_at,
    i.updated_at,
    i.created_by,
    i.updated_by,
    
    -- Informations client
    c.name as client_name,
    c.type as client_type,
    c.contact_info as client_contact_info,
    c.address as client_address,
    c.gps_coordinates as client_gps_coordinates,
    
    -- Informations projet (si applicable)
    p.name as project_name,
    p.description as project_description,
    p.start_date as project_start_date,
    p.end_date as project_end_date,
    p.budget as project_budget,
    p.status as project_status,
    
    -- Informations chef d'équipe
    tl.first_name as team_leader_first_name,
    tl.last_name as team_leader_last_name,
    tl.email as team_leader_email,
    tl.phone as team_leader_phone,
    tl.position as team_leader_position,
    
    -- Dernière progression (depuis la vue latest_installations_tracking)
    lt.tracking_date as latest_tracking_date,
    lt.global_progress,
    lt.execution_file_progress,
    
    -- Progression KYA-SoP
    lt.metalwork_progress,
    lt.excavation_progress,
    lt.pv_supports_progress,
    lt.modules_wiring_progress,
    lt.pv_inverter_cables_progress,
    lt.inverters_wiring_progress,
    lt.batteries_wiring_progress,
    lt.ac_dc_boxes_progress,
    lt.load_separation_progress,
    lt.battery_inverter_connection_progress,
    lt.grounding_progress,
    
    -- Progression Lampadaire
    lt.pole_installation_progress,
    lt.lamp_installation_progress,
    
    -- Tests de mise en service
    lt.commissioning_test_status,
    
    -- Calcul de progression automatique
    CASE 
        WHEN i.product_type = 'KYA-SoP' THEN
            COALESCE((
                COALESCE(lt.execution_file_progress, 0) +
                COALESCE(lt.metalwork_progress, 0) +
                COALESCE(lt.excavation_progress, 0) +
                COALESCE(lt.pv_supports_progress, 0) +
                COALESCE(lt.modules_wiring_progress, 0) +
                COALESCE(lt.pv_inverter_cables_progress, 0) +
                COALESCE(lt.inverters_wiring_progress, 0) +
                COALESCE(lt.batteries_wiring_progress, 0) +
                COALESCE(lt.ac_dc_boxes_progress, 0) +
                COALESCE(lt.load_separation_progress, 0) +
                COALESCE(lt.battery_inverter_connection_progress, 0) +
                COALESCE(lt.grounding_progress, 0)
            ) / 12, 0)
        WHEN i.product_type = 'Lampadaire' THEN
            COALESCE((
                COALESCE(lt.execution_file_progress, 0) +
                COALESCE(lt.metalwork_progress, 0) +
                COALESCE(lt.excavation_progress, 0) +
                COALESCE(lt.pole_installation_progress, 0) +
                COALESCE(lt.lamp_installation_progress, 0)
            ) / 5, 0)
        ELSE 0
    END as calculated_progress,
    
    -- Indicateurs de retard
    CASE 
        WHEN i.actual_end_date IS NOT NULL THEN false
        WHEN i.planned_end_date IS NOT NULL AND i.planned_end_date < CURRENT_DATE THEN true
        ELSE false
    END as is_overdue,
    
    -- Durée écoulée
    CASE 
        WHEN i.actual_start_date IS NOT NULL AND i.actual_end_date IS NOT NULL THEN
            i.actual_end_date - i.actual_start_date
        WHEN i.actual_start_date IS NOT NULL THEN
            CURRENT_DATE - i.actual_start_date
        ELSE NULL
    END as elapsed_days

FROM installations i
LEFT JOIN clients c ON i.client_id = c.id
LEFT JOIN projects p ON i.project_id = p.id
LEFT JOIN persons tl ON i.team_leader_id = tl.id
LEFT JOIN latest_installations_tracking lt ON i.id = lt.installation_id;

-- Index pour optimiser les performances
CREATE INDEX IF NOT EXISTS idx_installation_details_complete_id ON installations(id);
CREATE INDEX IF NOT EXISTS idx_installation_details_complete_status ON installations(status);
CREATE INDEX IF NOT EXISTS idx_installation_details_complete_product_type ON installations(product_type);

-- Commentaire sur la vue
COMMENT ON VIEW installation_details_complete IS 'Vue complète pour les détails d''installation avec toutes les relations et calculs automatiques';
