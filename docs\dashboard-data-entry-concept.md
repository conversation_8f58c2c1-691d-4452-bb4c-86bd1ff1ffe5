# KYA Dashboards & Data Entry Concept

## Core Pattern

**Each business domain = 2 tables**
1. **Main table** - Stable data (rarely changes)
2. **Tracking table** - Daily updates (frequently modified)

**IMPORTANT:** Each business domain has its own specific fields, forms, and functions. The pattern is consistent but the content varies per domain.

## Shared Tables

```sql
-- Shared across all domains
CREATE TABLE clients (
    id UUID PRIMARY KEY,
    name TEXT NOT NULL,
    type TEXT CHECK (type IN ('INDIVIDUAL', 'COMPANY')),
    contact TEXT,
    address TEXT
);

CREATE TABLE projects (
    id UUID PRIMARY KEY,
    name TEXT NOT NULL,
    client_id UUID REFERENCES clients(id),
    description TEXT,
    start_date DATE,
    end_date DATE
);
```

## Domain-Specific Implementation

### Each Domain Has Different:

1. **Fields** - Specific to business needs
2. **Forms** - Tailored to domain workflow
3. **Calculations** - Domain-specific metrics
4. **Validations** - Business rules per domain
5. **Dashboard KPIs** - Relevant metrics only

### Example: Installations Domain

```sql
-- Main table: installation-specific fields
CREATE TABLE installations (
    id UUID PRIMARY KEY,
    name TEXT NOT NULL,
    client_id UUID REFERENCES clients(id),
    project_id UUID REFERENCES projects(id),
    product_type TEXT CHECK (product_type IN ('KYA-SoP', 'Lampadaire')),
    
    -- INSTALLATION-SPECIFIC FIELDS
    site_location TEXT,
    gps_coordinates TEXT,
    equipment_description TEXT,
    project_manager TEXT,
    installation_team TEXT,
    
    created_at TIMESTAMP DEFAULT NOW()
);

-- Tracking table: installation-specific progress
CREATE TABLE installations_tracking (
    id UUID PRIMARY KEY,
    installation_id UUID REFERENCES installations(id),
    tracking_date DATE DEFAULT CURRENT_DATE,
    
    -- INSTALLATION-SPECIFIC PROGRESS FIELDS
    execution_rate DECIMAL,
    construction_progress DECIMAL,
    supply_progress DECIMAL,
    assembly_progress DECIMAL,
    wiring_progress DECIMAL,
    commissioning_progress DECIMAL,
    testing_progress DECIMAL,
    
    -- INSTALLATION-SPECIFIC STATUS FIELDS
    technical_reception_date DATE,
    provisional_reception_date DATE,
    work_duration_days INTEGER,
    
    daily_comments TEXT,
    issues_encountered TEXT,
    
    created_at TIMESTAMP DEFAULT NOW(),
    created_by UUID REFERENCES auth_users(id),
    
    UNIQUE(installation_id, tracking_date)
);
```

### Example: Maintenance Domain (Different Fields)

```sql
-- Main table: maintenance-specific fields
CREATE TABLE maintenance (
    id UUID PRIMARY KEY,
    name TEXT NOT NULL,
    client_id UUID REFERENCES clients(id),
    project_id UUID REFERENCES projects(id),
    product_type TEXT CHECK (product_type IN ('KYA-SoP', 'Lampadaire')),
    
    -- MAINTENANCE-SPECIFIC FIELDS (DIFFERENT FROM INSTALLATIONS)
    installation_id UUID REFERENCES installations(id),
    maintenance_type TEXT,
    equipment_serial TEXT,
    warranty_status TEXT,
    maintenance_contract TEXT,
    
    created_at TIMESTAMP DEFAULT NOW()
);

-- Tracking table: maintenance-specific tracking
CREATE TABLE maintenance_tracking (
    id UUID PRIMARY KEY,
    maintenance_id UUID REFERENCES maintenance(id),
    tracking_date DATE DEFAULT CURRENT_DATE,
    
    -- MAINTENANCE-SPECIFIC FIELDS (COMPLETELY DIFFERENT)
    intervention_type TEXT,
    problem_description TEXT,
    parts_replaced TEXT,
    labor_hours DECIMAL,
    cost_parts DECIMAL,
    cost_labor DECIMAL,
    next_maintenance_date DATE,
    equipment_status TEXT,
    
    technician_notes TEXT,
    client_feedback TEXT,
    
    created_at TIMESTAMP DEFAULT NOW(),
    created_by UUID REFERENCES auth_users(id),
    
    UNIQUE(maintenance_id, tracking_date)
);
```

## Domain-Specific Forms

### Installation Form (Example)
```typescript
interface InstallationTrackingForm {
  // INSTALLATION-SPECIFIC FIELDS
  execution_rate: number;
  construction_progress: number;
  supply_progress: number;
  assembly_progress: number;
  wiring_progress: number;
  commissioning_progress: number;
  
  // INSTALLATION-SPECIFIC VALIDATIONS
  technical_reception_date?: Date;
  provisional_reception_date?: Date;
  work_duration_days?: number;
  
  daily_comments: string;
  issues_encountered?: string;
}
```

### Maintenance Form (Completely Different)
```typescript
interface MaintenanceTrackingForm {
  // MAINTENANCE-SPECIFIC FIELDS (DIFFERENT FROM INSTALLATIONS)
  intervention_type: string;
  problem_description: string;
  parts_replaced?: string;
  labor_hours: number;
  cost_parts?: number;
  cost_labor?: number;
  next_maintenance_date?: Date;
  equipment_status: string;
  
  technician_notes: string;
  client_feedback?: string;
}
```

## Domain-Specific Services

### Each Domain Has Its Own Service Logic

```typescript
// Installation Service - Installation-specific logic
export class InstallationService {
  static async updateDailyTracking(id: string, data: InstallationTrackingData) {
    // INSTALLATION-SPECIFIC VALIDATIONS
    if (data.execution_rate > 100) throw new Error('Invalid execution rate');
    if (data.technical_reception_date && !data.provisional_reception_date) {
      throw new Error('Provisional reception required before technical');
    }
    
    return this.createOrUpdateTodayTracking(id, data);
  }
  
  static async calculateInstallationKPIs(teamId: string) {
    // INSTALLATION-SPECIFIC CALCULATIONS
    return {
      averageExecutionRate: ...,
      installationsOnSchedule: ...,
      averageWorkDuration: ...,
      pendingReceptions: ...
    };
  }
}

// Maintenance Service - Maintenance-specific logic (DIFFERENT)
export class MaintenanceService {
  static async updateDailyTracking(id: string, data: MaintenanceTrackingData) {
    // MAINTENANCE-SPECIFIC VALIDATIONS (DIFFERENT RULES)
    if (data.labor_hours < 0) throw new Error('Invalid labor hours');
    if (data.cost_parts && data.cost_parts < 0) throw new Error('Invalid cost');
    
    return this.createOrUpdateTodayTracking(id, data);
  }
  
  static async calculateMaintenanceKPIs(teamId: string) {
    // MAINTENANCE-SPECIFIC CALCULATIONS (DIFFERENT METRICS)
    return {
      averageInterventionTime: ...,
      totalMaintenanceCost: ...,
      equipmentUptime: ...,
      pendingInterventions: ...
    };
  }
}
```

## Domain-Specific Dashboards

### Installation Dashboard
```typescript
const InstallationDashboard = () => {
  // INSTALLATION-SPECIFIC KPIS
  const kpis = [
    { label: 'Avg Execution Rate', value: '85%' },
    { label: 'On Schedule', value: '12/15' },
    { label: 'Pending Receptions', value: '3' },
    { label: 'Avg Work Duration', value: '45 days' }
  ];
  
  // INSTALLATION-SPECIFIC CHARTS
  return (
    <Dashboard>
      <ProgressChart data={installationProgress} />
      <ReceptionStatusChart data={receptionData} />
      <TeamPerformanceChart data={teamData} />
    </Dashboard>
  );
};
```

### Maintenance Dashboard (Different KPIs)
```typescript
const MaintenanceDashboard = () => {
  // MAINTENANCE-SPECIFIC KPIS (COMPLETELY DIFFERENT)
  const kpis = [
    { label: 'Avg Response Time', value: '2.5h' },
    { label: 'Equipment Uptime', value: '98.5%' },
    { label: 'Monthly Cost', value: '$15,420' },
    { label: 'Pending Interventions', value: '7' }
  ];
  
  // MAINTENANCE-SPECIFIC CHARTS (DIFFERENT FROM INSTALLATIONS)
  return (
    <Dashboard>
      <CostAnalysisChart data={costData} />
      <UptimeChart data={uptimeData} />
      <InterventionTypeChart data={interventionData} />
    </Dashboard>
  );
};
```

## Versioning Logic (Same Pattern, Different Data)

```typescript
// Generic versioning function - SAME PATTERN
const updateTracking = async (domainTable: string, domainId: string, data: any) => {
  const today = new Date().toISOString().split('T')[0];
  
  const existing = await db.from(`${domainTable}_tracking`)
    .select('*')
    .eq(`${domainTable}_id`, domainId)
    .eq('tracking_date', today)
    .single();
  
  if (existing.data) {
    // Update today's record - SAME LOGIC, DIFFERENT DATA
    return db.from(`${domainTable}_tracking`)
      .update(data)
      .eq(`${domainTable}_id`, domainId)
      .eq('tracking_date', today);
  } else {
    // Create new record - SAME LOGIC, DIFFERENT DATA
    return db.from(`${domainTable}_tracking`)
      .insert({ [`${domainTable}_id`]: domainId, ...data });
  }
};
```

## Key Implementation Points

1. **Same Pattern, Different Content:** All domains follow the 2-table pattern but have completely different fields and logic

2. **Domain-Specific Everything:** Each domain needs its own:
   - Database fields
   - Form components
   - Validation rules
   - Service methods
   - Dashboard KPIs
   - Chart types

3. **No Generic Forms:** Don't try to create one generic form - each domain needs its specific form with its specific fields

4. **Shared Infrastructure Only:** Only share the basic pattern (2 tables, versioning logic) and common tables (clients, projects)

5. **Independent Development:** Each domain can be developed independently without affecting others

## Adding New Domains

When adding a new domain (e.g., "Commercial"):

1. **Create domain-specific tables** with fields relevant to commercial activities
2. **Build domain-specific forms** for commercial data entry
3. **Implement domain-specific services** with commercial business logic
4. **Design domain-specific dashboards** with commercial KPIs
5. **Reuse only:** clients, projects tables and versioning pattern

**Remember:** Each domain is unique in its data, forms, and functions - only the architectural pattern is shared.