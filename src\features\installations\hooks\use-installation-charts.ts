import { useMemo } from 'react';
import { useInstallations } from './use-installations';
import { getInstallationProgress } from '../utils/tracking-utils';

export interface ChartData {
  productTypeDistribution: Array<{
    name: string;
    value: number;
    percentage: number;
    color: string;
  }>;
  clientDistribution: Array<{
    name: string;
    value: number;
    installations: number;
  }>;
  statusEvolution: Array<{
    date: string;
    planning: number;
    in_progress: number;
    completed: number;
    on_hold: number;
  }>;
  progressDistribution: {
    completed: number;
    high: number;
    medium: number;
    low: number;
  };
}

export function useInstallationCharts(installations?: any[]): {
  chartData: ChartData;
  isLoading: boolean;
  error: Error | null;
} {
  const { data: fetchedInstallations, isLoading, error } = useInstallations({}, !!installations);

  const data = installations || fetchedInstallations;

  const chartData = useMemo(() => {
    if (!data) {
      return {
        productTypeDistribution: [],
        clientDistribution: [],
        statusEvolution: [],
        progressDistribution: { completed: 0, high: 0, medium: 0, low: 0 },
      };
    }

    // Distribution par type de produit
    const productTypes = data.reduce((acc, inst) => {
      const type = inst.product_type || 'Non défini';
      acc[type] = (acc[type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const productTypeDistribution = Object.keys(productTypes).map(name => {
      const value = productTypes[name];
      return {
        name: name === 'KYA-SoP' ? 'KYA-SoP' : 'Lampadaire',
        value,
        percentage: Math.round((value / data.length) * 100),
        color: name === 'KYA-SoP' ? '#1ca18c' : '#f99d32',
      };
    });

    // Distribution par client (Top 5)
    const clientTypes = data.reduce((acc, inst) => {
      const type = inst.client?.type || 'Non défini';
      acc[type] = (acc[type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const clientDistribution = Object.keys(clientTypes).map(name => {
      const value = clientTypes[name];
      return {
        name: name === 'INDIVIDUAL' ? 'Ménage' : 'Institution',
        value,
        installations: value,
      };
    });

    // Évolution des statuts - pas de données historiques disponibles pour le moment
    const statusEvolution: Array<{
      date: string;
      planning: number;
      in_progress: number;
      completed: number;
      on_hold: number;
    }> = [];

    // Distribution de progression
    const progressDistribution = data.reduce(
      (acc, inst) => {
        const progress = getInstallationProgress(inst);
        if (inst.status === 'COMPLETED') {
          acc.completed += 1;
        } else if (progress >= 75) {
          acc.high += 1;
        } else if (progress >= 50) {
          acc.medium += 1;
        } else {
          acc.low += 1;
        }
        return acc;
      },
      { completed: 0, high: 0, medium: 0, low: 0 }
    );

    return {
      productTypeDistribution,
      clientDistribution,
      statusEvolution,
      progressDistribution,
    };
  }, [data]);

  return {
    chartData,
    isLoading,
    error,
  };
}
