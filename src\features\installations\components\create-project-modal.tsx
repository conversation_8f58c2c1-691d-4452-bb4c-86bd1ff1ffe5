'use client';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Loader2, Save, FolderOpen, Calendar } from 'lucide-react';

import { useCreateProject } from '@/hooks/use-projects';
import type { Project } from '@/types/shared';

// Form validation schema
const projectSchema = z.object({
  name: z.string().min(1, 'Le nom est requis').max(255, 'Le nom est trop long'),
  description: z.string().optional(),
  start_date: z.string().optional(),
  end_date: z.string().optional(),
  budget: z.string().optional(),
  status: z.enum(['ACTIVE', 'COMPLETED', 'CANCELLED', 'ON_HOLD'], {
    message: 'Le statut est requis',
  }),
});

type ProjectFormData = z.infer<typeof projectSchema>;

interface CreateProjectModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onProjectCreated: (project: Project) => void;
  clientId: string;
}

export function CreateProjectModal({ open, onOpenChange, onProjectCreated, clientId }: CreateProjectModalProps) {
  const createProjectMutation = useCreateProject();

  const form = useForm<ProjectFormData>({
    resolver: zodResolver(projectSchema),
    defaultValues: {
      name: '',
      description: '',
      start_date: '',
      end_date: '',
      budget: '',
      status: 'ACTIVE',
    },
  });

  const onSubmit = async (data: ProjectFormData) => {
    try {
      const projectData = {
        name: data.name,
        client_id: clientId,
        description: data.description || undefined,
        start_date: data.start_date ? new Date(data.start_date) : undefined,
        end_date: data.end_date ? new Date(data.end_date) : undefined,
        budget: data.budget ? parseFloat(data.budget) : undefined,
        status: data.status,
      };

      const newProject = await createProjectMutation.mutateAsync(projectData);
      onProjectCreated(newProject);
      form.reset();
    } catch (error) {
      // Error handling is done in the mutation
      console.error('Error creating project:', error);
    }
  };

  const handleClose = () => {
    if (!createProjectMutation.isPending) {
      form.reset();
      onOpenChange(false);
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      ACTIVE: { color: 'bg-green-100 text-green-700', label: 'Actif' },
      COMPLETED: { color: 'bg-blue-100 text-blue-700', label: 'Terminé' },
      CANCELLED: { color: 'bg-red-100 text-red-700', label: 'Annulé' },
      ON_HOLD: { color: 'bg-yellow-100 text-yellow-700', label: 'En attente' },
    };
    
    const config = variants[status as keyof typeof variants] || variants.ACTIVE;
    
    return (
      <Badge variant="secondary" className={config.color}>
        {config.label}
      </Badge>
    );
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <div className="p-2 bg-gradient-to-r from-green-500 to-emerald-600 rounded-lg">
              <FolderOpen className="h-4 w-4 text-white" />
            </div>
            Créer un Nouveau Projet
          </DialogTitle>
          <DialogDescription>
            Ajoutez les informations du projet pour l'associer à l'installation.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nom du Projet *</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="Ex: Installation Hôpital Dakar"
                        className="bg-white"
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Statut *</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger className="bg-white">
                          <SelectValue placeholder="Sélectionner le statut" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="ACTIVE">
                          <div className="flex items-center gap-2">
                            <span>Actif</span>
                            {getStatusBadge('ACTIVE')}
                          </div>
                        </SelectItem>
                        <SelectItem value="COMPLETED">
                          <div className="flex items-center gap-2">
                            <span>Terminé</span>
                            {getStatusBadge('COMPLETED')}
                          </div>
                        </SelectItem>
                        <SelectItem value="ON_HOLD">
                          <div className="flex items-center gap-2">
                            <span>En attente</span>
                            {getStatusBadge('ON_HOLD')}
                          </div>
                        </SelectItem>
                        <SelectItem value="CANCELLED">
                          <div className="flex items-center gap-2">
                            <span>Annulé</span>
                            {getStatusBadge('CANCELLED')}
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="Description détaillée du projet"
                      className="bg-white min-h-[80px]"
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <FormField
                control={form.control}
                name="start_date"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Date de Début</FormLabel>
                    <FormControl>
                      <Input 
                        type="date"
                        className="bg-white"
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="end_date"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Date de Fin</FormLabel>
                    <FormControl>
                      <Input 
                        type="date"
                        className="bg-white"
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="budget"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Budget (FCFA)</FormLabel>
                    <FormControl>
                      <Input 
                        type="number"
                        placeholder="Ex: 5000000"
                        className="bg-white"
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <DialogFooter>
              <Button 
                type="button" 
                variant="outline" 
                onClick={handleClose}
                disabled={createProjectMutation.isPending}
              >
                Annuler
              </Button>
              <Button 
                type="submit" 
                disabled={createProjectMutation.isPending}
                className="bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700"
              >
                {createProjectMutation.isPending ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Création...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Créer le Projet
                  </>
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
