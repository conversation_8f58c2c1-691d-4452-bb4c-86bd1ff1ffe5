// Utilitaires pour la gestion des données de tracking des installations

/**
 * Extrait les données de tracking les plus récentes d'une installation
 * Gère le cas où latest_tracking peut être null, undefined ou un tableau
 */
export function getLatestTracking(installation: any) {
  if (!installation?.latest_tracking) return null;
  
  // Si c'est un tableau, prendre le premier élément (le plus récent)
  if (Array.isArray(installation.latest_tracking)) {
    return installation.latest_tracking.length > 0 ? installation.latest_tracking[0] : null;
  }
  
  // Si c'est un objet, le retourner directement
  return installation.latest_tracking;
}

/**
 * Obtient la progression globale d'une installation de manière sécurisée
 */
export function getInstallationProgress(installation: any): number {
  const latestTracking = getLatestTracking(installation);
  return latestTracking?.global_progress || 0;
}

/**
 * Obtient la date de dernière mise à jour d'une installation
 */
export function getLastUpdateDate(installation: any): string | null {
  const latestTracking = getLatestTracking(installation);
  return latestTracking?.created_at || latestTracking?.tracking_date || null;
}

/**
 * Vérifie si une installation a des données de tracking pour une date donnée
 */
export function hasTrackingForDate(installation: any, date: string): boolean {
  const latestTracking = getLatestTracking(installation);
  if (!latestTracking?.tracking_date) return false;
  
  const trackingDate = new Date(latestTracking.tracking_date).toISOString().split('T')[0];
  return trackingDate === date;
}

/**
 * Obtient toutes les données de progression spécifiques selon le type de produit
 */
export function getProgressDetails(installation: any) {
  const latestTracking = getLatestTracking(installation);
  if (!latestTracking) return null;

  const common = {
    global_progress: latestTracking.global_progress || 0,
    execution_file_progress: latestTracking.execution_file_progress || 0,
    metalwork_progress: latestTracking.metalwork_progress || 0,
    excavation_progress: latestTracking.excavation_progress || 0,
  };

  if (installation.product_type === 'KYA-SoP') {
    return {
      ...common,
      pv_supports_progress: latestTracking.pv_supports_progress || 0,
      modules_wiring_progress: latestTracking.modules_wiring_progress || 0,
      pv_inverter_cables_progress: latestTracking.pv_inverter_cables_progress || 0,
      inverters_wiring_progress: latestTracking.inverters_wiring_progress || 0,
      batteries_wiring_progress: latestTracking.batteries_wiring_progress || 0,
      ac_dc_boxes_progress: latestTracking.ac_dc_boxes_progress || 0,
      load_separation_progress: latestTracking.load_separation_progress || 0,
      battery_inverter_connection_progress: latestTracking.battery_inverter_connection_progress || 0,
      grounding_progress: latestTracking.grounding_progress || 0,
    };
  } else if (installation.product_type === 'Lampadaire') {
    return {
      ...common,
      pole_installation_progress: latestTracking.pole_installation_progress || 0,
      lamp_installation_progress: latestTracking.lamp_installation_progress || 0,
    };
  }

  return common;
}
