-- Schéma de Base de Données - KYA Dashboards

-- Enable UUID generation
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- 1. Structure Organisationnelle

-- Table `persons` (créée en premier pour résoudre la dépendance)
CREATE TABLE persons (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    first_name VARCHAR(100) NOT NULL,
    last_name VA<PERSON><PERSON><PERSON>(100) NOT NULL,
    email VARCHAR(255) UNIQUE,
    phone VARCHAR(20),
    employee_id VARCHAR(50) UNIQUE, -- Matricule employé
    position VARCHAR(100), -- Poste occupé
    hire_date DATE,
    is_active BOOLEAN DEFAULT true,
    metadata JSONB DEFAULT '{}', -- Informations RH supplémentaires
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table `entities`
CREATE TABLE entities (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    type VARCHAR(50) NOT NULL CHECK (type IN ('DIRECTION', 'EQUIPE', 'SOUS_EQUIPE', 'DEPARTEMENT')),
    parent_id UUID REFERENCES entities(id) ON DELETE CASCADE,
    manager_id UUID REFERENCES persons(id) ON DELETE SET NULL,
    description TEXT,
    code VARCHAR(50) UNIQUE, -- Code métier pour identification
    is_active BOOLEAN DEFAULT true,
    metadata JSONB DEFAULT '{}', -- Données spécifiques par type
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table `entities_persons`
CREATE TABLE entities_persons (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    entity_id UUID NOT NULL REFERENCES entities(id) ON DELETE CASCADE,
    person_id UUID NOT NULL REFERENCES persons(id) ON DELETE CASCADE,
    role_in_entity VARCHAR(100), -- Rôle spécifique dans cette entité
    start_date DATE DEFAULT CURRENT_DATE,
    end_date DATE,
    is_primary BOOLEAN DEFAULT false, -- Entité principale de la personne
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(entity_id, person_id, start_date)
);

-- 2. Authentification et Profils Utilisateurs

-- Table `user_profiles` (créée en premier pour résoudre la dépendance)
CREATE TABLE user_profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    auth_user_id UUID REFERENCES public.auth_users(id) ON DELETE CASCADE,
    person_id UUID REFERENCES public.persons(id) ON DELETE SET NULL,
    display_name VARCHAR(255),
    avatar_url TEXT,
    bio TEXT,
    preferences JSONB DEFAULT '{}', -- Thème, langue, timezone, etc.
    notification_settings JSONB DEFAULT '{}', -- Préférences de notifications
    dashboard_config JSONB DEFAULT '{}', -- Configuration des dashboards
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CONSTRAINT user_profiles_auth_user_id_key UNIQUE (auth_user_id),
    CONSTRAINT user_profiles_person_id_key UNIQUE (person_id)
);

-- Table `auth_users`
CREATE TABLE auth_users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    supabase_id UUID UNIQUE NOT NULL, -- Référence vers auth.users de Supabase
    email VARCHAR(255) UNIQUE NOT NULL,
    is_active BOOLEAN DEFAULT true,
    last_login_at TIMESTAMP WITH TIME ZONE,
    login_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Système RBAC (Role-Based Access Control)

-- Table `roles`
CREATE TABLE roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) UNIQUE NOT NULL,
    display_name VARCHAR(255) NOT NULL,
    description TEXT,
    level INTEGER NOT NULL DEFAULT 0, -- Niveau hiérarchique du rôle
    is_system_role BOOLEAN DEFAULT false, -- Rôle système non modifiable
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table `permissions`
CREATE TABLE permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) UNIQUE NOT NULL,
    display_name VARCHAR(255) NOT NULL,
    description TEXT,
    resource VARCHAR(100) NOT NULL, -- Ressource concernée (users, entities, data, etc.)
    action VARCHAR(50) NOT NULL, -- Action (create, read, update, delete, manage)
    is_system_permission BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table `roles_permissions`
CREATE TABLE roles_permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    role_id UUID NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
    permission_id UUID NOT NULL REFERENCES permissions(id) ON DELETE CASCADE,
    granted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    granted_by UUID, -- Référence ajoutée plus tard pour éviter une dépendance circulaire
    UNIQUE(role_id, permission_id)
);

-- Table `user_roles`
CREATE TABLE user_roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth_users(id) ON DELETE CASCADE,
    role_id UUID NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
    entity_id UUID REFERENCES entities(id) ON DELETE CASCADE, -- Rôle limité à cette entité
    granted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    granted_by UUID, -- Référence ajoutée plus tard pour éviter une dépendance circulaire
    expires_at TIMESTAMP WITH TIME ZONE, -- Rôle temporaire
    is_active BOOLEAN DEFAULT true,
    UNIQUE(user_id, role_id, entity_id)
);

-- 4. Audit et Traçabilité

-- Table `audit_logs`
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth_users(id),
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(100) NOT NULL,
    resource_id UUID,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Index et Contraintes

-- Index pour les performances

-- Index sur les hiérarchies
CREATE INDEX idx_entities_parent_id ON entities(parent_id);
CREATE INDEX idx_entities_type ON entities(type);
CREATE INDEX idx_entities_manager_id ON entities(manager_id);

-- Index sur les relations personnes-entités
CREATE INDEX idx_entities_persons_entity_id ON entities_persons(entity_id);
CREATE INDEX idx_entities_persons_person_id ON entities_persons(person_id);
CREATE INDEX idx_entities_persons_active ON entities_persons(entity_id, person_id) WHERE end_date IS NULL;

-- Index sur l'authentification
CREATE INDEX idx_auth_users_supabase_id ON auth_users(supabase_id);
CREATE INDEX idx_auth_users_person_id ON auth_users(person_id);
CREATE INDEX idx_auth_users_email ON auth_users(email);

-- Index sur les rôles et permissions
CREATE INDEX idx_user_roles_user_id ON user_roles(user_id);
CREATE INDEX idx_user_roles_entity_id ON user_roles(entity_id);
CREATE INDEX idx_user_roles_active ON user_roles(user_id, entity_id) WHERE is_active = true;

-- Index sur l'audit
CREATE INDEX idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_resource ON audit_logs(resource_type, resource_id);
CREATE INDEX idx_audit_logs_created_at ON audit_logs(created_at);

-- Ajout des contraintes de clé étrangère reportées (après création de toutes les tables)
ALTER TABLE roles_permissions 
    ADD CONSTRAINT fk_roles_permissions_granted_by 
    FOREIGN KEY (granted_by) REFERENCES auth_users(id);
    
ALTER TABLE user_roles 
    ADD CONSTRAINT fk_user_roles_granted_by 
    FOREIGN KEY (granted_by) REFERENCES auth_users(id);

-- Contraintes de validation

-- Validation des emails
ALTER TABLE persons ADD CONSTRAINT check_email_format 
    CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$');

ALTER TABLE auth_users ADD CONSTRAINT check_email_format 
    CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$');

-- Validation des dates
ALTER TABLE entities_persons ADD CONSTRAINT check_date_range 
    CHECK (end_date IS NULL OR end_date >= start_date);

ALTER TABLE user_roles ADD CONSTRAINT check_expiry_date 
    CHECK (expires_at IS NULL OR expires_at > granted_at);

-- Fonctions et Triggers

-- Fonction de mise à jour automatique des timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers pour les timestamps
CREATE TRIGGER update_entities_updated_at BEFORE UPDATE ON entities
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_persons_updated_at BEFORE UPDATE ON persons
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_auth_users_updated_at BEFORE UPDATE ON auth_users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_profiles_updated_at BEFORE UPDATE ON user_profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Fonction pour vérifier les permissions
CREATE OR REPLACE FUNCTION check_user_permission(
    p_user_id UUID,
    p_permission_name VARCHAR,
    p_entity_id UUID DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
    has_permission BOOLEAN := FALSE;
BEGIN
    SELECT EXISTS(
        SELECT 1
        FROM user_roles ur
        JOIN roles_permissions rp ON ur.role_id = rp.role_id
        JOIN permissions p ON rp.permission_id = p.id
        WHERE ur.user_id = p_user_id
        AND p.name = p_permission_name
        AND ur.is_active = true
        AND (p_entity_id IS NULL OR ur.entity_id = p_entity_id OR ur.entity_id IS NULL)
        AND (ur.expires_at IS NULL OR ur.expires_at > NOW())
    ) INTO has_permission;
    
    RETURN has_permission;
END;
$$ LANGUAGE plpgsql;

-- Function to get all permission names for a user
CREATE OR REPLACE FUNCTION get_user_permissions(p_user_id UUID)
RETURNS TABLE(permission_name TEXT) AS $$
BEGIN
    RETURN QUERY
    SELECT DISTINCT p.name
    FROM permissions p
    JOIN roles_permissions rp ON p.id = rp.permission_id
    JOIN user_roles ur ON rp.role_id = ur.role_id
    WHERE ur.user_id = p_user_id AND ur.is_active = true AND (ur.expires_at IS NULL OR ur.expires_at > NOW());
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
