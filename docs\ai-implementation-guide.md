# AI Implementation Guide - KYA Business Domains

## Context for AI Developer

You are implementing a new business domain for KYA Dashboards. This system manages different business activities (installations, maintenance, commercial, projects) with a consistent architectural pattern but domain-specific implementations.

## Architecture Overview

```mermaid
graph TB
    subgraph "Shared Tables"
        C[clients]
        P[projects]
    end
    
    subgraph "Domain: Installations"
        I1[installations<br/>main table]
        I2[installations_tracking<br/>daily updates]
        I1 --> I2
        C --> I1
        P --> I1
    end
    
    subgraph "Domain: Maintenance"
        M1[maintenance<br/>main table]
        M2[maintenance_tracking<br/>daily updates]
        M1 --> M2
        C --> M1
        P --> M1
        I1 --> M1
    end
    
    subgraph "Domain: Commercial"
        CO1[commercial<br/>main table]
        CO2[commercial_tracking<br/>daily updates]
        CO1 --> CO2
        C --> CO1
        P --> CO1
    end
```

## Data Architecture Pattern

### Every Domain Has 2 Tables

```sql
-- Pattern for ANY domain
CREATE TABLE [domain_name] (
    id UUID PRIMARY KEY,
    name TEXT NOT NULL,
    client_id UUID REFERENCES clients(id),
    project_id UUID REFERENCES projects(id),
    product_type TEXT CHECK (product_type IN ('KYA-SoP', 'Lampadaire')),
    
    -- DOMAIN-SPECIFIC FIELDS HERE
    -- These vary completely per domain
    
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE [domain_name]_tracking (
    id UUID PRIMARY KEY,
    [domain_name]_id UUID REFERENCES [domain_name](id),
    tracking_date DATE DEFAULT CURRENT_DATE,
    
    -- DOMAIN-SPECIFIC TRACKING FIELDS HERE
    -- These are completely different per domain
    
    daily_comments TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    created_by UUID REFERENCES auth_users(id),
    
    UNIQUE([domain_name]_id, tracking_date)
);
```

### Versioning Logic (Same for All Domains)

```typescript
const updateTracking = async (domainTable: string, domainId: string, data: any) => {
  const today = new Date().toISOString().split('T')[0];
  
  const existing = await supabase
    .from(`${domainTable}_tracking`)
    .select('*')
    .eq(`${domainTable}_id`, domainId)
    .eq('tracking_date', today)
    .single();
  
  if (existing.data) {
    // Same day = UPDATE
    return supabase
      .from(`${domainTable}_tracking`)
      .update(data)
      .eq(`${domainTable}_id`, domainId)
      .eq('tracking_date', today);
  } else {
    // Different day = INSERT
    return supabase
      .from(`${domainTable}_tracking`)
      .insert({ [`${domainTable}_id`]: domainId, ...data });
  }
};
```

## Interface Architecture

### Dashboard Structure (3 Main Tabs)

```mermaid
graph LR
    subgraph "Domain Dashboard"
        T1[Overview Tab<br/>KPIs & Statistics]
        T2[Daily Tracking Tab<br/>Progress Forms]
        T3[History Tab<br/>Past Records & Analysis]
    end
    
    T1 --> T2
    T2 --> T3
```

### Tab-Specific Data Requirements

#### Tab 1: Overview (KPIs & Statistics)
**Data Sources:**
- Main domain table for basic info
- Latest tracking records for current status
- Aggregated statistics via SQL functions

**Key Metrics to Display:**
```sql
-- From get_installation_stats() function
- total_installations: Total count
- active_installations: Currently in progress
- completed_installations: Finished items
- average_progress: Overall completion percentage
- overdue_installations: Past due date
- this_month_completed: Recent completions
```

**Visual Components:**
- KPI cards with numbers and trends
- Progress charts (bar, pie, line)
- Status distribution graphs
- Timeline/calendar view of deadlines

#### Tab 2: Daily Tracking (Progress Forms)
**Data Sources:**
- Domain tracking table for daily updates
- Product-specific progress fields
- Current day's tracking record

**Form Fields by Product Type:**

**Common Fields (All Products):**
```sql
- execution_file_progress (0-100%)
- global_progress (0-100%)
- daily_comments (TEXT)
- issues_encountered (TEXT)
- next_actions (TEXT)
- hours_worked (DECIMAL)
- weather_conditions (VARCHAR)
```

**KYA-SoP Specific Fields:**
```sql
- metalwork_progress (0-100%)
- excavation_progress (0-100%)
- pv_supports_progress (0-100%)
- modules_wiring_progress (0-100%)
- pv_inverter_cables_progress (0-100%)
- inverters_wiring_progress (0-100%)
- batteries_wiring_progress (0-100%)
- ac_dc_boxes_progress (0-100%)
- load_separation_progress (0-100%)
- battery_inverter_connection_progress (0-100%)
- grounding_progress (0-100%)
```

**Lampadaire Specific Fields:**
```sql
- pole_installation_progress (0-100%)
- lamp_installation_progress (0-100%)
```

**Testing & Commissioning:**
```sql
- commissioning_test_status (ENUM)
- commissioning_test_notes (TEXT)
- commissioning_test_date (DATE)
```

#### Tab 3: History (Past Records & Analysis)
**Data Sources:**
- All tracking records ordered by date
- Main table for context information
- Calculated progress trends

**Display Components:**
- Timeline view of all tracking entries
- Progress evolution charts
- Comparison between planned vs actual dates
- Historical comments and issues log
- Team performance analytics

### User Flow (Same Pattern, Different Content)

```mermaid
graph TD
    A[User Opens Domain Item] --> B[Item Detail View]
    B --> C{User Action}
    
    C -->|Update Progress| D[Daily Tracking Form]
    C -->|View History| E[Tracking History]
    C -->|Edit Details| F[Main Data Form]
    
    D --> G{Same Day?}
    G -->|Yes| H[Update Today's Record]
    G -->|No| I[Create New Record]
    
    E --> J[List All Tracking Records]
    J --> K[Select Date to Edit]
    
    F --> L[Update Main Data]
```

### Component Structure (3-Tab Architecture)

```
src/features/[domain]/
├── components/
│   ├── [domain]-dashboard.tsx      # Main dashboard with 3 tabs
│   ├── [domain]-overview.tsx       # Tab 1: KPIs & Statistics
│   ├── [domain]-tracking.tsx       # Tab 2: Daily tracking forms
│   ├── [domain]-history.tsx        # Tab 3: Historical records
│   ├── [domain]-detail.tsx         # Item detail view
│   ├── [domain]-form.tsx           # Main data form
│   ├── [domain]-tracking-form.tsx  # Daily progress form
│   ├── [domain]-stats.tsx          # Statistics components
│   └── [domain]-charts.tsx         # Chart components
├── services/
│   └── [domain]-service.ts         # Business logic & API calls
├── types/
│   └── index.ts                    # TypeScript interfaces
├── hooks/
│   ├── use-[domain].ts             # Main data hooks
│   ├── use-[domain]-stats.ts       # Statistics hooks
│   └── use-[domain]-tracking.ts    # Tracking hooks
└── utils/
    └── [domain]-calculations.ts    # Progress calculations
```

## Implementation Requirements

### 1. Database Tables

Create 2 tables following the pattern:
- Main table with domain-specific stable fields
- Tracking table with domain-specific daily update fields
- Reference shared tables: `clients`, `projects`
- Include `product_type` enum field

### 2. TypeScript Types (Based on Installations Example)

```typescript
// Main entity type (installations example)
interface Installation {
  id: string;
  name: string;
  client_id: string;
  project_id?: string;
  product_type: 'KYA-SoP' | 'Lampadaire';
  
  // Installation specific fields
  installation_number: string;
  site_location: string;
  gps_coordinates?: string;
  
  // Equipment specifications (conditional)
  peak_power?: string; // KYA-SoP only
  inverter_specs?: string; // KYA-SoP only
  battery_capacity?: string; // KYA-SoP only
  equipment_description?: string;
  
  // Team composition
  team_leader_id?: string;
  team_members: string[];
  
  // Planning
  planned_start_date?: Date;
  planned_end_date?: Date;
  actual_start_date?: Date;
  actual_end_date?: Date;
  total_duration_days?: number;
  
  // Reception and delivery
  technical_reception_date?: Date;
  technical_reception_status?: 'PENDING' | 'APPROVED_WITH_RESERVES' | 'APPROVED_WITHOUT_RESERVES' | 'REJECTED';
  technical_reception_notes?: string;
  provisional_reception_date?: Date;
  provisional_reception_status?: 'PENDING' | 'APPROVED_WITH_RESERVES' | 'APPROVED_WITHOUT_RESERVES' | 'REJECTED';
  provisional_reception_notes?: string;
  general_functioning_state?: 'EXCELLENT' | 'GOOD' | 'FAIR' | 'POOR' | 'NOT_FUNCTIONAL';
  
  // Status
  status: 'PLANNING' | 'IN_PROGRESS' | 'TESTING' | 'COMPLETED' | 'ON_HOLD' | 'CANCELLED';
  comments?: string;
  
  created_at: Date;
  updated_at: Date;
  created_by: string;
  updated_by?: string;
}

// Tracking type with product-specific fields
interface InstallationTracking {
  id: string;
  installation_id: string;
  tracking_date: Date;
  
  // Common progress fields
  execution_file_progress: number; // 0-100
  global_progress: number; // 0-100
  
  // KYA-SoP specific progress fields (nullable for Lampadaire)
  metalwork_progress?: number;
  excavation_progress?: number;
  pv_supports_progress?: number;
  modules_wiring_progress?: number;
  pv_inverter_cables_progress?: number;
  inverters_wiring_progress?: number;
  batteries_wiring_progress?: number;
  ac_dc_boxes_progress?: number;
  load_separation_progress?: number;
  battery_inverter_connection_progress?: number;
  grounding_progress?: number;
  
  // Lampadaire specific progress fields (nullable for KYA-SoP)
  pole_installation_progress?: number;
  lamp_installation_progress?: number;
  
  // Testing and commissioning
  commissioning_test_status?: 'NOT_STARTED' | 'IN_PROGRESS' | 'PASSED_WITH_RESERVES' | 'PASSED_WITHOUT_RESERVES' | 'FAILED';
  commissioning_test_notes?: string;
  commissioning_test_date?: Date;
  
  // Daily tracking fields
  daily_comments?: string;
  issues_encountered?: string;
  next_actions?: string;
  hours_worked?: number;
  weather_conditions?: string;
  
  created_at: Date;
  updated_at: Date;
  created_by: string;
  updated_by?: string;
}

// Statistics type for Overview tab
interface InstallationStats {
  total_installations: number;
  active_installations: number;
  completed_installations: number;
  average_progress: number;
  overdue_installations: number;
  this_month_completed: number;
}
```

### 3. Service Implementation

```typescript
export class [Domain]Service {
  // Main data operations
  static async create[Domain](data: Create[Domain]Data) {
    return supabase.from('[domain]').insert(data);
  }
  
  static async update[Domain](id: string, data: Update[Domain]Data) {
    return supabase.from('[domain]').update(data).eq('id', id);
  }
  
  // Tracking operations (use versioning logic)
  static async updateDailyTracking(id: string, data: [Domain]TrackingData) {
    return updateTracking('[domain]', id, data);
  }
  
  static async getTrackingHistory(id: string) {
    return supabase
      .from('[domain]_tracking')
      .select('*')
      .eq('[domain]_id', id)
      .order('tracking_date', { ascending: false });
  }
  
  // Domain-specific calculations
  static async calculate[Domain]KPIs(teamId: string) {
    // Implement domain-specific KPI calculations
    // This will be completely different per domain
  }
}
```

### 4. React Components

#### Dashboard Component (3-Tab Structure)
```typescript
const InstallationDashboard = () => {
  const { data: stats } = useInstallationStats();
  const { data: installations } = useInstallationList();
  
  return (
    <Tabs defaultValue="overview">
      <TabsList>
        <TabsTrigger value="overview">Overview</TabsTrigger>
        <TabsTrigger value="tracking">Daily Tracking</TabsTrigger>
        <TabsTrigger value="history">History</TabsTrigger>
      </TabsList>
      
      <TabsContent value="overview">
        <InstallationOverview stats={stats} installations={installations} />
      </TabsContent>
      
      <TabsContent value="tracking">
        <InstallationTracking installations={installations} />
      </TabsContent>
      
      <TabsContent value="history">
        <InstallationHistory installations={installations} />
      </TabsContent>
    </Tabs>
  );
};
```

#### Overview Tab Component
```typescript
const InstallationOverview = ({ stats, installations }: {
  stats: InstallationStats;
  installations: Installation[];
}) => {
  return (
    <div className="space-y-6">
      {/* KPI Cards */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
        <KPICard title="Total" value={stats.total_installations} />
        <KPICard title="Active" value={stats.active_installations} />
        <KPICard title="Completed" value={stats.completed_installations} />
        <KPICard title="Avg Progress" value={`${stats.average_progress}%`} />
        <KPICard title="Overdue" value={stats.overdue_installations} variant="warning" />
        <KPICard title="This Month" value={stats.this_month_completed} variant="success" />
      </div>
      
      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <ProgressChart installations={installations} />
        <StatusDistributionChart installations={installations} />
        <TimelineChart installations={installations} />
        <ProductTypeChart installations={installations} />
      </div>
      
      {/* Recent Activity */}
      <RecentInstallationsTable installations={installations.slice(0, 10)} />
    </div>
  );
};
```

#### Tracking Form Component (Product-Specific Fields)
```typescript
const InstallationTrackingForm = ({ installationId }: { installationId: string }) => {
  const { data: installation } = useInstallation(installationId);
  const { data: todayTracking } = useInstallationTodayTracking(installationId);
  const updateMutation = useInstallationTrackingMutation();
  
  const form = useForm<InstallationTrackingData>({
    defaultValues: todayTracking || {}
  });
  
  const onSubmit = (data: InstallationTrackingData) => {
    updateMutation.mutate({ installationId, data });
  };
  
  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {/* Common Progress Fields */}
        <div className="grid grid-cols-2 gap-4">
          <FormField name="execution_file_progress" label="Execution File Progress (%)" type="number" min={0} max={100} />
          <FormField name="global_progress" label="Global Progress (%)" type="number" min={0} max={100} />
        </div>
        
        {/* Product-Specific Progress Fields */}
        {installation?.product_type === 'KYA-SoP' && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">KYA-SoP Progress</h3>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              <FormField name="metalwork_progress" label="Metalwork (%)" type="number" min={0} max={100} />
              <FormField name="excavation_progress" label="Excavation (%)" type="number" min={0} max={100} />
              <FormField name="pv_supports_progress" label="PV Supports (%)" type="number" min={0} max={100} />
              <FormField name="modules_wiring_progress" label="Modules Wiring (%)" type="number" min={0} max={100} />
              <FormField name="pv_inverter_cables_progress" label="PV-Inverter Cables (%)" type="number" min={0} max={100} />
              <FormField name="inverters_wiring_progress" label="Inverters Wiring (%)" type="number" min={0} max={100} />
              <FormField name="batteries_wiring_progress" label="Batteries Wiring (%)" type="number" min={0} max={100} />
              <FormField name="ac_dc_boxes_progress" label="AC/DC Boxes (%)" type="number" min={0} max={100} />
              <FormField name="load_separation_progress" label="Load Separation (%)" type="number" min={0} max={100} />
              <FormField name="battery_inverter_connection_progress" label="Battery-Inverter Connection (%)" type="number" min={0} max={100} />
              <FormField name="grounding_progress" label="Grounding (%)" type="number" min={0} max={100} />
            </div>
          </div>
        )}
        
        {installation?.product_type === 'Lampadaire' && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Lampadaire Progress</h3>
            <div className="grid grid-cols-2 gap-4">
              <FormField name="pole_installation_progress" label="Pole Installation (%)" type="number" min={0} max={100} />
              <FormField name="lamp_installation_progress" label="Lamp Installation (%)" type="number" min={0} max={100} />
            </div>
          </div>
        )}
        
        {/* Testing & Commissioning */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Testing & Commissioning</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField name="commissioning_test_status" label="Test Status" type="select" 
              options={[
                { value: 'NOT_STARTED', label: 'Not Started' },
                { value: 'IN_PROGRESS', label: 'In Progress' },
                { value: 'PASSED_WITH_RESERVES', label: 'Passed with Reserves' },
                { value: 'PASSED_WITHOUT_RESERVES', label: 'Passed without Reserves' },
                { value: 'FAILED', label: 'Failed' }
              ]} 
            />
            <FormField name="commissioning_test_date" label="Test Date" type="date" />
          </div>
          <FormField name="commissioning_test_notes" label="Test Notes" type="textarea" />
        </div>
        
        {/* Daily Tracking */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Daily Tracking</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField name="hours_worked" label="Hours Worked" type="number" step={0.5} />
            <FormField name="weather_conditions" label="Weather Conditions" />
          </div>
          <FormField name="daily_comments" label="Daily Comments" type="textarea" />
          <FormField name="issues_encountered" label="Issues Encountered" type="textarea" />
          <FormField name="next_actions" label="Next Actions" type="textarea" />
        </div>
        
        <Button type="submit" className="w-full">Update Progress</Button>
      </form>
    </Form>
  );
};
```

## Navigation Integration

### Add to Primary Sidebar
```typescript
// In src/features/dashboard/components/primary-sidebar.tsx
const navItems = [
  // ... existing items
  {
    href: '/[domain]',
    label: '[Domain] Management',
    icon: [DomainIcon],
    permission: '[domain].access',
  },
];
```

### Add Routes
```typescript
// In src/app/(dashboard)/[domain]/page.tsx
export default function [Domain]Page() {
  return <[Domain]Dashboard />;
}
```

## Key Implementation Notes

1. **Domain-Specific Everything**: Fields, forms, validations, KPIs are unique per domain
2. **Reuse Pattern Only**: Only architectural pattern and shared tables are common
3. **No Generic Components**: Each domain needs its own specialized components
4. **Independent Development**: Domains don't affect each other
5. **Consistent UX**: Same 3-tab structure and user flow across domains
6. **Product-Type Conditional Fields**: Forms adapt based on KYA-SoP vs Lampadaire
7. **Daily Versioning**: Same day = update, different day = new record

---

# AI Implementation Prompt

## Your Task

You are implementing the **[DOMAIN_NAME]** business domain for KYA Dashboards.

### Context
- KYA manages different business activities with a consistent pattern
- Each domain has 2 tables: main data + daily tracking
- Shared tables: clients, projects
- Product types: 'KYA-SoP', 'Lampadaire'
- Same day updates = UPDATE, different day = INSERT new record
- 3-tab dashboard structure: Overview, Daily Tracking, History

### What You Need to Implement

1. **Database Schema**
   - Create `[domain]` table with domain-specific fields
   - Create `[domain]_tracking` table with daily update fields
   - Reference `clients` and `projects` tables
   - Include `product_type` field
   - Add SQL functions for statistics calculation

2. **TypeScript Types**
   - Main entity interface with all domain fields
   - Tracking interface with product-specific progress fields
   - Statistics interface for Overview tab
   - Form data interfaces

3. **Service Layer**
   - CRUD operations for main data
   - Daily tracking with versioning logic
   - Domain-specific KPI calculations
   - Statistics aggregation functions

4. **React Components**
   - Dashboard with 3 tabs (Overview, Daily Tracking, History)
   - Overview tab with KPI cards and charts
   - Daily tracking tab with product-specific forms
   - History tab with timeline and analytics
   - Detail views and forms

5. **Navigation**
   - Add to sidebar navigation
   - Create route pages

### Domain-Specific Requirements
[PROVIDE SPECIFIC REQUIREMENTS FOR THE DOMAIN HERE]

### Expected Deliverables
- Database migration files with complete schema
- TypeScript type definitions
- Service implementation with business logic
- React components for all 3 tabs
- Route configuration
- Tests (optional)

### Architecture Reference
Follow the pattern in [`docs/installations-domain-complete.sql`](docs/installations-domain-complete.sql) for database schema and [`docs/dashboard-data-entry-concept.md`](docs/dashboard-data-entry-concept.md) for conceptual understanding.

Remember: Each domain is unique in its data and functionality - only the architectural pattern and 3-tab structure are shared.