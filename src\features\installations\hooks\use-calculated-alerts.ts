import { useMemo } from 'react';
import { useInstallations } from './use-installations';
import type { Installation } from '@/types/installation';

export interface CalculatedAlert {
  id: string;
  type: 'overdue' | 'stagnant' | 'low_progress';
  severity: 'high' | 'medium' | 'low';
  title: string;
  description: string;
  installation: Installation;
  date: string | null;
  daysCount?: number;
}

export interface AlertKpis {
  overdue_count: number;
  stagnant_count: number;
  low_progress_count: number;
  total_alerts: number;
}

export interface AlertAnalytics {
  evolutionData: Array<{
    date: string;
    overdue: number;
    stagnant: number;
    low_progress: number;
  }>;
  typeDistribution: Array<{
    name: string;
    value: number;
    color: string;
  }>;
}

export function useCalculatedAlerts(
  installations?: Installation[]
): {
  alerts: CalculatedAlert[];
  kpis: AlertKpis;
  analytics: AlertAnalytics;
  isLoading: boolean;
  error: Error | null;
} {
  const { data: fetchedInstallations, isLoading: fetchLoading, error } = useInstallations({}, !!installations);

  const data = installations || fetchedInstallations;

  const { alerts, kpis, analytics } = useMemo(() => {
    if (!data) {
      return {
        alerts: [],
        kpis: { overdue_count: 0, stagnant_count: 0, low_progress_count: 0, total_alerts: 0 },
        analytics: { evolutionData: [], typeDistribution: [] },
      };
    }

    const now = new Date();

    // Installations en retard
    const overdueInstallations = data.filter(inst => {
      if (inst.status === 'COMPLETED' || !inst.planned_end_date) return false;
      const plannedEnd = new Date(inst.planned_end_date);
      return now > plannedEnd;
    });

    // Installations sans mise à jour (>7 jours)
    const stagnantInstallations = data.filter(inst => {
      if (!inst.latest_tracking?.created_at || inst.status === 'COMPLETED') return false;
      const lastUpdate = new Date(inst.latest_tracking.created_at);
      const daysSinceUpdate = Math.floor((now.getTime() - lastUpdate.getTime()) / (1000 * 60 * 60 * 24));
      return daysSinceUpdate > 7;
    });

    // Installations avec progression faible (<25%)
    const lowProgressInstallations = data.filter(inst => {
      if (inst.status === 'COMPLETED') return false;
      const progress = inst.latest_tracking?.global_progress || 0;
      return progress < 25 && inst.status === 'IN_PROGRESS';
    });

    // Création des alertes
    const alerts: CalculatedAlert[] = [
      ...overdueInstallations.map(inst => {
        const plannedEnd = new Date(inst.planned_end_date!);
        const daysOverdue = Math.ceil((now.getTime() - plannedEnd.getTime()) / (1000 * 60 * 60 * 24));
        return {
          id: `overdue-${inst.id}`,
          type: 'overdue' as const,
          severity: 'high' as const,
          title: 'Installation en retard',
          description: `${inst.name} dépasse la date prévue de ${daysOverdue} jour(s)`,
          installation: inst,
          date: inst.planned_end_date ? new Date(inst.planned_end_date).toISOString() : null,
          daysCount: daysOverdue,
        };
      }),
      ...stagnantInstallations.map(inst => {
        const lastUpdate = new Date(inst.latest_tracking!.created_at);
        const daysSinceUpdate = Math.floor((now.getTime() - lastUpdate.getTime()) / (1000 * 60 * 60 * 24));
        return {
          id: `stagnant-${inst.id}`,
          type: 'stagnant' as const,
          severity: 'medium' as const,
          title: 'Pas de mise à jour récente',
          description: `${inst.name} n'a pas été mise à jour depuis ${daysSinceUpdate} jours`,
          installation: inst,
          date: new Date(inst.latest_tracking!.created_at).toISOString(),
          daysCount: daysSinceUpdate,
        };
      }),
      ...lowProgressInstallations.map(inst => {
        const progress = Math.round(inst.latest_tracking?.global_progress || 0);
        return {
          id: `low-progress-${inst.id}`,
          type: 'low_progress' as const,
          severity: 'low' as const,
          title: 'Progression faible',
          description: `${inst.name} a une progression de seulement ${progress}%`,
          installation: inst,
          date: inst.latest_tracking?.created_at ? new Date(inst.latest_tracking.created_at).toISOString() : null,
        };
      }),
    ];

    // Tri par sévérité puis par date
    alerts.sort((a, b) => {
      const severityOrder = { high: 3, medium: 2, low: 1 };
      const severityDiff = severityOrder[b.severity] - severityOrder[a.severity];
      if (severityDiff !== 0) return severityDiff;
      
      if (a.date && b.date) {
        return new Date(b.date).getTime() - new Date(a.date).getTime();
      }
      return 0;
    });

    // KPIs
    const kpis: AlertKpis = {
      overdue_count: overdueInstallations.length,
      stagnant_count: stagnantInstallations.length,
      low_progress_count: lowProgressInstallations.length,
      total_alerts: alerts.length,
    };

    // Analytics - Évolution sur 30 jours (pas de données historiques disponibles)
    const evolutionData: Array<{
      date: string;
      overdue: number;
      stagnant: number;
      low_progress: number;
    }> = [];

    // Distribution par type
    const typeDistribution = [
      {
        name: 'En retard',
        value: kpis.overdue_count,
        color: '#ef4444',
      },
      {
        name: 'Sans mise à jour',
        value: kpis.stagnant_count,
        color: '#f97316',
      },
      {
        name: 'Progression faible',
        value: kpis.low_progress_count,
        color: '#eab308',
      },
    ].filter(item => item.value > 0);

    const analytics: AlertAnalytics = {
      evolutionData,
      typeDistribution,
    };

    return { alerts, kpis, analytics };
  }, [data]);

  return {
    alerts,
    kpis,
    analytics,
    isLoading: fetchLoading,
    error,
  };
}
