// Project Hooks - KYA Dashboards
// Shared hooks for project management across all domains

'use client';

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { ProjectService } from '@/services/project-service';
import type { Project, CreateProjectData, UpdateProjectData } from '@/types/shared';

// Query Keys
export const projectKeys = {
  all: ['projects'] as const,
  lists: () => [...projectKeys.all, 'list'] as const,
  list: (filters: Record<string, any>) => [...projectKeys.lists(), { filters }] as const,
  details: () => [...projectKeys.all, 'detail'] as const,
  detail: (id: string) => [...projectKeys.details(), id] as const,
  byClient: (clientId: string) => [...projectKeys.all, 'byClient', clientId] as const,
  search: (query: string, clientId?: string) => [...projectKeys.all, 'search', { query, clientId }] as const,
};

// Get all projects
export function useProjects() {
  return useQuery({
    queryKey: projectKeys.lists(),
    queryFn: ProjectService.getAllProjects,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Get projects by client
export function useProjectsByClient(clientId: string) {
  return useQuery({
    queryKey: projectKeys.byClient(clientId),
    queryFn: () => ProjectService.getProjectsByClient(clientId),
    enabled: !!clientId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Get project by ID
export function useProject(id: string) {
  return useQuery({
    queryKey: projectKeys.detail(id),
    queryFn: () => ProjectService.getProjectById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Search projects
export function useSearchProjects(query: string, clientId?: string) {
  return useQuery({
    queryKey: projectKeys.search(query, clientId),
    queryFn: () => ProjectService.searchProjects(query, clientId),
    enabled: query.length >= 2,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

// Create project mutation
export function useCreateProject() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (data: CreateProjectData) => ProjectService.createProject(data),
    onSuccess: (newProject) => {
      // Invalidate and refetch projects list
      queryClient.invalidateQueries({ queryKey: projectKeys.lists() });
      
      // Invalidate projects by client
      if (newProject.client_id) {
        queryClient.invalidateQueries({ queryKey: projectKeys.byClient(newProject.client_id) });
      }
      
      // Add the new project to the cache
      queryClient.setQueryData(projectKeys.detail(newProject.id), newProject);
      
      toast.success('Projet créé avec succès');
    },
    onError: (error) => {
      console.error('Error creating project:', error);
      toast.error('Erreur lors de la création du projet');
    },
  });
}

// Update project mutation
export function useUpdateProject() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateProjectData }) => 
      ProjectService.updateProject(id, data),
    onSuccess: (updatedProject) => {
      // Invalidate and refetch projects list
      queryClient.invalidateQueries({ queryKey: projectKeys.lists() });
      
      // Invalidate projects by client
      if (updatedProject.client_id) {
        queryClient.invalidateQueries({ queryKey: projectKeys.byClient(updatedProject.client_id) });
      }
      
      // Update the project in the cache
      queryClient.setQueryData(projectKeys.detail(updatedProject.id), updatedProject);
      
      toast.success('Projet mis à jour avec succès');
    },
    onError: (error) => {
      console.error('Error updating project:', error);
      toast.error('Erreur lors de la mise à jour du projet');
    },
  });
}

// Delete project mutation
export function useDeleteProject() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => ProjectService.deleteProject(id),
    onSuccess: (_, deletedId) => {
      // Invalidate and refetch projects list
      queryClient.invalidateQueries({ queryKey: projectKeys.lists() });
      
      // Remove the project from the cache
      queryClient.removeQueries({ queryKey: projectKeys.detail(deletedId) });
      
      toast.success('Projet supprimé avec succès');
    },
    onError: (error) => {
      console.error('Error deleting project:', error);
      toast.error('Erreur lors de la suppression du projet');
    },
  });
}
