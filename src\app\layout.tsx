import type { Metadata } from "next";
import { AuthProvider } from '@/components/providers/auth-provider';
import { QueryProvider } from '@/components/providers/query-provider';
import { Toaster } from '@/components/ui/sonner';
import "./globals.css";
import { Inter } from "next/font/google";


const inter = Inter({
  subsets: ['latin'],
  variable: '--font-sans', // Définir Inter comme la police sans-serif par défaut
});

export const metadata: Metadata = {
  title: "KYA Dashboards",
  description: "Application de dashboards automatisés pour remplacer les suivis Excel manuels",
  keywords: ["dashboard", "analytics", "reporting", "KYA", "business intelligence"],
  authors: [{ name: "KYA Team" }],
  creator: "KYA Team",
  publisher: "KYA",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL(process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'),
  openGraph: {
    title: "KYA Dashboards",
    description: "Application de dashboards automatisés pour remplacer les suivis Excel manuels",
    type: "website",
    locale: "fr_FR",
  },
  robots: {
    index: false, // Private application
    follow: false,
  },
};

// export default function RootLayout({
//   children,
// }: Readonly<{
//   children: React.ReactNode;
// }>) {
//   return (
//     <html lang="fr" suppressHydrationWarning>
//       <body
//         className={`${geistSans.variable} ${geistMono.variable} antialiased w-full`}
//         suppressHydrationWarning
//       >
//         <AuthProvider>
//           {children}
//         </AuthProvider>
//       </body>
//     </html>
//   );
// }

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="fr" suppressHydrationWarning>
      <body className={`${inter.variable} font-sans m-0 p-0 overflow-x-hidden w-full`} suppressHydrationWarning>
          <QueryProvider>
            <AuthProvider>
              <div className="w-full min-h-screen">
                {children}
                <Toaster richColors closeButton position="top-right" />
              </div>
            </AuthProvider>
          </QueryProvider>
      </body>
    </html>
  )
}