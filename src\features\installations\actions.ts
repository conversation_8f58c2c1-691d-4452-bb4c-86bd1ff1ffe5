// Installation Server Actions - KYA Dashboards
'use server';

import { revalidatePath } from 'next/cache';
import { redirect } from 'next/navigation';
import { createClient } from '@/utils/supabase/server';
import { InstallationServerService } from './services/installation-server-service';
import type {
  CreateInstallationData,
  UpdateInstallationData,
  InstallationTrackingData
} from './types';

// Get current user ID from Supabase auth
async function getCurrentUserId(): Promise<string> {
  try {
    const supabase = await createClient();
    const { data: { user }, error } = await supabase.auth.getUser();

    if (error || !user) {
      throw new Error('User not authenticated');
    }

    return user.id;
  } catch (error) {
    console.error('Error getting current user:', error);
    throw new Error('Failed to get current user ID');
  }
}

// Installation CRUD Actions
export async function createInstallationAction(data: CreateInstallationData) {
  try {
    const userId = await getCurrentUserId();
    const installation = await InstallationServerService.createInstallation(data, userId);
    
    revalidatePath('/installations');
    return { success: true, data: installation };
  } catch (error) {
    console.error('Error creating installation:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Failed to create installation' 
    };
  }
}

export async function updateInstallationAction(id: string, data: UpdateInstallationData) {
  try {
    const userId = await getCurrentUserId();
    const installation = await InstallationServerService.updateInstallation(id, data, userId);
    
    revalidatePath('/installations');
    revalidatePath(`/installations/${id}`);
    return { success: true, data: installation };
  } catch (error) {
    console.error('Error updating installation:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Failed to update installation' 
    };
  }
}

export async function deleteInstallationAction(id: string) {
  try {
    await InstallationServerService.deleteInstallation(id);
    
    revalidatePath('/installations');
    return { success: true };
  } catch (error) {
    console.error('Error deleting installation:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Failed to delete installation' 
    };
  }
}

// Installation Tracking Actions
export async function updateInstallationTrackingAction(
  installationId: string,
  data: InstallationTrackingData
) {
  try {
    const userId = await getCurrentUserId();
    const tracking = await InstallationServerService.updateDailyTracking(installationId, data, userId);
    
    revalidatePath('/installations');
    revalidatePath(`/installations/${installationId}`);
    return { success: true, data: tracking };
  } catch (error) {
    console.error('Error updating installation tracking:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Failed to update tracking' 
    };
  }
}

// Utility Actions
export async function generateInstallationNumberAction() {
  try {
    const number = await InstallationServerService.generateInstallationNumber();
    return { success: true, data: number };
  } catch (error) {
    console.error('Error generating installation number:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Failed to generate number' 
    };
  }
}

// Bulk Actions
export async function bulkUpdateInstallationsAction(
  installationIds: string[], 
  data: Partial<UpdateInstallationData>
) {
  try {
    const userId = await getCurrentUserId();
    const results = await Promise.allSettled(
      installationIds.map(id =>
        InstallationServerService.updateInstallation(id, data, userId)
      )
    );
    
    const successful = results.filter(r => r.status === 'fulfilled').length;
    const failed = results.filter(r => r.status === 'rejected').length;
    
    revalidatePath('/installations');
    
    return { 
      success: true, 
      data: { successful, failed, total: installationIds.length } 
    };
  } catch (error) {
    console.error('Error bulk updating installations:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Failed to bulk update installations' 
    };
  }
}

// Form Actions with Validation
export async function submitInstallationFormAction(formData: FormData) {
  try {
    // Extract and validate form data
    const data: CreateInstallationData = {
      name: formData.get('name') as string,
      client_id: formData.get('client_id') as string,
      project_id: formData.get('project_id') as string || undefined,
      product_type: formData.get('product_type') as 'KYA-SoP' | 'Lampadaire',
      installation_number: formData.get('installation_number') as string,
      site_location: formData.get('site_location') as string,
      gps_coordinates: formData.get('gps_coordinates') as string || undefined,
      peak_power: formData.get('peak_power') as string || undefined,
      inverter_specs: formData.get('inverter_specs') as string || undefined,
      battery_capacity: formData.get('battery_capacity') as string || undefined,
      equipment_description: formData.get('equipment_description') as string || undefined,
      team_leader_id: formData.get('team_leader_id') as string || undefined,
      team_members: formData.get('team_members') ? 
        JSON.parse(formData.get('team_members') as string) : [],
      planned_start_date: formData.get('planned_start_date') ? 
        new Date(formData.get('planned_start_date') as string) : undefined,
      planned_end_date: formData.get('planned_end_date') ? 
        new Date(formData.get('planned_end_date') as string) : undefined,
      comments: formData.get('comments') as string || undefined,
    };

    // Basic validation
    if (!data.name || !data.client_id || !data.product_type || !data.installation_number || !data.site_location) {
      return { 
        success: false, 
        error: 'Required fields are missing' 
      };
    }

    const result = await createInstallationAction(data);
    
    if (result.success) {
      redirect('/installations');
    }
    
    return result;
  } catch (error) {
    console.error('Error submitting installation form:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Failed to submit form' 
    };
  }
}

export async function submitTrackingFormAction(
  installationId: string, 
  formData: FormData
) {
  try {
    // Extract tracking data from form
    const data: InstallationTrackingData = {
      execution_file_progress: parseInt(formData.get('execution_file_progress') as string) || 0,
      global_progress: parseInt(formData.get('global_progress') as string) || 0,
      
      // KYA-SoP specific fields
      metalwork_progress: formData.get('metalwork_progress') ? 
        parseInt(formData.get('metalwork_progress') as string) : undefined,
      excavation_progress: formData.get('excavation_progress') ? 
        parseInt(formData.get('excavation_progress') as string) : undefined,
      pv_supports_progress: formData.get('pv_supports_progress') ? 
        parseInt(formData.get('pv_supports_progress') as string) : undefined,
      modules_wiring_progress: formData.get('modules_wiring_progress') ? 
        parseInt(formData.get('modules_wiring_progress') as string) : undefined,
      pv_inverter_cables_progress: formData.get('pv_inverter_cables_progress') ? 
        parseInt(formData.get('pv_inverter_cables_progress') as string) : undefined,
      inverters_wiring_progress: formData.get('inverters_wiring_progress') ? 
        parseInt(formData.get('inverters_wiring_progress') as string) : undefined,
      batteries_wiring_progress: formData.get('batteries_wiring_progress') ? 
        parseInt(formData.get('batteries_wiring_progress') as string) : undefined,
      ac_dc_boxes_progress: formData.get('ac_dc_boxes_progress') ? 
        parseInt(formData.get('ac_dc_boxes_progress') as string) : undefined,
      load_separation_progress: formData.get('load_separation_progress') ? 
        parseInt(formData.get('load_separation_progress') as string) : undefined,
      battery_inverter_connection_progress: formData.get('battery_inverter_connection_progress') ? 
        parseInt(formData.get('battery_inverter_connection_progress') as string) : undefined,
      grounding_progress: formData.get('grounding_progress') ? 
        parseInt(formData.get('grounding_progress') as string) : undefined,
      
      // Lampadaire specific fields
      pole_installation_progress: formData.get('pole_installation_progress') ? 
        parseInt(formData.get('pole_installation_progress') as string) : undefined,
      lamp_installation_progress: formData.get('lamp_installation_progress') ? 
        parseInt(formData.get('lamp_installation_progress') as string) : undefined,
      
      // Testing and other fields
      commissioning_test_status: formData.get('commissioning_test_status') as any || undefined,
      commissioning_test_notes: formData.get('commissioning_test_notes') as string || undefined,
      commissioning_test_date: formData.get('commissioning_test_date') ? 
        new Date(formData.get('commissioning_test_date') as string) : undefined,
      
      daily_comments: formData.get('daily_comments') as string || undefined,
      issues_encountered: formData.get('issues_encountered') as string || undefined,
      next_actions: formData.get('next_actions') as string || undefined,
      hours_worked: formData.get('hours_worked') ? 
        parseFloat(formData.get('hours_worked') as string) : undefined,
      weather_conditions: formData.get('weather_conditions') as string || undefined,
    };

    const result = await updateInstallationTrackingAction(installationId, data);
    
    if (result.success) {
      revalidatePath(`/installations/${installationId}`);
    }
    
    return result;
  } catch (error) {
    console.error('Error submitting tracking form:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to submit tracking'
    };
  }
}

// Create installation tracking action
export async function createInstallationTracking(
  installationId: string,
  data: {
    tracking_date: string;
    overall_progress: number;
    foundation_progress?: number;
    installation_progress?: number;
    connection_progress?: number;
    testing_progress?: number;
    pole_installation_progress?: number;
    electrical_connection_progress?: number;
    lighting_test_progress?: number;
    weather_conditions?: string;
    team_present?: string;
    issues_encountered?: string;
    next_steps?: string;
    notes?: string;
  }
) {
  try {
    const userId = await getCurrentUserId();

    // Convert to InstallationTrackingData format
    const trackingData: InstallationTrackingData = {
      execution_file_progress: data.overall_progress,
      global_progress: data.overall_progress,

      // KYA-SoP specific fields
      metalwork_progress: data.foundation_progress,
      excavation_progress: data.installation_progress,
      pv_supports_progress: data.connection_progress,
      modules_wiring_progress: data.testing_progress,

      // Lampadaire specific fields
      pole_installation_progress: data.pole_installation_progress,
      lamp_installation_progress: data.electrical_connection_progress,

      // Common fields
      daily_comments: data.notes,
      issues_encountered: data.issues_encountered,
      next_actions: data.next_steps,
      weather_conditions: data.weather_conditions,
    };

    const tracking = await InstallationServerService.createInstallationTracking(
      installationId,
      trackingData,
      userId
    );

    revalidatePath(`/saisie/installations/${installationId}`);
    revalidatePath('/saisie/installations');

    return { success: true, data: tracking };
  } catch (error) {
    console.error('Error creating installation tracking:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to create tracking'
    };
  }
}
