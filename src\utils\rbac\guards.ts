import { createClient } from '@/utils/supabase/server';
import { RBACService } from '@/features/rbac/services/rbac-service';
import { redirect } from 'next/navigation';

/**
 * A server-side guard function to protect Server Actions.
 * It checks if the currently authenticated user has a specific permission.
 * If the user is not authenticated or lacks the permission, it throws an error.
 * 
 * @param permissionName The name of the permission required to perform the action.
 * @throws {Error} If the user is not authenticated or does not have the required permission.
 * @returns {Promise<{ userId: string }>} The ID of the authenticated and authorized user.
 */
export async function requireRBACPermission(permissionName: string): Promise<{ userId: string }> {
  const supabase = await createClient();
  const { data: { user } } = await supabase.auth.getUser();

  if (!user) {
    // This should ideally not be hit if middleware is set up correctly,
    // but it's a crucial security check.
    throw new Error('Not authenticated');
  }

  // We need our internal user ID from the `auth_users` table
  const { data: authUser } = await supabase
    .from('auth_users')
    .select('id')
    .eq('supabase_id', user.id)
    .single();

  if (!authUser) {
    throw new Error('Authenticated user not found in the system.');
  }

  // Check if user is SUPER_ADMIN - they bypass all permission checks
  const { data: userRoles } = await supabase
    .from('user_roles')
    .select(`
      roles (
        name
      )
    `)
    .eq('user_id', authUser.id)
    .eq('is_active', true);

  const isSuperAdmin = userRoles?.some((ur: any) => ur.roles?.name === 'SUPER_ADMIN');

  // SUPER_ADMIN bypasses all permission checks
  if (isSuperAdmin) {
    console.log(`SUPER_ADMIN ${authUser.id} bypassing permission check for: ${permissionName}`);
    return { userId: authUser.id };
  }

  // Normal permission check for other roles
  const hasPermission = await RBACService.checkUserPermission(authUser.id, permissionName);

  if (!hasPermission) {
    throw new Error(`Permission denied: ${permissionName}`);
  }

  return { userId: authUser.id };
}
