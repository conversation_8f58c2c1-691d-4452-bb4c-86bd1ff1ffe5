// Installation Service - KYA Dashboards
import { createClient } from '@/utils/supabase/client';
import type {
  Installation,
  InstallationTracking,
  CreateInstallationData,
  UpdateInstallationData,
  InstallationTrackingData,
  InstallationStats,
  InstallationKPIs,
  InstallationSearchParams,
  ProductType
} from '../types';

// Versioning logic function (same for all domains)
async function updateTracking(domainTable: string, domainId: string, data: any, userId: string) {
  const supabase = createClient();
  const today = new Date().toISOString().split('T')[0];

  const { data: existing } = await supabase
    .from(`${domainTable}_tracking`)
    .select('*')
    .eq(`${domainTable}_id`, domainId)
    .eq('tracking_date', today)
    .single();

  if (existing) {
    // Same day = UPDATE
    return supabase
      .from(`${domainTable}_tracking`)
      .update({ ...data, updated_by: userId })
      .eq(`${domainTable}_id`, domainId)
      .eq('tracking_date', today)
      .select()
      .single();
  } else {
    // Different day = INSERT
    return supabase
      .from(`${domainTable}_tracking`)
      .insert({
        [`${domainTable}_id`]: domainId,
        ...data,
        created_by: userId
      })
      .select()
      .single();
  }
}

export class InstallationService {
  
  // Main Installation CRUD Operations
  static async createInstallation(data: CreateInstallationData, userId: string): Promise<Installation> {
    const supabase = createClient();
    
    const installationData = {
      ...data,
      created_by: userId,
      status: 'PLANNING' as const
    };
    
    const { data: installation, error } = await supabase
      .from('installations')
      .insert(installationData)
      .select(`
        *,
        client:clients(*),
        project:projects(*),
        team_leader:persons(*)
      `)
      .single();
    
    if (error) throw error;
    return installation;
  }
  
  static async updateInstallation(id: string, data: UpdateInstallationData, userId: string): Promise<Installation> {
    const supabase = createClient();
    
    const updateData = {
      ...data,
      updated_by: userId,
      updated_at: new Date().toISOString()
    };
    
    const { data: installation, error } = await supabase
      .from('installations')
      .update(updateData)
      .eq('id', id)
      .select(`
        *,
        client:clients(*),
        project:projects(*),
        team_leader:persons(*)
      `)
      .single();
    
    if (error) throw error;
    return installation;
  }
  
  static async getInstallation(id: string): Promise<Installation | null> {
    const supabase = createClient();

    // First try to get from the complete view if it exists
    const { data: viewData, error: viewError } = await supabase
      .from('installation_details_complete')
      .select('*')
      .eq('id', id)
      .single();

    if (!viewError && viewData) {
      // Transform view data to match Installation interface
      return {
        ...viewData,
        client: {
          id: viewData.client_id,
          name: viewData.client_name,
          type: viewData.client_type,
          contact_info: viewData.client_contact_info,
          address: viewData.client_address,
          gps_coordinates: viewData.client_gps_coordinates,
          created_at: viewData.created_at,
          updated_at: viewData.updated_at
        },
        project: viewData.project_id ? {
          id: viewData.project_id,
          name: viewData.project_name,
          description: viewData.project_description,
          start_date: viewData.project_start_date,
          end_date: viewData.project_end_date,
          budget: viewData.project_budget,
          status: viewData.project_status,
          created_at: viewData.created_at,
          updated_at: viewData.updated_at
        } : null,
        team_leader: viewData.team_leader_id ? {
          id: viewData.team_leader_id,
          firstName: viewData.team_leader_first_name,
          lastName: viewData.team_leader_last_name,
          email: viewData.team_leader_email,
          phone: viewData.team_leader_phone,
          position: viewData.team_leader_position,
          created_at: viewData.created_at,
          updated_at: viewData.updated_at
        } : null,
        latest_tracking: viewData.latest_tracking_date ? [{
          id: `${viewData.id}_tracking`,
          installation_id: viewData.id,
          tracking_date: viewData.latest_tracking_date,
          global_progress: viewData.global_progress,
          execution_file_progress: viewData.execution_file_progress,
          metalwork_progress: viewData.metalwork_progress,
          excavation_progress: viewData.excavation_progress,
          pv_supports_progress: viewData.pv_supports_progress,
          modules_wiring_progress: viewData.modules_wiring_progress,
          pv_inverter_cables_progress: viewData.pv_inverter_cables_progress,
          inverters_wiring_progress: viewData.inverters_wiring_progress,
          batteries_wiring_progress: viewData.batteries_wiring_progress,
          ac_dc_boxes_progress: viewData.ac_dc_boxes_progress,
          load_separation_progress: viewData.load_separation_progress,
          battery_inverter_connection_progress: viewData.battery_inverter_connection_progress,
          grounding_progress: viewData.grounding_progress,
          pole_installation_progress: viewData.pole_installation_progress,
          lamp_installation_progress: viewData.lamp_installation_progress,
          commissioning_test_status: viewData.commissioning_test_status,
          created_at: viewData.created_at,
          updated_at: viewData.updated_at
        }] : []
      };
    }

    // Fallback to regular query if view doesn't exist
    const { data: installation, error } = await supabase
      .from('installations')
      .select(`
        *,
        client:clients(*),
        project:projects(*),
        team_leader:persons(*),
        latest_tracking:latest_installations_tracking(*)
      `)
      .eq('id', id)
      .single();

    if (error) return null;
    return installation;
  }
  
  static async getInstallations(params: InstallationSearchParams = {}): Promise<Installation[]> {
    const supabase = createClient();

    let query = supabase
      .from('installations')
      .select(`
        *,
        client:clients(*),
        project:projects(*),
        team_leader:persons(*),
        latest_tracking:latest_installations_tracking(*)
      `);
    
    // Apply filters
    if (params.filters?.product_type) {
      query = query.eq('product_type', params.filters.product_type);
    }
    
    if (params.filters?.status) {
      query = query.eq('status', params.filters.status);
    }
    
    if (params.filters?.team_leader_id) {
      query = query.eq('team_leader_id', params.filters.team_leader_id);
    }
    
    if (params.filters?.client_id) {
      query = query.eq('client_id', params.filters.client_id);
    }
    
    if (params.filters?.date_range) {
      query = query
        .gte('planned_start_date', params.filters.date_range.start.toISOString())
        .lte('planned_end_date', params.filters.date_range.end.toISOString());
    }
    
    // Apply search
    if (params.query) {
      query = query.or(`name.ilike.%${params.query}%,installation_number.ilike.%${params.query}%`);
    }
    
    // Apply sorting
    const sortBy = params.sort_by || 'created_at';
    const sortOrder = params.sort_order || 'desc';
    query = query.order(sortBy, { ascending: sortOrder === 'asc' });
    
    // Apply pagination
    if (params.page && params.limit) {
      const from = (params.page - 1) * params.limit;
      const to = from + params.limit - 1;
      query = query.range(from, to);
    }
    
    const { data: installations, error } = await query;
    
    if (error) throw error;
    return installations || [];
  }
  
  static async deleteInstallation(id: string): Promise<void> {
    const supabase = createClient();
    
    const { error } = await supabase
      .from('installations')
      .delete()
      .eq('id', id);
    
    if (error) throw error;
  }
  
  // Installation Tracking Operations (using versioning logic from guide)
  
  static async getTodayTracking(installationId: string): Promise<InstallationTracking | null> {
    const supabase = createClient();
    const today = new Date().toISOString().split('T')[0];

    const { data: tracking, error } = await supabase
      .from('installations_tracking')
      .select('*')
      .eq('installation_id', installationId)
      .eq('tracking_date', today)
      .single();

    if (error) return null;
    return tracking;
  }

  static async updateDailyTracking(
    installationId: string,
    trackingData: any,
    userId: string
  ): Promise<InstallationTracking> {
    const supabase = createClient();
    const today = new Date().toISOString().split('T')[0];

    // Vérifier s'il existe déjà un suivi pour aujourd'hui
    const existingTracking = await this.getTodayTracking(installationId);

    if (existingTracking) {
      // Mise à jour du suivi existant
      const { data, error } = await supabase
        .from('installations_tracking')
        .update({
          ...trackingData,
          updated_by: userId,
          updated_at: new Date().toISOString(),
        })
        .eq('id', existingTracking.id)
        .select()
        .single();

      if (error) {
        console.error('Error updating tracking:', error);
        throw error;
      }

      return data;
    } else {
      // Création d'un nouveau suivi
      const { data, error } = await supabase
        .from('installations_tracking')
        .insert({
          installation_id: installationId,
          tracking_date: today,
          ...trackingData,
          created_by: userId,
          updated_by: userId,
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating tracking:', error);
        throw error;
      }

      return data;
    }
  }
  
  static async getTrackingHistory(installationId: string): Promise<InstallationTracking[]> {
    const supabase = createClient();

    const { data: trackings, error } = await supabase
      .from('installations_tracking')
      .select('*')
      .eq('installation_id', installationId)
      .order('tracking_date', { ascending: false });

    if (error) throw error;
    return trackings || [];
  }

  // Statistics and KPIs
  static async getInstallationStats(teamLeaderId?: string, entityId?: string): Promise<InstallationStats> {
    const supabase = createClient();

    try {
      // Calculer les statistiques à partir des données réelles
      const { data: installations, error } = await supabase
        .from('installations')
        .select(`
          *,
          latest_tracking:installations_tracking(global_progress)
        `);

      if (error) throw error;

      const now = new Date();
      const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

      const total_installations = installations?.length || 0;
      const completed_installations = installations?.filter(i => i.status === 'COMPLETED').length || 0;
      const active_installations = installations?.filter(i =>
        i.status === 'IN_PROGRESS' || i.status === 'PLANNING'
      ).length || 0;

      const overdue_installations = installations?.filter(i =>
        i.planned_end_date && new Date(i.planned_end_date) < now && i.status !== 'COMPLETED'
      ).length || 0;

      const this_month_completed = installations?.filter(i =>
        i.actual_end_date && new Date(i.actual_end_date) >= startOfMonth
      ).length || 0;

      // Calculer la progression moyenne
      const installationsWithProgress = installations?.filter(i =>
        i.latest_tracking && i.latest_tracking.global_progress !== null
      ) || [];

      const average_progress = installationsWithProgress.length > 0
        ? installationsWithProgress.reduce((sum, i) => sum + (i.latest_tracking?.global_progress || 0), 0) / installationsWithProgress.length
        : 0;

      return {
        total_installations,
        active_installations,
        completed_installations,
        average_progress,
        overdue_installations,
        this_month_completed
      };
    } catch (error) {
      console.error('Error fetching installation stats:', error);
      // Fallback en cas d'erreur
      return {
        total_installations: 0,
        active_installations: 0,
        completed_installations: 0,
        average_progress: 0,
        overdue_installations: 0,
        this_month_completed: 0
      };
    }
  }
  
  static async getInstallationKPIs(teamLeaderId?: string, entityId?: string): Promise<InstallationKPIs> {
    const supabase = createClient();

    try {
      // Get basic stats
      const stats = await this.getInstallationStats(teamLeaderId, entityId);

      // Get progress by product type
      const { data: installationsWithProgress } = await supabase
        .from('installations')
        .select(`
          product_type,
          installations_tracking(global_progress)
        `);

      const progress_by_type: Record<string, number> = {
        'KYA-SoP': 0,
        'Lampadaire': 0
      };

      if (installationsWithProgress) {
        const typeGroups: Record<string, number[]> = {};

        installationsWithProgress.forEach((installation: any) => {
          const type = installation.product_type;
          if (!typeGroups[type]) typeGroups[type] = [];

          if (installation.installations_tracking && installation.installations_tracking.length > 0) {
            const latestTracking = installation.installations_tracking[0];
            typeGroups[type].push(latestTracking.global_progress || 0);
          }
        });

        Object.entries(typeGroups).forEach(([type, progresses]) => {
          if (progresses.length > 0) {
            progress_by_type[type] = progresses.reduce((sum, p) => sum + p, 0) / progresses.length;
          }
        });
      }

      // Get team performance
      const { data: teamData } = await supabase
        .from('installations')
        .select(`
          team_leader_id,
          status,
          team_leader:persons(firstName, lastName),
          installations_tracking(global_progress)
        `)
        .not('team_leader_id', 'is', null);

      const team_performance: any[] = [];
      if (teamData) {
        const teamGroups = teamData.reduce((acc, installation) => {
          const leaderId = installation.team_leader_id;
          if (!acc[leaderId]) {
            acc[leaderId] = {
              team_leader_id: leaderId,
              team_leader_name: installation.team_leader
                ? `${(installation.team_leader as any).firstName} ${(installation.team_leader as any).lastName}`
                : 'Inconnu',
              installations: []
            };
          }
          acc[leaderId].installations.push(installation);
          return acc;
        }, {} as Record<string, any>);

        Object.values(teamGroups).forEach((team: any) => {
          const installations = team.installations;
          const completedCount = installations.filter((i: any) => i.status === 'COMPLETED').length;
          const progresses = installations
            .map((i: any) => i.installations_tracking?.global_progress || 0)
            .filter((p: number) => p > 0);

          team_performance.push({
            team_leader_id: team.team_leader_id,
            team_leader_name: team.team_leader_name,
            installations_count: installations.length,
            average_progress: progresses.length > 0
              ? progresses.reduce((sum: number, p: number) => sum + p, 0) / progresses.length
              : 0,
            completion_rate: installations.length > 0
              ? (completedCount / installations.length) * 100
              : 0
          });
        });
      }

      // Get recent completions
      const { data: recent_completions } = await supabase
        .from('installations')
        .select(`
          *,
          client:clients(*),
          team_leader:persons(*)
        `)
        .eq('status', 'COMPLETED')
        .not('actual_end_date', 'is', null)
        .order('actual_end_date', { ascending: false })
        .limit(5);

      // Get overdue installations
      const { data: overdue_installations } = await supabase
        .from('installations')
        .select(`
          *,
          client:clients(*),
          team_leader:persons(*)
        `)
        .neq('status', 'COMPLETED')
        .lt('planned_end_date', new Date().toISOString())
        .order('planned_end_date', { ascending: true });

      return {
        stats,
        progress_by_type,
        team_performance,
        recent_completions: recent_completions || [],
        overdue_installations: overdue_installations || []
      };
    } catch (error) {
      console.error('Error fetching installation KPIs:', error);
      // Fallback en cas d'erreur
      const stats = await this.getInstallationStats(teamLeaderId, entityId);
      return {
        stats,
        progress_by_type: { 'KYA-SoP': 0, 'Lampadaire': 0 },
        team_performance: [],
        recent_completions: [],
        overdue_installations: []
      };
    }
  }
  
  // Utility functions
  static async generateInstallationNumber(): Promise<string> {
    const supabase = createClient();
    const year = new Date().getFullYear();
    const prefix = `INST-${year}-`;

    // Get the last installation number for this year
    const { data: lastInstallation } = await supabase
      .from('installations')
      .select('installation_number')
      .like('installation_number', `${prefix}%`)
      .order('installation_number', { ascending: false })
      .limit(1)
      .single();

    let nextNumber = 1;
    if (lastInstallation) {
      const lastNumber = parseInt(lastInstallation.installation_number.split('-')[2]);
      nextNumber = lastNumber + 1;
    }

    return `${prefix}${nextNumber.toString().padStart(3, '0')}`;
  }
  
  static getProgressStepsForProduct(productType: ProductType): string[] {
    const commonSteps = ['execution_file_progress', 'metalwork_progress', 'excavation_progress'];
    
    if (productType === 'KYA-SoP') {
      return [
        ...commonSteps,
        'pv_supports_progress',
        'modules_wiring_progress',
        'pv_inverter_cables_progress',
        'inverters_wiring_progress',
        'batteries_wiring_progress',
        'ac_dc_boxes_progress',
        'load_separation_progress',
        'battery_inverter_connection_progress',
        'grounding_progress'
      ];
    } else if (productType === 'Lampadaire') {
      return [
        ...commonSteps,
        'pole_installation_progress',
        'lamp_installation_progress'
      ];
    }
    
    return commonSteps;
  }
}
