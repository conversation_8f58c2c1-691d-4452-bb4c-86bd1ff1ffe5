import { camelCase, isArray, isObject, transform, snakeCase } from 'lodash';

/**
 * Recursively converts object keys from snake_case to camelCase.
 * @param obj The object to convert.
 * @returns A new object with camelCase keys.
 */
export function keysToCamel(obj: any): any {
  if (isArray(obj)) {
    return obj.map((v: any) => keysToCamel(v));
  }
  if (isObject(obj) && obj.constructor === Object) {
    return transform(obj, (result: { [key: string]: any }, value: any, key: string) => {
      const camelKey = camelCase(key);
      result[camelKey] = keysToCamel(value);
    });
  }
  return obj;
}

/**
 * Recursively converts object keys from camelCase to snake_case.
 * @param obj The object to convert.
 * @returns A new object with snake_case keys.
 */
export function keysToSnake(obj: any): any {
  if (isArray(obj)) {
    return obj.map((v: any) => keysToSnake(v));
  }
  if (isObject(obj) && obj.constructor === Object) {
    return transform(obj, (result: { [key: string]: any }, value: any, key: string) => {
      const snake = snakeCase(key);
      result[snake] = keysToSnake(value);
    });
  }
  return obj;
}