'use client';

import { useQuery } from '@tanstack/react-query';
import { InstallationService } from '../services/installation-service';
import { Installation } from '../types';

export function useInstallationDetails(id: string) {
  return useQuery<Installation | null, Error>({
    queryKey: ['installation-details', id],
    queryFn: () => InstallationService.getInstallation(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 2,
    refetchOnWindowFocus: false,
  });
}
