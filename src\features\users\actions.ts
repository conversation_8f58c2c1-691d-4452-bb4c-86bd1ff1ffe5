'use server';

import { z } from 'zod';
import { revalidatePath } from 'next/cache';
import { UserService } from './services/user-service';
import { requireRBACPermission } from '@/utils/rbac/guards';
import {
  createUserFormSchema,
  updateUserFormSchema,
} from '@/features/users/schemas';

/**
 * Server Action to fetch all users.
 * Requires 'admin.users.read' permission.
 */
export async function getUsersAction() {
  try {
    await requireRBACPermission('admin.users.read');
    const users = await UserService.getUsers();
    return { data: users };
  } catch (error: any) {
    console.error('Error in getUsersAction:', error.message);
    return { error: "Impossible de récupérer les utilisateurs." };
  }
}

/**
 * Server Action to fetch a single user by ID.
 * Requires 'admin.users.read' permission.
 */
export async function getUserByIdAction(id: string) {
  try {
    await requireRBACPermission('admin.users.read');
    const user = await UserService.getUserById(id);
    if (!user) {
      return { error: 'Utilisateur non trouvé.' };
    }
    return { data: user };
  } catch (error: any) {
    console.error(`Error in getUserByIdAction for id ${id}:`, error.message);
    return { error: "Impossible de récupérer l'utilisateur." };
  }
}

/**
 * Server Action to create a new user.
 * Requires 'admin.users.manage' permission.
 */
export async function createUserAction(
  values: z.infer<typeof createUserFormSchema>
) {
  try {
    await requireRBACPermission('admin.users.manage');
    const validatedData = createUserFormSchema.parse(values);
    const user = await UserService.createUser(validatedData);
    revalidatePath('/admin/users');
    return { data: user };
  } catch (error: any) {
    console.error('Error in createUserAction:', error);
    if (error instanceof z.ZodError) {
      return { error: 'Données invalides.', details: error.flatten() };
    }
    return { error: "Impossible de créer l'utilisateur." };
  }
}

/**
 * Server Action to update an existing user.
 * Requires 'admin.users.manage' permission.
 */
export async function updateUserAction(
  id: string,
  values: z.infer<typeof updateUserFormSchema>
) {
  try {
    await requireRBACPermission('admin.users.manage');
    const validatedData = updateUserFormSchema.parse(values);
    const user = await UserService.updateUser(id, validatedData);
    revalidatePath('/admin/users');
    return { data: user };
  } catch (error: any) {
    console.error(`Error in updateUserAction for id ${id}:`, error);
    if (error instanceof z.ZodError) {
      return { error: 'Données invalides.', details: error.flatten() };
    }
    return { error: "Impossible de mettre à jour l'utilisateur." };
  }
}

/**
 * Server Action to delete a user.
 * Requires 'admin.users.manage' permission.
 */
export async function deleteUserAction(id: string) {
  try {
    await requireRBACPermission('admin.users.manage');
    await UserService.deleteUser(id);
    revalidatePath('/admin/users');
    return { data: { success: true } };
  } catch (error: any) {
    console.error(`Error in deleteUserAction for id ${id}:`, error.message);
    return { error: "Impossible de supprimer l'utilisateur." };
  }
}