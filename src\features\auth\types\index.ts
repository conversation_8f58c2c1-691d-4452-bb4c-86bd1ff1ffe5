// Auth Feature Types
export * from '@/types/auth';

// Additional feature-specific types
export interface AuthError {
  code: string;
  message: string;
  details?: Record<string, any>;
}

export interface AuthState {
  user: import('@/types/auth').AuthUser | null;
  profile: import('@/types/auth').UserProfile | null;
  session: import('@/types/auth').AuthSession | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  error: AuthError | null;
}

export interface AuthActions {
  login: (credentials: import('@/types/auth').LoginCredentials) => Promise<import('@/types/auth').LoginResponse>;
  logout: () => Promise<void>;
  refreshSession: () => Promise<void>;
  updateProfile: (profile: Partial<import('@/types/auth').UserProfile>) => Promise<import('@/types/auth').UserProfile>;
  clearError: () => void;
  resetPassword?: (email: string) => Promise<void>;
  changePassword?: (data: import('@/types/auth').PasswordChangeData) => Promise<void>;
}

export interface UseAuthReturn extends AuthState, AuthActions {}

// Form state types
export interface LoginFormState {
  email: string;
  password: string;
  rememberMe: boolean;
  isSubmitting: boolean;
  errors: Record<string, string>;
}

export interface PasswordResetFormState {
  email: string;
  isSubmitting: boolean;
  isSuccess: boolean;
  errors: Record<string, string>;
}

export interface PasswordChangeFormState {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
  isSubmitting: boolean;
  errors: Record<string, string>;
}

export interface ProfileFormState {
  displayName: string;
  bio: string;
  preferences: {
    theme: 'light' | 'dark' | 'system';
    language: string;
    timezone: string;
    dateFormat: string;
    numberFormat: string;
  };
  notificationSettings: {
    email: boolean;
    push: boolean;
    inApp: boolean;
    alerts: boolean;
    reports: boolean;
  };
  isSubmitting: boolean;
  errors: Record<string, string>;
}
