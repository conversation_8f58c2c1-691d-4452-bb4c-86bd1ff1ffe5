import { z } from 'zod';

export const userFormSchema = z.object({
  displayName: z.string().min(2, {
    message: "Le nom d'affichage doit contenir au moins 2 caractères.",
  }),
  email: z.string().email({
    message: 'Veu<PERSON>z saisir une adresse e-mail valide.',
  }),
  roleIds: z.array(z.string()).min(1, {
    message: "L'utilisateur doit avoir au moins un rôle.",
  }),
});

export const createUserFormSchema = userFormSchema.extend({
  password: z.string().min(8, {
    message: 'Le mot de passe doit contenir au moins 8 caractères.',
  }),
});

export const updateUserFormSchema = userFormSchema.extend({
  password: z
    .string()
    .optional()
    .refine((val) => val === '' || (val?.length || 0) >= 8, {
      message: 'Le mot de passe doit contenir au moins 8 caractères.',
    }),
});

export type UserFormValues = z.infer<typeof userFormSchema>;
export type CreateUserFormValues = z.infer<typeof createUserFormSchema>;
export type UpdateUserFormValues = z.infer<typeof updateUserFormSchema>;