import { createClient } from '@/utils/supabase/server';
import { keysToCamel, keysToSnake } from '@/utils/case-converter';
import type { Entity, CreateEntityData, UpdateEntityData, EntityNavigationItem } from '@/types/entities';
import { AuditService } from '@/utils/services/audit-service';
import { EntityFeatureMappingService } from './entity-feature-mapping';

export class EntityService {

  /**
   * Get all entities with optional filters
   */
  static async getAllEntities(filters: { isActive?: boolean } = {}): Promise<Entity[]> {
    const supabase = await createClient();

    let query = supabase
      .from('entities')
      .select('*')
      .order('name', { ascending: true });

    if (filters.isActive !== undefined) {
      query = query.eq('is_active', filters.isActive);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching entities:', error);
      throw new Error('Failed to fetch entities.');
    }

    return keysToCamel(data || []);
  }

  static async getHierarchy(): Promise<Entity[]> {
    const supabase = await createClient();
    const { data, error } = await supabase
      .from('entities')
      .select('*')
      .order('name', { ascending: true });

    if (error) {
      console.error('Error fetching entities:', error);
      throw new Error('Failed to fetch organizational hierarchy.');
    }

    const entities: Entity[] = keysToCamel(data);

    const entityMap = new Map<string, Entity>();
    const roots: Entity[] = [];

    entities.forEach(entity => {
      entity.children = []; // Initialize children array
      entityMap.set(entity.id, entity);
    });

    entities.forEach(entity => {
      if (entity.parentId && entityMap.has(entity.parentId)) {
        const parent = entityMap.get(entity.parentId)!;
        parent.children.push(entityMap.get(entity.id)!);
      } else {
        roots.push(entityMap.get(entity.id)!);
      }
    });

    return roots;
  }

  /**
   * Get hierarchy with feature mappings for navigation
   */
  static async getNavigationHierarchy(): Promise<EntityNavigationItem[]> {
    const hierarchy = await this.getHierarchy();
    return EntityFeatureMappingService.transformToNavigationItems(hierarchy);
  }

  /**
   * Get entity by ID with feature mapping information
   */
  static async getEntityWithMapping(id: string): Promise<(Entity & { featureMapping?: any }) | null> {
    const supabase = await createClient();
    const { data, error } = await supabase
      .from('entities')
      .select('*')
      .eq('id', id)
      .single();

    if (error || !data) return null;

    const entity: Entity = keysToCamel(data);
    const featureMapping = EntityFeatureMappingService.getFeatureMappingByEntity(entity);

    return {
      ...entity,
      featureMapping
    };
  }

  static async createEntity(data: CreateEntityData, createdBy: string): Promise<Entity> {
    const supabase = await createClient();
    const { data: entity, error } = await supabase
      .from('entities')
      .insert(keysToSnake(data))
      .select()
      .single();

    if (error) throw new Error(`Failed to create entity: ${error.message}`);

    await AuditService.log('CREATE', 'ENTITY', entity.id, {
      userId: createdBy,
      newValues: entity,
    });

    return keysToCamel(entity);
  }

  static async updateEntity(id: string, data: UpdateEntityData, updatedBy: string): Promise<Entity> {
    const supabase = await createClient();
    const { data: entity, error } = await supabase
      .from('entities')
      .update(keysToSnake(data))
      .eq('id', id)
      .select()
      .single();

    if (error) throw new Error(`Failed to update entity: ${error.message}`);

    await AuditService.log('UPDATE', 'ENTITY', id, {
      userId: updatedBy,
      newValues: entity,
    });

    return keysToCamel(entity);
  }

  static async deleteEntity(id: string, deletedBy: string): Promise<void> {
    const supabase = await createClient();
    const { error } = await supabase.from('entities').delete().eq('id', id);

    if (error) throw new Error(`Failed to delete entity: ${error.message}`);

    await AuditService.log('DELETE', 'ENTITY', id, { userId: deletedBy });
  }
}