// Test simple de la requête utilisateurs
// Exécutez avec: node -r dotenv/config src/test-user-query.js

const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Variables d\'environnement Supabase manquantes');
  console.log('NEXT_PUBLIC_SUPABASE_URL:', supabaseUrl ? '✅ Définie' : '❌ Manquante');
  console.log('NEXT_PUBLIC_SUPABASE_ANON_KEY:', supabaseKey ? '✅ Définie' : '❌ Manquante');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function testBasicQuery() {
  console.log('🧪 Test de requête basique sur auth_users...');
  
  try {
    const { data, error } = await supabase
      .from('auth_users')
      .select('id, email, is_active, created_at')
      .limit(5);

    if (error) {
      console.error('❌ Erreur requête basique:', error);
      return false;
    }

    console.log('✅ Requête basique réussie!');
    console.log(`📊 Nombre d'utilisateurs: ${data.length}`);
    if (data.length > 0) {
      console.log('👤 Premier utilisateur:', data[0].email);
    }
    return true;

  } catch (err) {
    console.error('💥 Erreur inattendue:', err);
    return false;
  }
}

async function testJoinQuery() {
  console.log('\n🔗 Test de requête avec jointures...');
  
  try {
    const { data, error } = await supabase
      .from('auth_users')
      .select(`
        id,
        email,
        is_active,
        user_profiles!auth_user_id (
          id,
          display_name
        ),
        user_roles!user_id (
          id,
          is_active,
          roles (
            name,
            display_name
          )
        )
      `)
      .eq('is_active', true)
      .limit(3);

    if (error) {
      console.error('❌ Erreur requête avec jointures:', error);
      return false;
    }

    console.log('✅ Requête avec jointures réussie!');
    console.log(`📊 Nombre d'utilisateurs: ${data.length}`);
    
    data.forEach((user, index) => {
      console.log(`\n👤 Utilisateur ${index + 1}:`);
      console.log(`  - Email: ${user.email}`);
      console.log(`  - Profil: ${user.user_profiles?.display_name || 'Aucun'}`);
      console.log(`  - Rôles: ${user.user_roles?.map(ur => ur.roles?.name).join(', ') || 'Aucun'}`);
    });

    return true;

  } catch (err) {
    console.error('💥 Erreur inattendue:', err);
    return false;
  }
}

async function testTableStructure() {
  console.log('\n🏗️ Test de structure des tables...');
  
  const tables = ['auth_users', 'user_profiles', 'user_roles', 'roles'];
  
  for (const table of tables) {
    try {
      const { data, error } = await supabase
        .from(table)
        .select('*')
        .limit(1);

      if (error) {
        console.error(`❌ Table ${table}:`, error.message);
      } else {
        console.log(`✅ Table ${table}: OK (${data.length} enregistrement(s) trouvé(s))`);
      }
    } catch (err) {
      console.error(`💥 Table ${table}:`, err.message);
    }
  }
}

async function main() {
  console.log('🚀 Démarrage des tests de requête utilisateurs...\n');
  
  await testTableStructure();
  
  const basicOk = await testBasicQuery();
  if (!basicOk) {
    console.log('\n❌ Test basique échoué, arrêt des tests.');
    return;
  }
  
  await testJoinQuery();
  
  console.log('\n✨ Tests terminés!');
}

main().catch(console.error);
