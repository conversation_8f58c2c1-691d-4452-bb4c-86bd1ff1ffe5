// Authentication and User Types
export interface AuthUser {
  id: string;
  email: string;
  supabaseId: string;
  personId?: string;
  userProfileId?: string;
  isActive: boolean;
  emailVerified: boolean;
  lastLoginAt?: Date;
  loginCount: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface UserProfile {
  id: string;
  displayName?: string;
  avatarUrl?: string;
  bio?: string;
  preferences: UserPreferences;
  notificationSettings: NotificationSettings;
  dashboardConfig: DashboardConfig;
  createdAt: Date;
  updatedAt: Date;
}

export interface UserPreferences {
  theme?: 'light' | 'dark' | 'system';
  language?: string;
  timezone?: string;
  dateFormat?: string;
  numberFormat?: string;
}

export interface NotificationSettings {
  email?: boolean;
  push?: boolean;
  inApp?: boolean;
  alerts?: boolean;
  reports?: boolean;
}

export interface DashboardConfig {
  defaultView?: string;
  widgets?: string[];
  layout?: Record<string, any>;
  filters?: Record<string, any>;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface LoginResponse {
  success: boolean;
  user: AuthUser;
  session: any; // Supabase Session type
  profile?: UserProfile;
}

export interface AuthSession {
  accessToken: string;
  refreshToken: string;
  expiresAt: Date;
  user: AuthUser;
}

export interface AuthContext {
  user: AuthUser | null;
  profile: UserProfile | null;
  session: AuthSession | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (credentials: LoginCredentials) => Promise<LoginResponse>;
  logout: () => Promise<void>;
  refreshSession: () => Promise<void>;
  updateProfile: (profile: Partial<UserProfile>) => Promise<UserProfile>;
  error: { code: string; message: string } | null;
  clearError: () => void;
  resetPassword?: (email: string) => Promise<void>;
  changePassword?: (data: PasswordChangeData) => Promise<void>;
}

export interface AuthGuardProps {
  children: React.ReactNode;
  requiredPermissions?: string[];
  requiredRoles?: string[];
  fallback?: React.ReactNode;
  redirectTo?: string;
}

// Form types
export interface LoginFormData {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface LoginFormErrors {
  email?: string;
  password?: string;
  general?: string;
}

// Form state type compatible with Record<string, string>
export interface LoginFormState {
  email: string;
  password: string;
  rememberMe: boolean;
  isSubmitting: boolean;
  errors: Record<string, string>;
}

export interface PasswordResetData {
  email: string;
}

export interface PasswordChangeData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

// Additional request types
export interface PasswordResetRequest {
  email: string;
}

export interface PasswordChangeRequest {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

export interface ProfileUpdateRequest {
  displayName?: string;
  avatarUrl?: string;
  bio?: string;
  preferences?: UserPreferences;
  notificationSettings?: NotificationSettings;
  dashboardConfig?: DashboardConfig;
}
