import { useMemo } from 'react';
import {
  <PERSON><PERSON>hart as RechartsBar<PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  ResponsiveContainer
} from 'recharts';
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  ChartLegend,
  ChartLegendContent,
  type ChartConfig
} from '@/components/ui/chart';
import { cn } from '@/lib/utils';

export interface BarChartData {
  label: string;
  value: number;
  fill?: string;
  [key: string]: any;
}

interface BarChartProps {
  data: BarChartData[];
  className?: string;
  showTooltip?: boolean;
  showLegend?: boolean;
  showGrid?: boolean;
  config?: ChartConfig;
  dataKey?: string;
  xAxisKey?: string;
  onBarClick?: (data: BarChartData, index: number) => void;
  barRadius?: number;
  margin?: {
    top?: number;
    right?: number;
    bottom?: number;
    left?: number;
  };
}

const defaultColors = [
  'hsl(var(--chart-1))', // KYA Primary
  'hsl(var(--chart-2))', // KYA Secondary
  'hsl(var(--chart-3))', // KYA Accent
  'hsl(var(--chart-4))', // KYA Brown
  'hsl(var(--chart-5))', // Additional colors
];

export function BarChart({
  data,
  className,
  showTooltip = true,
  showLegend = false,
  showGrid = true,
  config,
  dataKey = 'value',
  xAxisKey = 'label',
  onBarClick,
  barRadius = 4,
  margin = { top: 20, right: 30, left: 20, bottom: 5 },
}: BarChartProps) {
  const processedData = useMemo(() => {
    return data.map((item, index) => ({
      ...item,
      fill: item.fill || `var(--color-${item.label.toLowerCase().replace(/\s+/g, '-')})` || defaultColors[index % defaultColors.length],
    }));
  }, [data]);

  const chartConfig = useMemo(() => {
    if (config) return config;

    return data.reduce((acc, item, index) => {
      const key = item.label.toLowerCase().replace(/\s+/g, '-');
      acc[key] = {
        label: item.label,
        color: defaultColors[index % defaultColors.length],
      };
      return acc;
    }, {} as ChartConfig);
  }, [data, config]);

  if (data.length === 0) {
    return (
      <div className={cn('flex items-center justify-center h-[300px]', className)}>
        <div className="text-center text-muted-foreground">
          <div className="text-4xl mb-2">📊</div>
          <p>Aucune donnée disponible</p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn('w-full', className)}>
      <ChartContainer config={chartConfig} className="h-[300px]">
        <RechartsBarChart
          data={processedData}
          margin={margin}
          onClick={onBarClick}
        >
          {showGrid && <CartesianGrid strokeDasharray="3 3" />}
          <XAxis
            dataKey={xAxisKey}
            tickLine={false}
            tickMargin={10}
            axisLine={false}
            fontSize={12}
          />
          <YAxis
            tickLine={false}
            tickMargin={10}
            axisLine={false}
            fontSize={12}
            tickFormatter={(value) => value.toLocaleString('fr-FR')}
          />

          {showTooltip && (
            <ChartTooltip
              content={
                <ChartTooltipContent
                  formatter={(value, name) => [
                    `${Number(value).toLocaleString('fr-FR')}`,
                    name
                  ]}
                />
              }
            />
          )}

          {showLegend && (
            <ChartLegend content={<ChartLegendContent />} />
          )}

          <Bar
            dataKey={dataKey}
            radius={[barRadius, barRadius, 0, 0]}
            className="hover:opacity-80 transition-opacity cursor-pointer"
          />
        </RechartsBarChart>
      </ChartContainer>
    </div>
  );
}

// Composant de graphique en barres groupées
interface GroupedBarChartProps {
  data: Array<{
    label: string;
    values: Array<{
      label: string;
      value: number;
      color?: string;
    }>;
  }>;
  width?: number;
  height?: number;
  showLegend?: boolean;
  className?: string;
}

export function GroupedBarChart({
  data,
  width = 500,
  height = 300,
  showLegend = true,
  className,
}: GroupedBarChartProps) {
  const flatData = data.flatMap(group => 
    group.values.map(value => ({
      label: `${group.label} - ${value.label}`,
      value: value.value,
      color: value.color,
      group: group.label,
      subLabel: value.label,
    }))
  );

  const legendItems = useMemo(() => {
    const items = new Map();
    data.forEach(group => {
      group.values.forEach(value => {
        if (!items.has(value.label)) {
          items.set(value.label, {
            label: value.label,
            color: value.color || defaultColors[items.size % defaultColors.length],
          });
        }
      });
    });
    return Array.from(items.values());
  }, [data]);

  return (
    <div className={cn('space-y-4', className)}>
      <BarChart
        data={flatData}
        width={width}
        height={height}
        showValues={true}
      />
      
      {showLegend && legendItems.length > 0 && (
        <div className="flex flex-wrap gap-4 justify-center">
          {legendItems.map((item, index) => (
            <div key={index} className="flex items-center space-x-2">
              <div
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: item.color }}
              />
              <span className="text-sm text-gray-700">{item.label}</span>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

// Hook pour préparer les données du graphique
export function useBarChartData<T>(
  data: T[],
  config: {
    labelKey: keyof T;
    valueKey: keyof T;
    colorKey?: keyof T;
    colors?: string[];
  }
) {
  return useMemo(() => {
    return data.map((item, index) => ({
      label: String(item[config.labelKey]),
      value: Number(item[config.valueKey]),
      color: config.colorKey 
        ? String(item[config.colorKey])
        : config.colors?.[index % (config.colors?.length || defaultColors.length)] 
        || defaultColors[index % defaultColors.length],
      metadata: item,
    }));
  }, [data, config]);
}

// Composant de mini graphique en barres
interface MiniBarChartProps {
  data: BarChartData[];
  width?: number;
  height?: number;
  color?: string;
  className?: string;
}

export function MiniBarChart({ 
  data, 
  width = 100, 
  height = 40, 
  color = '#1ca18c',
  className 
}: MiniBarChartProps) {
  return (
    <BarChart
      data={data.map(item => ({ ...item, color }))}
      width={width}
      height={height}
      showValues={false}
      showGrid={false}
      showAxes={false}
      className={className}
    />
  );
}
