// User Management Types
import type { AuthUser, UserProfile } from './auth';
import type { Person, Entity } from './entities';
import { Permission, Role } from './rbac';

export interface User extends AuthUser {
  // Relations (populated when needed)
  profile?: UserProfile;
  person?: Person;
  roles?: UserRole[];
  permissions?: UserPermission[];
  entities?: UserEntity[];
}
export type UserWithProfile = Omit<User, 'roles'> & {
  profile: UserProfile;
  roles: Role[];
};

export interface UserRole {
  id: string;
  userId: string;
  roleId: string;
  entityId?: string;
  assignedAt: Date;
  assignedBy?: string;
  expiresAt?: Date;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  
  // Relations (populated when needed)
  user?: User;
  role?: Role;
  entity?: Entity;
  assignedByUser?: User;
}

export interface UserPermission {
  id: string;
  userId: string;
  permissionId: string;
  entityId?: string;
  grantedAt: Date;
  grantedBy?: string;
  expiresAt?: Date;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  
  // Relations (populated when needed)
  user?: User;
  permission?: Permission;
  entity?: Entity;
  grantedByUser?: User;
}

export interface UserEntity {
  id: string;
  userId: string;
  entityId: string;
  accessLevel: UserAccessLevel;
  assignedAt: Date;
  assignedBy?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  
  // Relations (populated when needed)
  user?: User;
  entity?: Entity;
  assignedByUser?: User;
}

export type UserAccessLevel = 'READ' | 'WRITE' | 'ADMIN' | 'OWNER';

export interface UserSession {
  id: string;
  userId: string;
  sessionToken: string;
  deviceInfo?: string;
  ipAddress?: string;
  userAgent?: string;
  isActive: boolean;
  lastActivity: Date;
  expiresAt: Date;
  createdAt: Date;
  
  // Relations (populated when needed)
  user?: User;
}

// Form and Input Types
export interface CreateUserData {
  email: string;
  password?: string;
  personId?: string;
  profile?: Partial<UserProfile>;
  roles?: string[];
  entities?: string[];
  sendInvitation?: boolean;
}

export interface UpdateUserData {
  email?: string;
  personId?: string;
  isActive?: boolean;
  profile?: Partial<UserProfile>;
}

export interface AssignRoleData {
  userId: string;
  roleId: string;
  entityId?: string;
  expiresAt?: Date;
}

export interface GrantPermissionData {
  userId: string;
  permissionId: string;
  entityId?: string;
  expiresAt?: Date;
}

export interface AssignEntityData {
  userId: string;
  entityId: string;
  accessLevel: UserAccessLevel;
}

// Query and Filter Types
export interface UserFilters {
  isActive?: boolean;
  hasRole?: string;
  hasPermission?: string;
  entityId?: string;
  searchTerm?: string;
  lastLoginBefore?: Date;
  lastLoginAfter?: Date;
  createdBefore?: Date;
  createdAfter?: Date;
}

export interface UserRoleFilters {
  userId?: string;
  roleId?: string;
  entityId?: string;
  isActive?: boolean;
  expiresAfter?: Date;
  expiresBefore?: Date;
}

export interface UserPermissionFilters {
  userId?: string;
  permissionId?: string;
  entityId?: string;
  isActive?: boolean;
  resource?: string;
  action?: string;
}

// Statistics and Analytics Types
export interface UserStats {
  totalUsers: number;
  activeUsers: number;
  inactiveUsers: number;
  usersWithoutRoles: number;
  usersWithMultipleRoles: number;
  averageRolesPerUser: number;
  recentLogins: number;
  neverLoggedIn: number;
}

export interface UserActivityStats {
  userId: string;
  lastLogin?: Date;
  loginCount: number;
  sessionCount: number;
  activeSessionCount: number;
  averageSessionDuration: number;
  lastActivity?: Date;
}

export interface UserRoleStats {
  roleId: string;
  roleName: string;
  userCount: number;
  activeUserCount: number;
  entitiesCount: number;
  averageUsersPerEntity: number;
}

// Invitation and Onboarding Types
export interface UserInvitation {
  id: string;
  email: string;
  invitedBy: string;
  roles: string[];
  entities: string[];
  token: string;
  expiresAt: Date;
  acceptedAt?: Date;
  isUsed: boolean;
  createdAt: Date;
  
  // Relations (populated when needed)
  invitedByUser?: User;
}

export interface CreateInvitationData {
  email: string;
  roles: string[];
  entities?: string[];
  message?: string;
  expiresInDays?: number;
}

export interface AcceptInvitationData {
  token: string;
  password: string;
  profile: Partial<UserProfile>;
}

// Bulk Operations Types
export interface BulkUserOperation {
  operation: 'create' | 'update' | 'delete' | 'activate' | 'deactivate';
  userIds?: string[];
  data?: any;
  filters?: UserFilters;
}

export interface BulkOperationResult {
  success: boolean;
  processedCount: number;
  successCount: number;
  errorCount: number;
  errors: Array<{
    userId?: string;
    error: string;
  }>;
}
