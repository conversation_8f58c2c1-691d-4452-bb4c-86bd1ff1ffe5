// RBAC Feature Types

// Base RBAC types (local definitions to avoid import issues)
export interface Role {
  id: string;
  name: string;
  description?: string;
  entityId?: string;
  isActive: boolean;
  isSystemRole: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface Permission {
  id: string;
  name: string;
  resource: string;
  action: string;
  description?: string;
  isSystemPermission: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface PermissionMatrix {
  [roleId: string]: {
    [permissionId: string]: boolean;
  };
}

export interface RoleFilters {
  name?: string;
  entityId?: string;
  isActive?: boolean;
  isSystemRole?: boolean;
}

export interface PermissionFilters {
  resource?: string;
  action?: string;
  isSystemPermission?: boolean;
}

// Data types for RBAC operations
export interface CreateRoleData {
  name: string;
  description?: string;
  entityId?: string;
  isActive?: boolean;
}

export interface UpdateRoleData {
  name?: string;
  description?: string;
  isActive?: boolean;
}

export interface CreatePermissionData {
  name: string;
  resource: string;
  action: string;
  description?: string;
}

export interface AssignPermissionToRoleData {
  roleId: string;
  permissionId: string;
}

// Additional feature-specific types
export interface RBACError {
  code: string;
  message: string;
  details?: Record<string, any>;
}

export interface RBACState {
  roles: Role[];
  permissions: Permission[];
  permissionMatrix: PermissionMatrix | null;
  isLoading: boolean;
  error: RBACError | null;
}

export interface RBACActions {
  loadRoles: (filters?: RoleFilters) => Promise<void>;
  loadPermissions: (filters?: PermissionFilters) => Promise<void>;
  loadPermissionMatrix: () => Promise<void>;
  createRole: (data: CreateRoleData) => Promise<boolean>;
  updateRole: (roleId: string, data: UpdateRoleData) => Promise<boolean>;
  createPermission: (data: CreatePermissionData) => Promise<boolean>;
  assignPermissionToRole: (data: AssignPermissionToRoleData) => Promise<boolean>;
  revokePermissionFromRole: (roleId: string, permissionId: string) => Promise<boolean>;
  clearError: () => void;
}

export interface UseRBACReturn extends RBACState, RBACActions {}

// Form state types
export interface RoleFormState {
  name: string;
  displayName: string;
  description: string;
  level: number;
  permissions: string[];
  isSubmitting: boolean;
  errors: Record<string, string>;
}

export interface PermissionFormState {
  name: string;
  displayName: string;
  description: string;
  resource: string;
  action: import('@/types/rbac').PermissionAction;
  isSubmitting: boolean;
  errors: Record<string, string>;
}

// UI Component Props
export interface RoleListProps {
  onRoleSelect?: (role: import('@/types/rbac').Role) => void;
  selectedRoleId?: string;
  filters?: import('@/types/rbac').RoleFilters;
  showActions?: boolean;
  compact?: boolean;
}

export interface PermissionMatrixProps {
  selectedRoleId?: string;
  onPermissionToggle?: (roleId: string, permissionId: string, granted: boolean) => void;
  readOnly?: boolean;
  compact?: boolean;
}

export interface RoleCardProps {
  role: import('@/types/rbac').Role;
  onEdit?: (role: import('@/types/rbac').Role) => void;
  onDelete?: (roleId: string) => void;
  onSelect?: (role: import('@/types/rbac').Role) => void;
  isSelected?: boolean;
  showActions?: boolean;
}

export interface PermissionCardProps {
  permission: import('@/types/rbac').Permission;
  onEdit?: (permission: import('@/types/rbac').Permission) => void;
  onDelete?: (permissionId: string) => void;
  onSelect?: (permission: import('@/types/rbac').Permission) => void;
  isSelected?: boolean;
  showActions?: boolean;
}

// Permission checking types
export interface PermissionCheckResult {
  hasPermissions: boolean;
  isChecking: boolean;
  missingPermissions?: string[];
}

export interface RoleCheckResult {
  hasRoles: boolean;
  isChecking: boolean;
  missingRoles?: string[];
}

// Bulk operations
export interface BulkRoleAssignment {
  userIds: string[];
  roleIds: string[];
  entityId?: string;
}

export interface BulkPermissionAssignment {
  roleIds: string[];
  permissionIds: string[];
}

// Advanced filtering
export interface AdvancedRoleFilters {
  hasUsers?: boolean;
  userCount?: {
    min?: number;
    max?: number;
  };
  createdAfter?: Date;
  createdBefore?: Date;
  lastModifiedAfter?: Date;
  lastModifiedBefore?: Date;
}

export interface AdvancedPermissionFilters {
  hasRoles?: boolean;
  roleCount?: {
    min?: number;
    max?: number;
  };
  createdAfter?: Date;
  createdBefore?: Date;
}

// Analytics and reporting
export interface RoleAnalytics {
  roleId: string;
  roleName: string;
  userCount: number;
  permissionCount: number;
  entityCount: number;
  usageFrequency: number;
  lastUsed?: Date;
  trends: {
    userGrowth: number;
    permissionChanges: number;
  };
}

export interface PermissionAnalytics {
  permissionId: string;
  permissionName: string;
  roleCount: number;
  userCount: number;
  usageFrequency: number;
  lastGranted?: Date;
  trends: {
    grantFrequency: number;
    revokeFrequency: number;
  };
}

export interface RBACAnalytics {
  overview: import('@/types/rbac').RBACStats;
  roleAnalytics: RoleAnalytics[];
  permissionAnalytics: PermissionAnalytics[];
  trends: {
    totalRolesGrowth: number;
    totalPermissionsGrowth: number;
    averagePermissionsPerRole: number;
    mostUsedRoles: string[];
    mostUsedPermissions: string[];
  };
}

// Import/Export types
export interface RBACExport {
  roles: import('@/types/rbac').Role[];
  permissions: import('@/types/rbac').Permission[];
  assignments: Record<string, string[]>; // roleId -> permissionIds
  metadata: {
    exportedAt: Date;
    exportedBy: string;
    version: string;
    includeSystemRoles: boolean;
    includeSystemPermissions: boolean;
  };
}

export interface RBACImport {
  roles: Omit<import('@/types/rbac').Role, 'id' | 'createdAt' | 'updatedAt'>[];
  permissions: Omit<import('@/types/rbac').Permission, 'id' | 'createdAt' | 'updatedAt'>[];
  assignments: Record<string, string[]>; // roleName -> permissionNames
  options?: {
    overwriteExisting?: boolean;
    skipSystemRoles?: boolean;
    skipSystemPermissions?: boolean;
    validateReferences?: boolean;
  };
}

// Validation types
export interface RoleValidationResult {
  isValid: boolean;
  errors: Record<string, string>;
  warnings: Record<string, string>;
}

export interface PermissionValidationResult {
  isValid: boolean;
  errors: Record<string, string>;
  warnings: Record<string, string>;
}

export interface MatrixValidationResult {
  isValid: boolean;
  conflicts: Array<{
    roleId: string;
    permissionId: string;
    reason: string;
  }>;
  warnings: Array<{
    roleId: string;
    permissionId: string;
    message: string;
  }>;
}
