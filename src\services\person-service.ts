// Service pour la gestion des personnes - KYA Dashboards
import { createClient } from '@/utils/supabase/client';
import type { Person, CreatePersonData, UpdatePersonData } from '@/types/shared';

export class PersonService {
  
  // Récupérer toutes les personnes
  static async getPersons(): Promise<Person[]> {
    const supabase = createClient();
    
    const { data: persons, error } = await supabase
      .from('persons')
      .select('*')
      .order('last_name', { ascending: true });
    
    if (error) throw error;
    return persons || [];
  }

  // Récupérer une personne par ID
  static async getPerson(id: string): Promise<Person | null> {
    const supabase = createClient();
    
    const { data: person, error } = await supabase
      .from('persons')
      .select('*')
      .eq('id', id)
      .single();
    
    if (error) return null;
    return person;
  }

  // Récupérer seulement les personnes actives
  static async getActivePersons(): Promise<Person[]> {
    const supabase = createClient();
    
    const { data: persons, error } = await supabase
      .from('persons')
      .select('*')
      .eq('is_active', true)
      .order('last_name', { ascending: true });
    
    if (error) throw error;
    return persons || [];
  }

  // Créer une nouvelle personne
  static async createPerson(data: CreatePersonData): Promise<Person> {
    const supabase = createClient();
    
    const { data: person, error } = await supabase
      .from('persons')
      .insert(data)
      .select()
      .single();
    
    if (error) throw error;
    return person;
  }

  // Mettre à jour une personne
  static async updatePerson(id: string, data: UpdatePersonData): Promise<Person> {
    const supabase = createClient();
    
    const updateData = {
      ...data,
      updated_at: new Date().toISOString()
    };
    
    const { data: person, error } = await supabase
      .from('persons')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();
    
    if (error) throw error;
    return person;
  }

  // Supprimer une personne (soft delete)
  static async deletePerson(id: string): Promise<void> {
    const supabase = createClient();
    
    const { error } = await supabase
      .from('persons')
      .update({ 
        is_active: false,
        updated_at: new Date().toISOString()
      })
      .eq('id', id);
    
    if (error) throw error;
  }

  // Rechercher des personnes
  static async searchPersons(searchTerm: string): Promise<Person[]> {
    const supabase = createClient();
    
    const { data: persons, error } = await supabase
      .from('persons')
      .select('*')
      .or(`first_name.ilike.%${searchTerm}%,last_name.ilike.%${searchTerm}%,email.ilike.%${searchTerm}%,employee_id.ilike.%${searchTerm}%`)
      .eq('is_active', true)
      .order('last_name', { ascending: true })
      .limit(20);
    
    if (error) throw error;
    return persons || [];
  }

  // Récupérer les personnes par équipe/entité
  static async getPersonsByEntity(entityId: string): Promise<Person[]> {
    const supabase = createClient();
    
    const { data: persons, error } = await supabase
      .from('persons')
      .select('*')
      .eq('entity_id', entityId)
      .eq('is_active', true)
      .order('last_name', { ascending: true });
    
    if (error) throw error;
    return persons || [];
  }

  // Récupérer les chefs d'équipe
  static async getTeamLeaders(): Promise<Person[]> {
    const supabase = createClient();
    
    const { data: persons, error } = await supabase
      .from('persons')
      .select('*')
      .ilike('position', '%chef%')
      .eq('is_active', true)
      .order('last_name', { ascending: true });
    
    if (error) throw error;
    return persons || [];
  }

  // Récupérer les techniciens
  static async getTechnicians(): Promise<Person[]> {
    const supabase = createClient();
    
    const { data: persons, error } = await supabase
      .from('persons')
      .select('*')
      .ilike('position', '%technicien%')
      .eq('is_active', true)
      .order('last_name', { ascending: true });
    
    if (error) throw error;
    return persons || [];
  }
}
