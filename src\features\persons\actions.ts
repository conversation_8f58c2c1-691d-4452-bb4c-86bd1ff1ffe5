'use server';

import { z } from 'zod';
import { revalidatePath } from 'next/cache';
import { PersonService } from './services/person-service';
import { requireRBACPermission } from '@/utils/rbac/guards';
import {
  createPersonFormSchema,
  updatePersonFormSchema,
  personFiltersSchema,
  personSortSchema,
  paginationSchema,
  personEntitySchema,
  updatePersonEntitySchema,
  bulkPersonOperationSchema,
} from './schemas';

/**
 * Server Action to fetch persons with pagination and filters.
 * Requires 'admin.persons.read' permission.
 */
export async function getPersonsAction(
  filters?: z.infer<typeof personFiltersSchema>,
  sort?: z.infer<typeof personSortSchema>,
  pagination?: z.infer<typeof paginationSchema>
) {
  try {
    await requireRBACPermission('admin.persons.read');
    
    const validatedFilters = personFiltersSchema.parse(filters || {});
    const validatedSort = personSortSchema.parse(sort || { field: 'lastName', direction: 'asc' });
    const validatedPagination = paginationSchema.parse(pagination || { page: 1, limit: 20 });
    
    const result = await PersonService.getPersons(
      validatedFilters,
      validatedSort,
      validatedPagination.page,
      validatedPagination.limit
    );
    
    return { data: result };
  } catch (error: any) {
    console.error('Error in getPersonsAction:', error.message);
    return { error: "Impossible de récupérer les personnes." };
  }
}

/**
 * Server Action to fetch a single person by ID.
 * Requires 'admin.persons.read' permission.
 */
export async function getPersonByIdAction(id: string) {
  try {
    await requireRBACPermission('admin.persons.read');
    
    const person = await PersonService.getPersonById(id);
    if (!person) {
      return { error: 'Personne non trouvée.' };
    }
    
    return { data: person };
  } catch (error: any) {
    console.error(`Error in getPersonByIdAction for id ${id}:`, error.message);
    return { error: "Impossible de récupérer la personne." };
  }
}

/**
 * Server Action to create a new person.
 * Requires 'admin.persons.manage' permission.
 */
export async function createPersonAction(
  values: z.infer<typeof createPersonFormSchema>
) {
  try {
    const { userId } = await requireRBACPermission('admin.persons.manage');
    
    const parsedData = createPersonFormSchema.parse(values);
    const validatedData = {
      ...parsedData,
      hireDate: parsedData.hireDate || undefined,
    };
    const person = await PersonService.createPerson(validatedData, userId);
    
    revalidatePath('/admin/persons');
    return { data: person };
  } catch (error: any) {
    console.error('Error in createPersonAction:', error);
    if (error instanceof z.ZodError) {
      return { error: 'Données invalides.', details: error.flatten() };
    }
    return { error: "Impossible de créer la personne." };
  }
}

/**
 * Server Action to update an existing person.
 * Requires 'admin.persons.manage' permission.
 */
export async function updatePersonAction(
  id: string,
  values: z.infer<typeof updatePersonFormSchema>
) {
  try {
    const { userId } = await requireRBACPermission('admin.persons.manage');
    
    const validatedData = updatePersonFormSchema.parse(values);
    const person = await PersonService.updatePerson(id, validatedData, userId);
    
    revalidatePath('/admin/persons');
    revalidatePath(`/admin/persons/${id}`);
    return { data: person };
  } catch (error: any) {
    console.error(`Error in updatePersonAction for id ${id}:`, error);
    if (error instanceof z.ZodError) {
      return { error: 'Données invalides.', details: error.flatten() };
    }
    return { error: "Impossible de mettre à jour la personne." };
  }
}

/**
 * Server Action to delete a person (soft delete).
 * Requires 'admin.persons.manage' permission.
 */
export async function deletePersonAction(id: string) {
  try {
    const { userId } = await requireRBACPermission('admin.persons.manage');
    
    await PersonService.deletePerson(id, userId);
    
    revalidatePath('/admin/persons');
    return { data: { success: true } };
  } catch (error: any) {
    console.error(`Error in deletePersonAction for id ${id}:`, error.message);
    return { error: "Impossible de supprimer la personne." };
  }
}

/**
 * Server Action to get person statistics.
 * Requires 'admin.persons.read' permission.
 */
export async function getPersonStatsAction() {
  try {
    await requireRBACPermission('admin.persons.read');
    
    const stats = await PersonService.getPersonStats();
    return { data: stats };
  } catch (error: any) {
    console.error('Error in getPersonStatsAction:', error.message);
    return { error: "Impossible de récupérer les statistiques." };
  }
}

/**
 * Server Action to assign a person to an entity.
 * Requires 'admin.persons.assign' permission.
 */
export async function assignPersonToEntityAction(
  personId: string,
  values: z.infer<typeof personEntitySchema>
) {
  try {
    const { userId } = await requireRBACPermission('admin.persons.assign');
    
    const validatedData = personEntitySchema.parse(values);
    const assignment = await PersonService.assignPersonToEntity(personId, validatedData, userId);
    
    revalidatePath('/admin/persons');
    revalidatePath(`/admin/persons/${personId}`);
    revalidatePath('/admin/entities');
    return { data: assignment };
  } catch (error: any) {
    console.error('Error in assignPersonToEntityAction:', error);
    if (error instanceof z.ZodError) {
      return { error: 'Données invalides.', details: error.flatten() };
    }
    return { error: "Impossible d'assigner la personne à l'entité." };
  }
}

/**
 * Server Action to update a person's entity assignment.
 * Requires 'admin.persons.assign' permission.
 */
export async function updatePersonEntityAssignmentAction(
  assignmentId: string,
  values: z.infer<typeof updatePersonEntitySchema>
) {
  try {
    const { userId } = await requireRBACPermission('admin.persons.assign');
    
    const validatedData = updatePersonEntitySchema.parse(values);
    const assignment = await PersonService.updatePersonEntityAssignment(assignmentId, validatedData, userId);
    
    revalidatePath('/admin/persons');
    revalidatePath('/admin/entities');
    return { data: assignment };
  } catch (error: any) {
    console.error('Error in updatePersonEntityAssignmentAction:', error);
    if (error instanceof z.ZodError) {
      return { error: 'Données invalides.', details: error.flatten() };
    }
    return { error: "Impossible de mettre à jour l'assignation." };
  }
}

/**
 * Server Action to remove a person from an entity.
 * Requires 'admin.persons.assign' permission.
 */
export async function removePersonFromEntityAction(assignmentId: string) {
  try {
    const { userId } = await requireRBACPermission('admin.persons.assign');
    
    await PersonService.removePersonFromEntity(assignmentId, userId);
    
    revalidatePath('/admin/persons');
    revalidatePath('/admin/entities');
    return { data: { success: true } };
  } catch (error: any) {
    console.error(`Error in removePersonFromEntityAction for id ${assignmentId}:`, error.message);
    return { error: "Impossible de retirer la personne de l'entité." };
  }
}

/**
 * Server Action to get persons by entity.
 * Requires 'admin.persons.read' permission.
 */
export async function getPersonsByEntityAction(entityId: string) {
  try {
    await requireRBACPermission('admin.persons.read');
    
    const persons = await PersonService.getPersonsByEntity(entityId);
    return { data: persons };
  } catch (error: any) {
    console.error(`Error in getPersonsByEntityAction for entity ${entityId}:`, error.message);
    return { error: "Impossible de récupérer les personnes de l'entité." };
  }
}

/**
 * Server Action for bulk operations on persons.
 * Requires 'admin.persons.manage' permission.
 */
export async function bulkPersonOperationAction(
  values: z.infer<typeof bulkPersonOperationSchema>
) {
  try {
    const { userId } = await requireRBACPermission('admin.persons.manage');
    
    const validatedData = bulkPersonOperationSchema.parse(values);
    const { operation, personIds } = validatedData;
    
    // TODO: Implement bulk operations in PersonService
    // For now, just return success
    
    revalidatePath('/admin/persons');
    return { data: { success: true, processed: personIds.length } };
  } catch (error: any) {
    console.error('Error in bulkPersonOperationAction:', error);
    if (error instanceof z.ZodError) {
      return { error: 'Données invalides.', details: error.flatten() };
    }
    return { error: "Impossible d'effectuer l'opération en lot." };
  }
}
