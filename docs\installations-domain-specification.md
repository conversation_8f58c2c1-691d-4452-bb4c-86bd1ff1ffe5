# Domaine Installations - Spécification Technique

## Vue d'ensemble

Ce document spécifie l'implémentation du domaine "Installations" selon le concept général défini dans [`business-domain-concept.md`](business-domain-concept.md), en utilisant les données réelles extraites des classeurs Excel existants.

## Analyse des Données Existantes

### Structure Actuelle (Excel)

D'après l'analyse des classeurs fournis, le domaine installations contient :

**Colonnes principales identifiées :**
- **Identification** : Numéro d'installation, nom du projet
- **Client** : Nom client, type (particulier/entreprise)
- **Localisation** : Adresse, région
- **Équipe** : Responsable, équipe assignée
- **Planification** : Date début, date fin prévue
- **Statut** : Phase actuelle, pourcentage d'avancement
- **Technique** : Type d'installation, équipements
- **Suivi** : Commentaires, problèmes, décisions

## Modélisation des Données

### 1. Table Principale : `installations`

```sql
CREATE TABLE installations (
    -- Identifiants
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    installation_number VARCHAR(50) UNIQUE NOT NULL,  -- Ex: "INST-2024-001"
    name VARCHAR(255) NOT NULL,                       -- Nom du projet d'installation
    description TEXT NOT NULL,                        -- Description détaillée
    
    -- Relations avec données partagées
    client_id UUID NOT NULL REFERENCES clients(id),
    project_id UUID REFERENCES projects(id),
    product_id UUID NOT NULL REFERENCES products(id),
    
    -- Équipe flexible (selon nouveau modèle)
    team_members UUID[] NOT NULL,                     -- Array de person_id
    team_leader_id UUID NOT NULL REFERENCES persons(id),
    
    -- Données spécifiques aux installations
    installation_type VARCHAR(100) NOT NULL,         -- Type d'installation
    location_address TEXT NOT NULL,                  -- Adresse complète
    location_coordinates POINT,                      -- Coordonnées GPS optionnelles
    region VARCHAR(100),                             -- Région/Zone
    
    -- Planification
    planned_start_date DATE NOT NULL,
    planned_end_date DATE NOT NULL,
    actual_start_date DATE,
    actual_end_date DATE,
    
    -- Spécifications techniques
    equipment_specifications JSONB DEFAULT '{}',     -- Specs techniques
    technical_requirements TEXT,                     -- Exigences techniques
    site_constraints TEXT,                          -- Contraintes du site
    
    -- Métadonnées et traçabilité
    comments TEXT NOT NULL,                         -- Commentaires généraux
    metadata JSONB DEFAULT '{}',                    -- Données supplémentaires
    
    -- Audit
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID NOT NULL REFERENCES auth_users(id),
    updated_by UUID REFERENCES auth_users(id)
);
```

### 2. Table de Suivi : `installations_tracking`

```sql
CREATE TABLE installations_tracking (
    -- Identifiants
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    installation_id UUID NOT NULL REFERENCES installations(id) ON DELETE CASCADE,
    
    -- Statut et progression (champs communs)
    status VARCHAR(50) NOT NULL,                    -- PREPARATION, IN_PROGRESS, TESTING, COMPLETED, ON_HOLD
    progress_percentage INTEGER DEFAULT 0 CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
    
    -- Temps et effort (champs communs)
    hours_worked DECIMAL(5,2),
    estimated_hours_remaining DECIMAL(5,2),
    
    -- Commentaires et décisions (champs communs)
    daily_notes TEXT,
    issues_encountered TEXT,
    decisions_made TEXT,
    next_actions TEXT,
    
    -- Champs spécifiques aux installations (conditionnels selon produit)
    installation_phase VARCHAR(50),                 -- Phase spécifique: SITE_SURVEY, PREPARATION, INSTALLATION, TESTING, FINALIZATION
    equipment_status VARCHAR(50),                   -- État des équipements
    site_preparation_status VARCHAR(50),            -- État préparation site
    installation_quality_check BOOLEAN,            -- Contrôle qualité effectué
    client_approval_received BOOLEAN,               -- Approbation client reçue
    technical_documentation_completed BOOLEAN,      -- Documentation technique complète
    client_training_completed BOOLEAN,              -- Formation client effectuée
    
    -- Métriques spécifiques
    equipment_installed_count INTEGER,              -- Nombre d'équipements installés
    tests_passed_count INTEGER,                     -- Nombre de tests réussis
    defects_found_count INTEGER DEFAULT 0,          -- Nombre de défauts trouvés
    
    -- Conditions météo/site (si pertinent)
    weather_conditions VARCHAR(100),               -- Conditions météorologiques
    site_accessibility VARCHAR(100),               -- Accessibilité du site
    
    -- Audit
    tracking_date DATE NOT NULL DEFAULT CURRENT_DATE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID NOT NULL REFERENCES auth_users(id),
    updated_by UUID REFERENCES auth_users(id),
    
    -- Contrainte d'unicité par jour
    UNIQUE(installation_id, tracking_date)
);
```

### 3. Configuration des Produits pour Installations

```sql
-- Exemple de configuration dans la table products
INSERT INTO products (name, category, field_config) VALUES 
('Installation Solaire Résidentielle', 'INSTALLATION', '{
  "required_fields": ["installation_phase", "equipment_status"],
  "optional_fields": ["weather_conditions", "site_accessibility"],
  "conditional_fields": {
    "installation_phase": {
      "SITE_SURVEY": ["site_preparation_status", "site_accessibility"],
      "PREPARATION": ["equipment_status", "site_preparation_status"],
      "INSTALLATION": ["equipment_installed_count", "installation_quality_check"],
      "TESTING": ["tests_passed_count", "defects_found_count"],
      "FINALIZATION": ["client_approval_received", "client_training_completed", "technical_documentation_completed"]
    }
  },
  "status_workflow": ["PREPARATION", "IN_PROGRESS", "TESTING", "COMPLETED"],
  "phase_workflow": ["SITE_SURVEY", "PREPARATION", "INSTALLATION", "TESTING", "FINALIZATION"]
}'),
('Installation Éolienne Commerciale', 'INSTALLATION', '{
  "required_fields": ["installation_phase", "equipment_status", "weather_conditions"],
  "optional_fields": ["site_accessibility"],
  "conditional_fields": {
    "installation_phase": {
      "SITE_SURVEY": ["site_preparation_status", "weather_conditions"],
      "PREPARATION": ["equipment_status", "site_preparation_status"],
      "INSTALLATION": ["equipment_installed_count", "weather_conditions", "installation_quality_check"],
      "TESTING": ["tests_passed_count", "defects_found_count"],
      "FINALIZATION": ["client_approval_received", "technical_documentation_completed"]
    }
  },
  "status_workflow": ["PREPARATION", "IN_PROGRESS", "TESTING", "COMPLETED", "ON_HOLD"],
  "phase_workflow": ["SITE_SURVEY", "PREPARATION", "INSTALLATION", "TESTING", "FINALIZATION"]
}');
```

## Interface Utilisateur

### 1. Dashboard Installations - 5 Tabs

#### Tab 1: Aperçu Global
**Contenu :**
- **KPIs principaux** :
  - Installations en cours / Total
  - Taux de completion moyen
  - Retards identifiés
  - Heures travaillées cette semaine
- **Graphiques** :
  - Évolution des installations par mois
  - Répartition par phase d'installation
  - Performance par équipe
- **Carte géographique** : Localisation des installations actives

#### Tab 2: Suivi Journalier
**Contenu :**
- **Formulaire de saisie** adaptatif selon le produit sélectionné
- **Liste des installations actives** avec statut
- **Actions rapides** : Mise à jour statut, ajout commentaire
- **Planning du jour** : Installations prévues

**Exemple de formulaire adaptatif :**
```typescript
// Champs affichés selon le produit et la phase
const getFormFields = (product: Product, currentPhase: string) => {
  const config = product.field_config;
  const baseFields = ['status', 'progress_percentage', 'hours_worked', 'daily_notes'];
  const conditionalFields = config.conditional_fields[currentPhase] || [];
  
  return [...baseFields, ...conditionalFields];
};
```

#### Tab 3: Alertes & Décisions
**Contenu :**
- **Alertes critiques** :
  - Installations en retard
  - Problèmes techniques non résolus
  - Approbations clients en attente
- **Décisions en attente** :
  - Changements de planning
  - Modifications techniques
  - Ressources supplémentaires
- **Notifications** : Échéances importantes

#### Tab 4: Responsables & Équipes
**Contenu :**
- **Gestion des équipes** : Attribution des membres
- **Charge de travail** : Répartition par personne
- **Compétences** : Matching compétences/besoins
- **Planning** : Disponibilités et affectations

#### Tab 5: Historique & Performance
**Contenu :**
- **Métriques de performance** :
  - Temps moyen par phase
  - Taux de défauts par équipe
  - Satisfaction client
- **Analyses** :
  - Tendances par type d'installation
  - Comparaison équipes
  - Prédictions de délais
- **Rapports** : Export des données

### 2. Formulaire de Saisie Adaptatif

#### Structure du Formulaire
```typescript
interface InstallationTrackingForm {
  // Champs de base (toujours affichés)
  status: InstallationStatus;
  progress_percentage: number;
  hours_worked?: number;
  daily_notes?: string;
  
  // Champs conditionnels (selon produit et phase)
  installation_phase?: InstallationPhase;
  equipment_status?: EquipmentStatus;
  site_preparation_status?: SiteStatus;
  
  // Champs métriques (selon phase)
  equipment_installed_count?: number;
  tests_passed_count?: number;
  defects_found_count?: number;
  
  // Champs validation (selon phase)
  installation_quality_check?: boolean;
  client_approval_received?: boolean;
  technical_documentation_completed?: boolean;
}
```

#### Logique d'Affichage Conditionnel
```typescript
const InstallationTrackingForm = ({ installation, product }: Props) => {
  const [currentPhase, setCurrentPhase] = useState(installation.current_phase);
  const [formData, setFormData] = useState<InstallationTrackingForm>({});
  
  // Récupération de la configuration du produit
  const fieldConfig = product.field_config;
  const visibleFields = getVisibleFields(fieldConfig, currentPhase);
  
  return (
    <Form>
      {/* Champs de base toujours visibles */}
      <StatusSelect value={formData.status} onChange={handleStatusChange} />
      <ProgressSlider value={formData.progress_percentage} />
      <HoursInput value={formData.hours_worked} />
      
      {/* Champs conditionnels selon la phase */}
      {visibleFields.includes('installation_phase') && (
        <PhaseSelect 
          value={formData.installation_phase}
          options={fieldConfig.phase_workflow}
          onChange={setCurrentPhase}
        />
      )}
      
      {visibleFields.includes('equipment_status') && (
        <EquipmentStatusSelect value={formData.equipment_status} />
      )}
      
      {/* Champs métriques selon la phase */}
      {currentPhase === 'INSTALLATION' && (
        <MetricsSection>
          <NumberInput 
            label="Équipements installés"
            value={formData.equipment_installed_count}
          />
          <Checkbox 
            label="Contrôle qualité effectué"
            checked={formData.installation_quality_check}
          />
        </MetricsSection>
      )}
      
      {/* Section commentaires */}
      <NotesSection>
        <TextArea label="Notes du jour" value={formData.daily_notes} />
        <TextArea label="Problèmes rencontrés" value={formData.issues_encountered} />
        <TextArea label="Actions suivantes" value={formData.next_actions} />
      </NotesSection>
    </Form>
  );
};
```

## Services et Logique Métier

### 1. Service Installations

```typescript
export class InstallationService extends BaseDomainService<Installation, InstallationTracking> {
  
  async createInstallation(data: CreateInstallationData): Promise<Installation> {
    // Validation des données
    const validatedData = installationSchema.parse(data);
    
    // Génération du numéro d'installation
    const installationNumber = await this.generateInstallationNumber();
    
    // Création avec numéro généré
    const installation = await this.supabase
      .from('installations')
      .insert({
        ...validatedData,
        installation_number: installationNumber
      })
      .select()
      .single();
    
    // Audit
    await AuditService.log('installation.created', installation.id, validatedData);
    
    return installation;
  }
  
  async createTodayTracking(installationId: string, data: InstallationTrackingData): Promise<InstallationTracking> {
    // Vérifier si une saisie existe déjà aujourd'hui
    const existingTracking = await this.getTodayTracking(installationId);
    
    if (existingTracking) {
      // Mise à jour de la saisie existante
      return this.updateTodayTracking(installationId, data);
    } else {
      // Création d'une nouvelle saisie
      const tracking = await this.supabase
        .from('installations_tracking')
        .insert({
          installation_id: installationId,
          ...data,
          tracking_date: new Date().toISOString().split('T')[0]
        })
        .select()
        .single();
      
      await AuditService.log('installation.tracking.created', installationId, data);
      return tracking;
    }
  }
  
  async getInstallationsByTeam(teamMemberIds: string[]): Promise<Installation[]> {
    // Requête pour récupérer les installations d'une équipe
    const installations = await this.supabase
      .from('installations')
      .select(`
        *,
        client:clients(*),
        project:projects(*),
        product:products(*),
        team_leader:persons!team_leader_id(*),
        latest_tracking:installations_tracking(*)
      `)
      .overlaps('team_members', teamMemberIds)
      .eq('latest_tracking.tracking_date', new Date().toISOString().split('T')[0]);
    
    return installations;
  }
  
  async getInstallationStats(teamId?: string): Promise<InstallationStats> {
    // Calcul des statistiques d'installation
    const stats = await this.supabase.rpc('get_installation_stats', {
      team_filter: teamId
    });
    
    return stats;
  }
  
  private async generateInstallationNumber(): Promise<string> {
    const year = new Date().getFullYear();
    const prefix = `INST-${year}-`;
    
    // Récupérer le dernier numéro
    const lastInstallation = await this.supabase
      .from('installations')
      .select('installation_number')
      .like('installation_number', `${prefix}%`)
      .order('installation_number', { ascending: false })
      .limit(1)
      .single();
    
    let nextNumber = 1;
    if (lastInstallation) {
      const lastNumber = parseInt(lastInstallation.installation_number.split('-')[2]);
      nextNumber = lastNumber + 1;
    }
    
    return `${prefix}${nextNumber.toString().padStart(3, '0')}`;
  }
}
```

### 2. Hooks Spécialisés

```typescript
// Hook pour le suivi des installations
export function useInstallationTracking(installationId: string) {
  const { data: todayTracking, isLoading } = useQuery(
    ['installation-tracking', installationId, 'today'],
    () => InstallationService.getTodayTracking(installationId)
  );
  
  const createOrUpdateMutation = useMutation(
    (data: InstallationTrackingData) => 
      InstallationService.createTodayTracking(installationId, data),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['installation-tracking', installationId]);
        queryClient.invalidateQueries(['installation-stats']);
      }
    }
  );
  
  return {
    todayTracking,
    isLoading,
    createOrUpdate: createOrUpdateMutation.mutate,
    isSubmitting: createOrUpdateMutation.isLoading
  };
}

// Hook pour les statistiques d'équipe
export function useInstallationStats(teamId?: string) {
  return useQuery(
    ['installation-stats', teamId],
    () => InstallationService.getInstallationStats(teamId),
    {
      refetchInterval: 5 * 60 * 1000, // Refresh toutes les 5 minutes
    }
  );
}
```

## Types TypeScript

### 1. Types de Base

```typescript
export interface Installation {
  id: string;
  installation_number: string;
  name: string;
  description: string;
  
  // Relations
  client_id: string;
  project_id?: string;
  product_id: string;
  
  // Équipe
  team_members: string[];
  team_leader_id: string;
  
  // Spécifique installations
  installation_type: string;
  location_address: string;
  location_coordinates?: { lat: number; lng: number };
  region?: string;
  
  // Planification
  planned_start_date: Date;
  planned_end_date: Date;
  actual_start_date?: Date;
  actual_end_date?: Date;
  
  // Technique
  equipment_specifications: Record<string, any>;
  technical_requirements?: string;
  site_constraints?: string;
  
  // Métadonnées
  comments: string;
  metadata: Record<string, any>;
  
  // Audit
  created_at: Date;
  updated_at: Date;
  created_by: string;
  updated_by?: string;
  
  // Relations populées
  client?: Client;
  project?: Project;
  product?: Product;
  team_leader?: Person;
  latest_tracking?: InstallationTracking;
}

export interface InstallationTracking {
  id: string;
  installation_id: string;
  
  // Champs communs
  status: InstallationStatus;
  progress_percentage: number;
  hours_worked?: number;
  estimated_hours_remaining?: number;
  daily_notes?: string;
  issues_encountered?: string;
  decisions_made?: string;
  next_actions?: string;
  
  // Champs spécifiques installations
  installation_phase?: InstallationPhase;
  equipment_status?: EquipmentStatus;
  site_preparation_status?: SiteStatus;
  installation_quality_check?: boolean;
  client_approval_received?: boolean;
  technical_documentation_completed?: boolean;
  client_training_completed?: boolean;
  
  // Métriques
  equipment_installed_count?: number;
  tests_passed_count?: number;
  defects_found_count?: number;
  
  // Conditions
  weather_conditions?: string;
  site_accessibility?: string;
  
  // Audit
  tracking_date: Date;
  created_at: Date;
  updated_at: Date;
  created_by: string;
  updated_by?: string;
}
```

### 2. Enums et Types Utilitaires

```typescript
export type InstallationStatus = 
  | 'PREPARATION' 
  | 'IN_PROGRESS' 
  | 'TESTING' 
  | 'COMPLETED' 
  | 'ON_HOLD' 
  | 'CANCELLED';

export type InstallationPhase = 
  | 'SITE_SURVEY' 
  | 'PREPARATION' 
  | 'INSTALLATION' 
  | 'TESTING' 
  | 'FINALIZATION';

export type EquipmentStatus = 
  | 'NOT_DELIVERED' 
  | 'DELIVERED' 
  | 'INSTALLED' 
  | 'TESTED' 
  | 'OPERATIONAL';

export type SiteStatus = 
  | 'NOT_PREPARED' 
  | 'IN_PREPARATION' 
  | 'READY' 
  | 'ISSUES_FOUND';

export interface InstallationStats {
  total_installations: number;
  active_installations: number;
  completed_this_month: number;
  average_completion_time: number;
  installations_by_status: Record<InstallationStatus, number>;
  installations_by_phase: Record<InstallationPhase, number>;
  team_performance: Array<{
    team_leader_id: string;
    team_leader_name: string;
    installations_count: number;
    average_progress: number;
    completion_rate: number;
  }>;
}
```

## Migration et Déploiement

### 1. Script de Migration

```sql
-- Migration des données existantes depuis Excel vers le nouveau modèle
-- (À adapter selon la structure exacte des données Excel)

-- 1. Création des tables
-- (Utiliser les CREATE TABLE ci-dessus)

-- 2. Migration des données clients (si pas déjà fait)
INSERT INTO clients (name, type, contact_info, address)
SELECT DISTINCT 
  client_name,
  CASE WHEN client_type = 'Particulier' THEN 'HOUSEHOLD' ELSE 'INSTITUTION' END,
  '{}',
  client_address
FROM excel_installations_import;

-- 3. Migration des installations
INSERT INTO installations (
  installation_number, name, description, client_id, installation_type,
  location_address, planned_start_date, planned_end_date, team_members,
  team_leader_id, comments, created_by
)
SELECT 
  installation_number,
  project_name,
  COALESCE(description, 'Migré depuis Excel'),
  c.id,
  installation_type,
  location_address,
  planned_start_date,
  planned_end_date,
  ARRAY[team_leader_person_id], -- À adapter selon la structure équipe
  team_leader_person_id,
  COALESCE(comments, ''),
  system_user_id
FROM excel_installations_import ei
JOIN clients c ON c.name = ei.client_name;

-- 4. Migration du suivi (dernière entrée par installation)
INSERT INTO installations_tracking (
  installation_id, status, progress_percentage, daily_notes,
  tracking_date, created_by
)
SELECT 
  i.id,
  ei.current_status,
  ei.progress_percentage,
  ei.latest_notes,
  COALESCE(ei.last_update_date, CURRENT_DATE),
  system_user_id
FROM excel_installations_import ei
JOIN installations i ON i.installation_number = ei.installation_number;
```

### 2. Validation Post-Migration

```sql
-- Vérifications après migration
SELECT 
  'Installations' as table_name,
  COUNT(*) as count,
  COUNT(CASE WHEN team_members IS NULL OR array_length(team_members, 1) = 0 THEN 1 END) as missing_team,
  COUNT(CASE WHEN client_id IS NULL THEN 1 END) as missing_client
FROM installations

UNION ALL

SELECT 
  'Tracking' as table_name,
  COUNT(*) as count,
  COUNT(CASE WHEN status IS NULL THEN 1 END) as missing_status,
  COUNT(CASE WHEN tracking_date IS NULL THEN 1 END) as missing_date
FROM installations_tracking;
```

---

**Cette spécification fournit une implémentation complète et pragmatique du domaine installations, basée sur vos données réelles et suivant le concept architectural défini.**