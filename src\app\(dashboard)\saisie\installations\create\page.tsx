// Page de Création d'Installation - KYA Dashboards

import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Plus } from 'lucide-react';
import { InstallationCreateForm } from '@/features/installations/components/installation-create-form';

export default function CreateInstallationPage() {

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            <Button variant="ghost" size="sm" asChild>
              <Link href="/saisie/installations">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Retour aux installations
              </Link>
            </Button>
          </div>

          <div className="flex items-center gap-3 mb-2">
            <div className="p-2 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg">
              <Plus className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                Nouvelle Installation
              </h1>
              <p className="text-muted-foreground">
                Créez une nouvelle installation KYA-SoP ou Lampadaire
              </p>
            </div>
          </div>

          <div className="h-1 w-20 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full"></div>
        </div>

        {/* Formulaire */}
        <InstallationCreateForm />
      </div>
    </div>
  );
}
