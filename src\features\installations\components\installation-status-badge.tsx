'use client';

import { Badge } from '@/components/ui/badge';
import { Check<PERSON>ircle, Clock, AlertTriangle, Pause, XCircle, Wrench } from 'lucide-react';
import type { InstallationStatus } from '../types';

interface InstallationStatusBadgeProps {
  status: InstallationStatus;
  className?: string;
}

const STATUS_CONFIG = {
  PLANNING: {
    label: 'Planification',
    icon: Clock,
    variant: 'secondary' as const,
    className: 'bg-gray-100 text-gray-800 hover:bg-gray-200',
  },
  IN_PROGRESS: {
    label: 'En cours',
    icon: Wrench,
    variant: 'default' as const,
    className: 'bg-blue-100 text-blue-800 hover:bg-blue-200',
  },
  TESTING: {
    label: 'Tests',
    icon: AlertTriangle,
    variant: 'secondary' as const,
    className: 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200',
  },
  COMPLETED: {
    label: 'Terminé',
    icon: CheckCircle,
    variant: 'default' as const,
    className: 'bg-green-100 text-green-800 hover:bg-green-200',
  },
  ON_HOLD: {
    label: 'En attente',
    icon: Pause,
    variant: 'secondary' as const,
    className: 'bg-orange-100 text-orange-800 hover:bg-orange-200',
  },
  CANCELLED: {
    label: 'Annulé',
    icon: XCircle,
    variant: 'destructive' as const,
    className: 'bg-red-100 text-red-800 hover:bg-red-200',
  },
};

export function InstallationStatusBadge({ status, className }: InstallationStatusBadgeProps) {
  const config = STATUS_CONFIG[status];
  const Icon = config.icon;

  return (
    <Badge 
      variant={config.variant}
      className={`flex items-center gap-1 ${config.className} ${className || ''}`}
    >
      <Icon className="h-3 w-3" />
      {config.label}
    </Badge>
  );
}
