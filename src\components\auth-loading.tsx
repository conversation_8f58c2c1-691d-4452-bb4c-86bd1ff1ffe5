'use client';

import { cn } from "@/lib/utils"
import { Card, CardContent } from "@/components/ui/card"
import { Loader2, Shield } from "lucide-react"

interface AuthLoadingProps {
  message?: string
  className?: string
}

export function AuthLoading({ 
  message = "Chargement de la session...", 
  className 
}: AuthLoadingProps) {
  return (
    <div className={cn("flex flex-col items-center justify-center min-h-screen", className)}>
      <div className="flex items-center gap-3 mb-4">
        <Loader2 className="w-6 h-6 animate-spin text-primary" />
        <h2 className="text-lg font-semibold">{message}</h2>
      </div>
    </div>
  )
}