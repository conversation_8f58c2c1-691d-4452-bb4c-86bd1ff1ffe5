'use client';

import { useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import {
  MapPin,
  Calendar,
  User,
  Phone,
  Mail,
  Building,
  Zap,
  Settings,
  Clock,
  CheckCircle,
  AlertTriangle,
  TrendingUp,
  FileText,
  Camera,
  Edit,
  Download
} from 'lucide-react';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import { Installation, ReceptionStatus, FunctioningState, CommissioningTestStatus } from '../types';
import { getLatestTracking, getInstallationProgress } from '../utils/tracking-utils';
import { InstallationStatusBadge } from './installation-status-badge';
import { ProductTypeBadge } from './product-type-badge';

interface InstallationDetailsProps {
  installation: Installation;
}

export function InstallationDetails({ installation }: InstallationDetailsProps) {
  const [activeTab, setActiveTab] = useState('overview');
  
  const latestTracking = getLatestTracking(installation.latest_tracking);
  const progress = getInstallationProgress(installation.latest_tracking);
  


  const formatDate = (date: Date | string | null | undefined) => {
    if (!date) return 'Non définie';
    return format(new Date(date), 'dd/MM/yyyy', { locale: fr });
  };

  const getReceptionStatusLabel = (status: ReceptionStatus | undefined) => {
    switch (status) {
      case 'PENDING': return 'En attente';
      case 'APPROVED_WITH_RESERVES': return 'Approuvé avec réserves';
      case 'APPROVED_WITHOUT_RESERVES': return 'Approuvé sans réserves';
      case 'REJECTED': return 'Rejeté';
      default: return 'Non défini';
    }
  };

  const getFunctioningStateLabel = (state: FunctioningState | undefined) => {
    switch (state) {
      case 'EXCELLENT': return 'Excellent';
      case 'GOOD': return 'Bon';
      case 'FAIR': return 'Correct';
      case 'POOR': return 'Mauvais';
      case 'NOT_FUNCTIONAL': return 'Non fonctionnel';
      default: return 'Non défini';
    }
  };

  const getCommissioningStatusLabel = (status: CommissioningTestStatus | undefined) => {
    switch (status) {
      case 'NOT_STARTED': return 'Non commencé';
      case 'IN_PROGRESS': return 'En cours';
      case 'PASSED_WITH_RESERVES': return 'Réussi avec réserves';
      case 'PASSED_WITHOUT_RESERVES': return 'Réussi sans réserves';
      case 'FAILED': return 'Échoué';
      default: return 'Non défini';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div>
            <h1 className="text-2xl font-bold">{installation.name}</h1>
            <p className="text-muted-foreground">{installation.site_location}</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <InstallationStatusBadge status={installation.status} />
          <ProductTypeBadge productType={installation.product_type} />
        </div>
      </div>

      {/* Progress Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <TrendingUp className="h-5 w-5" />
            <span>Progression Globale</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Avancement</span>
              <span className="text-2xl font-bold">{Math.round(progress)}%</span>
            </div>
            <Progress value={progress} className="h-3" />
            <div className="flex items-center justify-between text-sm text-muted-foreground">
              <span>Dernière mise à jour: {latestTracking?.updated_at ? formatDate(latestTracking.updated_at) : 'Jamais'}</span>
              <span>Chef d'équipe: {installation.team_leader ? `${installation.team_leader.firstName} ${installation.team_leader.lastName}` : 'Non assigné'}</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Aperçu</TabsTrigger>
          <TabsTrigger value="progress">Progression</TabsTrigger>
          <TabsTrigger value="technical">Technique</TabsTrigger>
          <TabsTrigger value="documents">Documents</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Informations Générales */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Building className="h-5 w-5" />
                  <span>Informations Générales</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Type de Produit</label>
                    <p className="font-medium">{installation.product_type}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Statut</label>
                    <div className="mt-1">
                      <InstallationStatusBadge status={installation.status} />
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Date de Début Prévue</label>
                    <p className="font-medium">{formatDate(installation.planned_start_date)}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Date de Fin Prévue</label>
                    <p className="font-medium">{formatDate(installation.planned_end_date)}</p>
                  </div>
                </div>
                <Separator />
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Localisation</label>
                  <div className="flex items-center space-x-2 mt-1">
                    <MapPin className="h-4 w-4 text-muted-foreground" />
                    <p className="font-medium">{installation.site_location}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Informations Client */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <User className="h-5 w-5" />
                  <span>Informations Client</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {installation.client ? (
                  <>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Nom du Client</label>
                      <p className="font-medium">{installation.client.name}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Type de Client</label>
                      <p className="font-medium">
                        {installation.client.type === 'INDIVIDUAL' ? 'Particulier' :
                         installation.client.type === 'INSTITUTION' ? 'Institution' :
                         installation.client.type}
                      </p>
                    </div>
                    {installation.client.contact_info?.email && (
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">Email</label>
                        <div className="flex items-center space-x-2 mt-1">
                          <Mail className="h-4 w-4 text-muted-foreground" />
                          <p className="font-medium">{installation.client.contact_info.email}</p>
                        </div>
                      </div>
                    )}
                    {installation.client.contact_info?.phone && (
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">Téléphone</label>
                        <div className="flex items-center space-x-2 mt-1">
                          <Phone className="h-4 w-4 text-muted-foreground" />
                          <p className="font-medium">{installation.client.contact_info.phone}</p>
                        </div>
                      </div>
                    )}
                    {installation.client.address && (
                      <div>
                        <label className="text-sm font-medium text-muted-foreground">Adresse</label>
                        <div className="flex items-center space-x-2 mt-1">
                          <MapPin className="h-4 w-4 text-muted-foreground" />
                          <p className="font-medium">{installation.client.address}</p>
                        </div>
                      </div>
                    )}
                  </>
                ) : (
                  <p className="text-muted-foreground">Aucune information client disponible</p>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Chef d'Équipe et Membres */}
          {(installation.team_leader || installation.team_members?.length > 0) && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Settings className="h-5 w-5" />
                  <span>Équipe Assignée</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {installation.team_leader && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Chef d'Équipe</label>
                    <div className="flex items-center space-x-2 mt-1">
                      <User className="h-4 w-4 text-muted-foreground" />
                      <p className="font-medium">
                        {installation.team_leader.firstName} {installation.team_leader.lastName}
                      </p>
                    </div>
                    {installation.team_leader.position && (
                      <p className="text-sm text-muted-foreground ml-6">{installation.team_leader.position}</p>
                    )}
                    {installation.team_leader.email && (
                      <div className="flex items-center space-x-2 mt-1 ml-6">
                        <Mail className="h-3 w-3 text-muted-foreground" />
                        <p className="text-sm">{installation.team_leader.email}</p>
                      </div>
                    )}
                  </div>
                )}
                {installation.team_members?.length > 0 && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Membres de l'Équipe</label>
                    <p className="text-sm text-muted-foreground mt-1">
                      {installation.team_members.length} membre(s) assigné(s)
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="progress" className="space-y-6">
          {/* Progression Détaillée */}
          <Card>
            <CardHeader>
              <CardTitle>Progression Détaillée</CardTitle>
            </CardHeader>
            <CardContent>
              {latestTracking ? (
                <div className="space-y-6">
                  {/* Progression Commune */}
                  <div>
                    <h4 className="font-medium mb-4">Progression Générale</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <div className="flex justify-between items-center mb-2">
                          <span className="text-sm font-medium">Dossier d'exécution</span>
                          <span className="text-sm font-bold">{latestTracking.execution_file_progress || 0}%</span>
                        </div>
                        <Progress value={latestTracking.execution_file_progress || 0} className="h-2" />
                      </div>
                      <div>
                        <div className="flex justify-between items-center mb-2">
                          <span className="text-sm font-medium">Progression globale</span>
                          <span className="text-sm font-bold">{latestTracking.global_progress || 0}%</span>
                        </div>
                        <Progress value={latestTracking.global_progress || 0} className="h-2" />
                      </div>
                    </div>
                  </div>

                  <Separator />

                  {/* Progression Spécifique selon le Type */}
                  {installation.product_type === 'KYA-SoP' ? (
                    <div>
                      <h4 className="font-medium mb-4">Progression KYA-SoP</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="space-y-4">
                          <div>
                            <div className="flex justify-between items-center mb-2">
                              <span className="text-sm font-medium">Travaux de chaudronnerie</span>
                              <span className="text-sm font-bold">{latestTracking.metalwork_progress || 0}%</span>
                            </div>
                            <Progress value={latestTracking.metalwork_progress || 0} className="h-2" />
                          </div>
                          <div>
                            <div className="flex justify-between items-center mb-2">
                              <span className="text-sm font-medium">Fouilles</span>
                              <span className="text-sm font-bold">{latestTracking.excavation_progress || 0}%</span>
                            </div>
                            <Progress value={latestTracking.excavation_progress || 0} className="h-2" />
                          </div>
                          <div>
                            <div className="flex justify-between items-center mb-2">
                              <span className="text-sm font-medium">Pose des supports PV</span>
                              <span className="text-sm font-bold">{latestTracking.pv_supports_progress || 0}%</span>
                            </div>
                            <Progress value={latestTracking.pv_supports_progress || 0} className="h-2" />
                          </div>
                          <div>
                            <div className="flex justify-between items-center mb-2">
                              <span className="text-sm font-medium">Câblage des modules</span>
                              <span className="text-sm font-bold">{latestTracking.modules_wiring_progress || 0}%</span>
                            </div>
                            <Progress value={latestTracking.modules_wiring_progress || 0} className="h-2" />
                          </div>
                          <div>
                            <div className="flex justify-between items-center mb-2">
                              <span className="text-sm font-medium">Câbles PV-Onduleur</span>
                              <span className="text-sm font-bold">{latestTracking.pv_inverter_cables_progress || 0}%</span>
                            </div>
                            <Progress value={latestTracking.pv_inverter_cables_progress || 0} className="h-2" />
                          </div>
                          <div>
                            <div className="flex justify-between items-center mb-2">
                              <span className="text-sm font-medium">Câblage des onduleurs</span>
                              <span className="text-sm font-bold">{latestTracking.inverters_wiring_progress || 0}%</span>
                            </div>
                            <Progress value={latestTracking.inverters_wiring_progress || 0} className="h-2" />
                          </div>
                        </div>
                        <div className="space-y-4">
                          <div>
                            <div className="flex justify-between items-center mb-2">
                              <span className="text-sm font-medium">Câblage des batteries</span>
                              <span className="text-sm font-bold">{latestTracking.batteries_wiring_progress || 0}%</span>
                            </div>
                            <Progress value={latestTracking.batteries_wiring_progress || 0} className="h-2" />
                          </div>
                          <div>
                            <div className="flex justify-between items-center mb-2">
                              <span className="text-sm font-medium">Boîtiers AC/DC</span>
                              <span className="text-sm font-bold">{latestTracking.ac_dc_boxes_progress || 0}%</span>
                            </div>
                            <Progress value={latestTracking.ac_dc_boxes_progress || 0} className="h-2" />
                          </div>
                          <div>
                            <div className="flex justify-between items-center mb-2">
                              <span className="text-sm font-medium">Séparation des charges</span>
                              <span className="text-sm font-bold">{latestTracking.load_separation_progress || 0}%</span>
                            </div>
                            <Progress value={latestTracking.load_separation_progress || 0} className="h-2" />
                          </div>
                          <div>
                            <div className="flex justify-between items-center mb-2">
                              <span className="text-sm font-medium">Connexion batterie-onduleur</span>
                              <span className="text-sm font-bold">{latestTracking.battery_inverter_connection_progress || 0}%</span>
                            </div>
                            <Progress value={latestTracking.battery_inverter_connection_progress || 0} className="h-2" />
                          </div>
                          <div>
                            <div className="flex justify-between items-center mb-2">
                              <span className="text-sm font-medium">Mise à la terre</span>
                              <span className="text-sm font-bold">{latestTracking.grounding_progress || 0}%</span>
                            </div>
                            <Progress value={latestTracking.grounding_progress || 0} className="h-2" />
                          </div>
                        </div>
                      </div>
                    </div>
                  ) : installation.product_type === 'Lampadaire' ? (
                    <div>
                      <h4 className="font-medium mb-4">Progression Lampadaire</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="space-y-4">
                          <div>
                            <div className="flex justify-between items-center mb-2">
                              <span className="text-sm font-medium">Travaux de chaudronnerie</span>
                              <span className="text-sm font-bold">{latestTracking.metalwork_progress || 0}%</span>
                            </div>
                            <Progress value={latestTracking.metalwork_progress || 0} className="h-2" />
                          </div>
                          <div>
                            <div className="flex justify-between items-center mb-2">
                              <span className="text-sm font-medium">Fouilles</span>
                              <span className="text-sm font-bold">{latestTracking.excavation_progress || 0}%</span>
                            </div>
                            <Progress value={latestTracking.excavation_progress || 0} className="h-2" />
                          </div>
                        </div>
                        <div className="space-y-4">
                          <div>
                            <div className="flex justify-between items-center mb-2">
                              <span className="text-sm font-medium">Installation du mât</span>
                              <span className="text-sm font-bold">{latestTracking.pole_installation_progress || 0}%</span>
                            </div>
                            <Progress value={latestTracking.pole_installation_progress || 0} className="h-2" />
                          </div>
                          <div>
                            <div className="flex justify-between items-center mb-2">
                              <span className="text-sm font-medium">Installation du luminaire</span>
                              <span className="text-sm font-bold">{latestTracking.lamp_installation_progress || 0}%</span>
                            </div>
                            <Progress value={latestTracking.lamp_installation_progress || 0} className="h-2" />
                          </div>
                        </div>
                      </div>
                    </div>
                  ) : null}

                  {/* Tests et Mise en Service */}
                  {latestTracking.commissioning_test_status && (
                    <>
                      <Separator />
                      <div>
                        <h4 className="font-medium mb-4">Tests et Mise en Service</h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <label className="text-sm font-medium text-muted-foreground">Statut des Tests</label>
                            <p className="font-medium">{getCommissioningStatusLabel(latestTracking.commissioning_test_status)}</p>
                          </div>
                          {latestTracking.commissioning_test_date && (
                            <div>
                              <label className="text-sm font-medium text-muted-foreground">Date des Tests</label>
                              <p className="font-medium">{formatDate(latestTracking.commissioning_test_date)}</p>
                            </div>
                          )}
                          {latestTracking.commissioning_test_notes && (
                            <div className="md:col-span-2">
                              <label className="text-sm font-medium text-muted-foreground">Notes des Tests</label>
                              <p className="font-medium">{latestTracking.commissioning_test_notes}</p>
                            </div>
                          )}
                        </div>
                      </div>
                    </>
                  )}
                </div>
              ) : (
                <p className="text-muted-foreground">Aucune donnée de progression disponible</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="technical" className="space-y-6">
          {/* Spécifications Techniques */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Zap className="h-5 w-5" />
                <span>Spécifications Techniques</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Type de Produit</label>
                  <p className="font-medium">{installation.product_type}</p>
                </div>
                {installation.peak_power && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Puissance Crête</label>
                    <p className="font-medium">{installation.peak_power}</p>
                  </div>
                )}
                {installation.inverter_specs && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Spécifications Onduleur</label>
                    <p className="font-medium">{installation.inverter_specs}</p>
                  </div>
                )}
                {installation.battery_capacity && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Capacité Batterie</label>
                    <p className="font-medium">{installation.battery_capacity}</p>
                  </div>
                )}
                {installation.equipment_description && (
                  <div className="md:col-span-2">
                    <label className="text-sm font-medium text-muted-foreground">Description Équipement</label>
                    <p className="font-medium">{installation.equipment_description}</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* État de Fonctionnement */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Settings className="h-5 w-5" />
                <span>État de Fonctionnement</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">État Général</label>
                  <p className="font-medium">{getFunctioningStateLabel(installation.general_functioning_state)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Tests de Mise en Service</label>
                  <p className="font-medium">{getCommissioningStatusLabel(latestTracking?.commissioning_test_status)}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Réceptions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <CheckCircle className="h-5 w-5" />
                <span>Réceptions</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* Réception Technique */}
                <div>
                  <h4 className="font-medium mb-3">Réception Technique</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Date</label>
                      <p className="font-medium">{formatDate(installation.technical_reception_date)}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Statut</label>
                      <p className="font-medium">{getReceptionStatusLabel(installation.technical_reception_status)}</p>
                    </div>
                    {installation.technical_reception_notes && (
                      <div className="md:col-span-2">
                        <label className="text-sm font-medium text-muted-foreground">Notes</label>
                        <p className="font-medium">{installation.technical_reception_notes}</p>
                      </div>
                    )}
                  </div>
                </div>

                <Separator />

                {/* Réception Provisoire */}
                <div>
                  <h4 className="font-medium mb-3">Réception Provisoire</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Date</label>
                      <p className="font-medium">{formatDate(installation.provisional_reception_date)}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">Statut</label>
                      <p className="font-medium">{getReceptionStatusLabel(installation.provisional_reception_status)}</p>
                    </div>
                    {installation.provisional_reception_notes && (
                      <div className="md:col-span-2">
                        <label className="text-sm font-medium text-muted-foreground">Notes</label>
                        <p className="font-medium">{installation.provisional_reception_notes}</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="documents" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <FileText className="h-5 w-5" />
                <span>Documents et Photos</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Camera className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <p className="text-muted-foreground mb-4">Aucun document disponible pour le moment</p>
                <Button variant="outline">
                  <Download className="h-4 w-4 mr-2" />
                  Télécharger les documents
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Actions */}
      <div className="flex justify-end space-x-2">
        <Button variant="outline">
          <Edit className="h-4 w-4 mr-2" />
          Modifier
        </Button>
        <Button>
          <FileText className="h-4 w-4 mr-2" />
          Générer Rapport
        </Button>
      </div>
    </div>
  );
}
