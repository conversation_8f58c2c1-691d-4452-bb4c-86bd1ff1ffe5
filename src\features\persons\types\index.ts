// Person Management Types
import type { Entity } from '@/types/entities';
import type { AuthUser } from '@/types/auth';

export interface Person {
  id: string;
  firstName: string;
  lastName: string;
  email?: string;
  phone?: string;
  employeeId?: string;
  position?: string;
  hireDate?: Date;
  isActive: boolean;
  metadata: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
  
  // Computed properties
  fullName: string;
  displayName: string;
  
  // Relations (populated when needed)
  entities?: PersonEntity[];
  authUser?: AuthUser;
  primaryEntity?: Entity;
}

export interface PersonEntity {
  id: string;
  entityId: string;
  personId: string;
  roleInEntity?: string;
  startDate: Date;
  endDate?: Date;
  isPrimary: boolean;
  createdAt: Date;
  updatedAt: Date;
  
  // Relations (populated when needed)
  entity?: Entity;
  person?: Person;
}

export interface PersonWithEntities extends Person {
  entities: PersonEntity[];
  primaryEntity?: Entity;
}

// Form and Input Types
export interface CreatePersonData {
  firstName: string;
  lastName: string;
  email?: string;
  phone?: string;
  employeeId?: string;
  position?: string;
  hireDate?: Date;
  metadata?: Record<string, any>;
  entities?: CreatePersonEntityData[];
}

export interface UpdatePersonData {
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  employeeId?: string;
  position?: string;
  hireDate?: Date;
  isActive?: boolean;
  metadata?: Record<string, any>;
}

export interface CreatePersonEntityData {
  entityId: string;
  roleInEntity?: string;
  startDate?: Date;
  isPrimary?: boolean;
}

export interface UpdatePersonEntityData extends CreatePersonEntityData {
  id?: string;
  endDate?: Date;
}

// Filter and Search Types
export interface PersonFilters {
  search?: string;
  isActive?: boolean;
  entityId?: string;
  position?: string;
  hasEmail?: boolean;
  hiredAfter?: Date;
  hiredBefore?: Date;
}

export interface PersonSortOptions {
  field: 'firstName' | 'lastName' | 'email' | 'employeeId' | 'position' | 'hireDate' | 'createdAt';
  direction: 'asc' | 'desc';
}

// API Response Types
export interface PersonsResponse {
  data: PersonWithEntities[];
  total: number;
  page: number;
  limit: number;
}

export interface PersonStatsResponse {
  total: number;
  active: number;
  inactive: number;
  withoutEmail: number;
  recentHires: number; // Last 30 days
}

// Import/Export Types
export interface PersonImportData {
  firstName: string;
  lastName: string;
  email?: string;
  phone?: string;
  employeeId?: string;
  position?: string;
  hireDate?: string; // ISO date string
  entityCode?: string; // For entity assignment
  roleInEntity?: string;
}

export interface PersonExportData extends PersonImportData {
  id: string;
  isActive: boolean;
  primaryEntityName?: string;
  entitiesCount: number;
  createdAt: string;
  updatedAt: string;
}
