'use client';

import { useState, type ReactNode } from 'react';
import { Header } from '@/features/dashboard/components/header';
import { PrimarySidebar } from '@/features/dashboard/components/primary-sidebar';
import { SecondarySidebar } from '@/features/dashboard/components/secondary-sidebar';
import { SidebarProvider, SidebarInset } from '@/components/ui/sidebar';
import { usePermissionStore } from '@/features/rbac/stores/permission-store';

interface DashboardClientProps {
  children: ReactNode;
}

export function DashboardClient({ children }: DashboardClientProps) {
  const [isSecondarySidebarOpen, setIsSecondarySidebarOpen] = useState(false);
  const permissions = usePermissionStore((state) => state.permissions);

  return (
    <SidebarProvider>
      <div className="flex h-screen w-full bg-gray-50 dark:bg-gray-950">
        <PrimarySidebar permissions={permissions} />
        {isSecondarySidebarOpen && <SecondarySidebar />}
        <SidebarInset>
          <Header
            isSecondarySidebarOpen={isSecondarySidebarOpen}
            toggleSecondarySidebar={() =>
              setIsSecondarySidebarOpen(!isSecondarySidebarOpen)
            }
          />
          <main className="flex-1 p-6">{children}</main>
        </SidebarInset>
      </div>
    </SidebarProvider>
  );
}