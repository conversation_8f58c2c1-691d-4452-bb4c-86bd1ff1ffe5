import { z } from 'zod';

// Base person schema
export const personSchema = z.object({
  firstName: z
    .string()
    .min(2, { message: "Le prénom doit contenir au moins 2 caractères." })
    .max(100, { message: "Le prénom ne peut pas dépasser 100 caractères." }),
  lastName: z
    .string()
    .min(2, { message: "Le nom doit contenir au moins 2 caractères." })
    .max(100, { message: "Le nom ne peut pas dépasser 100 caractères." }),
  email: z
    .string()
    .email({ message: "Veuillez entrer une adresse email valide." })
    .optional()
    .or(z.literal("")),
  phone: z
    .string()
    .max(20, { message: "Le numéro de téléphone ne peut pas dépasser 20 caractères." })
    .optional()
    .or(z.literal("")),
  employeeId: z
    .string()
    .max(50, { message: "Le matricule ne peut pas dépasser 50 caractères." })
    .optional()
    .or(z.literal("")),
  position: z
    .string()
    .max(100, { message: "Le poste ne peut pas dépasser 100 caractères." })
    .optional()
    .or(z.literal("")),
  hireDate: z
    .date({ message: "Veuillez entrer une date d'embauche valide." })
    .optional()
    .or(z.literal(null)),
  metadata: z
    .record(z.string(), z.any())
    .optional()
    .default({}),
});

// Person entity assignment schema
export const personEntitySchema = z.object({
  entityId: z
    .string()
    .min(1, { message: "Veuillez sélectionner une entité." }),
  roleInEntity: z
    .string()
    .max(100, { message: "Le rôle ne peut pas dépasser 100 caractères." })
    .optional(),
  startDate: z
    .date({ message: "Veuillez entrer une date de début valide." })
    .optional(),
  isPrimary: z
    .boolean()
    .optional(),
});

// Create person form schema
export const createPersonFormSchema = personSchema.extend({
  entities: z
    .array(personEntitySchema)
    .optional()
    .default([]),
});

// Update person form schema
export const updatePersonFormSchema = personSchema.partial().extend({
  isActive: z.boolean().optional(),
});

// Update person entity schema
export const updatePersonEntitySchema = personEntitySchema.extend({
  id: z.string().optional(),
  endDate: z
    .date({ message: "Veuillez entrer une date de fin valide." })
    .optional()
    .or(z.literal(null)),
});

// Person filters schema
export const personFiltersSchema = z.object({
  search: z.string().optional(),
  isActive: z.boolean().optional(),
  entityId: z.string().optional(),
  position: z.string().optional(),
  hasEmail: z.boolean().optional(),
  hiredAfter: z.date().optional(),
  hiredBefore: z.date().optional(),
});

// Person sort schema
export const personSortSchema = z.object({
  field: z.enum(['firstName', 'lastName', 'email', 'employeeId', 'position', 'hireDate', 'createdAt']),
  direction: z.enum(['asc', 'desc']),
});

// Pagination schema
export const paginationSchema = z.object({
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(100).default(20),
});

// Person import schema
export const personImportSchema = z.object({
  firstName: z.string().min(2).max(100),
  lastName: z.string().min(2).max(100),
  email: z.string().email().optional().or(z.literal("")),
  phone: z.string().max(20).optional().or(z.literal("")),
  employeeId: z.string().max(50).optional().or(z.literal("")),
  position: z.string().max(100).optional().or(z.literal("")),
  hireDate: z.string().optional().or(z.literal("")), // ISO date string
  entityCode: z.string().optional().or(z.literal("")),
  roleInEntity: z.string().max(100).optional().or(z.literal("")),
});

// Bulk operations schema
export const bulkPersonOperationSchema = z.object({
  operation: z.enum(['activate', 'deactivate', 'delete', 'assign_entity', 'remove_entity']),
  personIds: z.array(z.string()).min(1, { message: "Veuillez sélectionner au moins une personne." }),
  entityId: z.string().optional(), // For assign/remove entity operations
  roleInEntity: z.string().optional(), // For assign entity operation
});

// Export types for use in components
export type PersonFormValues = z.infer<typeof createPersonFormSchema>;
export type UpdatePersonFormValues = z.infer<typeof updatePersonFormSchema>;
export type PersonEntityFormValues = z.infer<typeof personEntitySchema>;
export type UpdatePersonEntityFormValues = z.infer<typeof updatePersonEntitySchema>;
export type PersonFiltersValues = z.infer<typeof personFiltersSchema>;
export type PersonSortValues = z.infer<typeof personSortSchema>;
export type PaginationValues = z.infer<typeof paginationSchema>;
export type PersonImportValues = z.infer<typeof personImportSchema>;
export type BulkPersonOperationValues = z.infer<typeof bulkPersonOperationSchema>;
