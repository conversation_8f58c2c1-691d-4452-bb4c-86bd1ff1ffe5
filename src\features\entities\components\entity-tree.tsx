'use client';

import { useEffect, useState } from 'react';
import { getHierarchyAction } from '../actions';
import type { Entity } from '@/types/entities';
import { Button } from '@/components/ui/button';
import { PlusCircle, Edit, Trash2 } from 'lucide-react';
import { ManageEntityDialog } from './manage-entity-dialog';
import { DeleteEntityAlert } from './delete-entity-alert';

function EntityNodeView({
  node,
  level = 0,
  onEdit,
  onDelete,
}: {
  node: Entity;
  level?: number;
  onEdit: (entity: Entity) => void;
  onDelete: (entityId: string) => void;
}) {
  return (
    <div>
      <div
        className="flex items-center justify-between p-2 rounded-md hover:bg-gray-100"
        style={{ paddingLeft: `${level * 1.5 + 0.5}rem` }}
      >
        <div>
          <p className="font-medium">{node.name}</p>
          <p className="text-sm text-muted-foreground">{node.type}</p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="icon" className="h-8 w-8" onClick={() => onEdit(node)}>
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8 text-destructive hover:text-destructive"
            onClick={() => onDelete(node.id)}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </div>
      {node.children && node.children.length > 0 && (
        <div>
          {node.children.map((childNode: Entity) => (
            <EntityNodeView
              key={childNode.id}
              node={childNode}
              level={level + 1}
              onEdit={onEdit}
              onDelete={onDelete}
            />
          ))}
        </div>
      )}
    </div>
  );
}

export function EntityTree() {
  const [hierarchy, setHierarchy] = useState<Entity[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isManageDialogOpen, setIsManageDialogOpen] = useState(false);
  const [editingEntity, setEditingEntity] = useState<Entity | undefined>(undefined);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [deletingEntityId, setDeletingEntityId] = useState<string | undefined>(undefined);

  const loadHierarchy = async () => {
    setIsLoading(true);
    const result = await getHierarchyAction();
    if (result.data) {
      setHierarchy(result.data);
    } else {
      setError(result.error || 'Failed to load hierarchy');
    }
    setIsLoading(false);
  };

  useEffect(() => {
    loadHierarchy();
  }, []);

  const handleEdit = (entity: Entity) => {
    setEditingEntity(entity);
    setIsManageDialogOpen(true);
  };

  const handleDelete = (entityId: string) => {
    setDeletingEntityId(entityId);
    setIsDeleteDialogOpen(true);
  };

  const handleManageDialogClose = () => {
    setIsManageDialogOpen(false);
    setEditingEntity(undefined);
  };

  const handleDeleteDialogClose = () => {
    setIsDeleteDialogOpen(false);
    setDeletingEntityId(undefined);
  };

  const handleSuccess = () => {
    loadHierarchy();
  };

  if (isLoading) return <p>Chargement de la hiérarchie...</p>;
  if (error) return <p className="text-destructive">Erreur: {error}</p>;

  return (
    <div className="bg-white p-4 rounded-md border">
       <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold">Hiérarchie de l'organisation</h3>
        <Button size="sm" onClick={() => setIsManageDialogOpen(true)}>
          <PlusCircle className="mr-2 h-4 w-4" />
          Ajouter une entité
        </Button>
      </div>
      <div className="space-y-2">
        {hierarchy.map(node => (
          <EntityNodeView key={node.id} node={node} onEdit={handleEdit} onDelete={handleDelete} />
        ))}
      </div>
      <ManageEntityDialog
        isOpen={isManageDialogOpen}
        onClose={handleManageDialogClose}
        onSuccess={handleSuccess}
        entities={hierarchy}
        entity={editingEntity}
      />
      {deletingEntityId && (
        <DeleteEntityAlert
          isOpen={isDeleteDialogOpen}
          onClose={handleDeleteDialogClose}
          onSuccess={handleSuccess}
          entityId={deletingEntityId}
        />
      )}
    </div>
  );
}