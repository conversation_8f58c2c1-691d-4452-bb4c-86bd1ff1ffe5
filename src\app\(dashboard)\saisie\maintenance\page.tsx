// Page de Maintenance - KYA Dashboards
'use client';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Wrench, 
  Calendar, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  Plus,
  Settings,
  FileText,
  Users,
  MapPin
} from 'lucide-react';

export default function MaintenancePage() {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-gradient-to-r from-orange-500 to-red-600 rounded-lg">
              <Wrench className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Maintenance</h1>
              <p className="text-muted-foreground">
                Gestion de la maintenance préventive et corrective
              </p>
            </div>
          </div>
          <Button className="bg-gradient-to-r from-orange-500 to-red-600 hover:from-orange-600 hover:to-red-700">
            <Plus className="h-4 w-4 mr-2" />
            Nouvelle Intervention
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-red-100 rounded-lg">
                <AlertTriangle className="h-5 w-5 text-red-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Urgentes</p>
                <p className="text-2xl font-bold">3</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-orange-100 rounded-lg">
                <Clock className="h-5 w-5 text-orange-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">En Cours</p>
                <p className="text-2xl font-bold">7</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Calendar className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Planifiées</p>
                <p className="text-2xl font-bold">12</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <CheckCircle className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Terminées (ce mois)</p>
                <p className="text-2xl font-bold">28</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Module en développement */}
      <Card className="border-dashed border-2 border-orange-200">
        <CardContent className="p-12 text-center">
          <div className="flex flex-col items-center gap-4">
            <div className="p-4 bg-orange-100 rounded-full">
              <Wrench className="h-12 w-12 text-orange-600" />
            </div>
            <div className="space-y-2">
              <h3 className="text-2xl font-bold">Module Maintenance</h3>
              <p className="text-muted-foreground max-w-md">
                Le module de gestion de la maintenance est actuellement en développement. 
                Il permettra de gérer la maintenance préventive et corrective de vos installations.
              </p>
            </div>
            <Badge variant="secondary" className="bg-orange-100 text-orange-700">
              En Développement
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Fonctionnalités prévues */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Maintenance Préventive
            </CardTitle>
            <CardDescription>
              Planification et suivi des maintenances préventives
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2 text-sm">
              <li className="flex items-center gap-2">
                <div className="w-1.5 h-1.5 bg-blue-500 rounded-full" />
                Calendrier de maintenance
              </li>
              <li className="flex items-center gap-2">
                <div className="w-1.5 h-1.5 bg-blue-500 rounded-full" />
                Rappels automatiques
              </li>
              <li className="flex items-center gap-2">
                <div className="w-1.5 h-1.5 bg-blue-500 rounded-full" />
                Checklists de contrôle
              </li>
              <li className="flex items-center gap-2">
                <div className="w-1.5 h-1.5 bg-blue-500 rounded-full" />
                Historique des interventions
              </li>
            </ul>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5" />
              Maintenance Corrective
            </CardTitle>
            <CardDescription>
              Gestion des pannes et interventions d'urgence
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2 text-sm">
              <li className="flex items-center gap-2">
                <div className="w-1.5 h-1.5 bg-red-500 rounded-full" />
                Signalement de pannes
              </li>
              <li className="flex items-center gap-2">
                <div className="w-1.5 h-1.5 bg-red-500 rounded-full" />
                Priorisation des interventions
              </li>
              <li className="flex items-center gap-2">
                <div className="w-1.5 h-1.5 bg-red-500 rounded-full" />
                Suivi en temps réel
              </li>
              <li className="flex items-center gap-2">
                <div className="w-1.5 h-1.5 bg-red-500 rounded-full" />
                Rapports d'intervention
              </li>
            </ul>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Gestion des Équipes
            </CardTitle>
            <CardDescription>
              Organisation des équipes de maintenance
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2 text-sm">
              <li className="flex items-center gap-2">
                <div className="w-1.5 h-1.5 bg-green-500 rounded-full" />
                Affectation des techniciens
              </li>
              <li className="flex items-center gap-2">
                <div className="w-1.5 h-1.5 bg-green-500 rounded-full" />
                Compétences et certifications
              </li>
              <li className="flex items-center gap-2">
                <div className="w-1.5 h-1.5 bg-green-500 rounded-full" />
                Planning des interventions
              </li>
              <li className="flex items-center gap-2">
                <div className="w-1.5 h-1.5 bg-green-500 rounded-full" />
                Suivi des performances
              </li>
            </ul>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Documentation
            </CardTitle>
            <CardDescription>
              Gestion de la documentation technique
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2 text-sm">
              <li className="flex items-center gap-2">
                <div className="w-1.5 h-1.5 bg-purple-500 rounded-full" />
                Manuels techniques
              </li>
              <li className="flex items-center gap-2">
                <div className="w-1.5 h-1.5 bg-purple-500 rounded-full" />
                Procédures de maintenance
              </li>
              <li className="flex items-center gap-2">
                <div className="w-1.5 h-1.5 bg-purple-500 rounded-full" />
                Schémas et plans
              </li>
              <li className="flex items-center gap-2">
                <div className="w-1.5 h-1.5 bg-purple-500 rounded-full" />
                Base de connaissances
              </li>
            </ul>
          </CardContent>
        </Card>
      </div>

      {/* Timeline de développement */}
      <Card>
        <CardHeader>
          <CardTitle>Roadmap de Développement</CardTitle>
          <CardDescription>
            Planning prévisionnel pour le module de maintenance
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center gap-4">
              <div className="w-3 h-3 bg-blue-500 rounded-full" />
              <div className="flex-1">
                <p className="font-medium">Phase 1 - Maintenance Préventive</p>
                <p className="text-sm text-muted-foreground">Calendrier et planification des maintenances</p>
              </div>
              <Badge variant="secondary" className="bg-blue-100 text-blue-700">
                Q1 2024
              </Badge>
            </div>
            
            <div className="flex items-center gap-4">
              <div className="w-3 h-3 bg-orange-500 rounded-full" />
              <div className="flex-1">
                <p className="font-medium">Phase 2 - Maintenance Corrective</p>
                <p className="text-sm text-muted-foreground">Gestion des pannes et interventions d'urgence</p>
              </div>
              <Badge variant="secondary" className="bg-orange-100 text-orange-700">
                Q2 2024
              </Badge>
            </div>
            
            <div className="flex items-center gap-4">
              <div className="w-3 h-3 bg-green-500 rounded-full" />
              <div className="flex-1">
                <p className="font-medium">Phase 3 - Gestion des Équipes</p>
                <p className="text-sm text-muted-foreground">Organisation et suivi des équipes techniques</p>
              </div>
              <Badge variant="secondary" className="bg-green-100 text-green-700">
                Q3 2024
              </Badge>
            </div>
            
            <div className="flex items-center gap-4">
              <div className="w-3 h-3 bg-purple-500 rounded-full" />
              <div className="flex-1">
                <p className="font-medium">Phase 4 - Documentation et Reporting</p>
                <p className="text-sm text-muted-foreground">Système complet de documentation et rapports</p>
              </div>
              <Badge variant="secondary" className="bg-purple-100 text-purple-700">
                Q4 2024
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
