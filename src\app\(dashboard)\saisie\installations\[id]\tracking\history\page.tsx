import { Suspense } from 'react';
import { notFound } from 'next/navigation';
import Link from 'next/link';
import { ArrowLeft, History, TrendingUp } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

import { InstallationServerService } from '@/features/installations/services/installation-server-service';
import { TrackingHistoryExpandableTable } from '@/features/installations/components/tracking-history-expandable-table';

interface TrackingHistoryPageProps {
  params: {
    id: string;
  };
}

// Composant pour afficher l'historique des suivis sous forme de tableau expandable
async function TrackingHistoryTable({ installationId }: { installationId: string }) {
  try {
    // Récupérer les vraies données d'historique
    const trackingHistory = await InstallationServerService.getTrackingHistory(installationId);
    const installation = await InstallationServerService.getInstallation(installationId);

    if (!installation) {
      return (
        <Card className="kpi-card shadow-kya-card">
          <CardContent className="text-center py-12">
            <div className="text-red-500 mb-4">
              <TrendingUp className="h-12 w-12 mx-auto" />
            </div>
            <h3 className="text-lg font-semibold mb-2">Installation Introuvable</h3>
            <p className="text-muted-foreground">
              Impossible de charger les informations de l'installation.
            </p>
          </CardContent>
        </Card>
      );
    }

    return (
      <TrackingHistoryExpandableTable
        trackingHistory={trackingHistory || []}
        installation={installation}
      />
    );
  } catch (error) {
    console.error('Error loading tracking history:', error);
    return (
      <Card className="kpi-card shadow-kya-card">
        <CardContent className="text-center py-12">
          <div className="text-red-500 mb-4">
            <TrendingUp className="h-12 w-12 mx-auto" />
          </div>
          <h3 className="text-lg font-semibold mb-2">Erreur de Chargement</h3>
          <p className="text-muted-foreground">
            Impossible de charger l'historique des suivis.
          </p>
        </CardContent>
      </Card>
    );
  }
}



export default async function TrackingHistoryPage({ params }: TrackingHistoryPageProps) {
  try {
    const { id } = await params;
    const installation = await InstallationServerService.getInstallation(id);
    
    if (!installation) {
      notFound();
    }

    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100">
        <div className="container mx-auto px-4 py-8">
          {/* Header avec navigation */}
          <div className="mb-8">
            <div className="flex items-center gap-4 mb-4">
              <Button asChild variant="ghost" size="sm">
                <Link href="/saisie/installations" className="flex items-center gap-2">
                  <ArrowLeft className="h-4 w-4" />
                  Retour aux Installations
                </Link>
              </Button>
            </div>
            
            <div className="flex items-center gap-3 mb-2">
              <div className="p-2 gradient-kya-primary rounded-lg shadow-kya-card">
                <History className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-kya-primary">
                  Historique des suivis
                </h1>
                <p className="text-muted-foreground">
                  Installation {installation.installation_number}
                </p>
              </div>
            </div>
            <div className="h-1 w-20 gradient-kya-primary rounded-full"></div>
          </div>



          {/* Tableau de l'historique */}
          <Suspense fallback={<div>Chargement de l'historique...</div>}>
            <TrackingHistoryTable installationId={id} />
          </Suspense>
        </div>
      </div>
    );
  } catch (error) {
    console.error('Error loading tracking history page:', error);
    notFound();
  }
}
