// Installation Hooks - KYA Dashboards
'use client';

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { InstallationService } from '../services/installation-service';
import { createClient } from '@/utils/supabase/client';
import type {
  Installation,
  InstallationTracking,
  CreateInstallationData,
  UpdateInstallationData,
  InstallationTrackingData,
  InstallationSearchParams,
  InstallationKPIs
} from '../types';

// Hook for fetching installations list
export function useInstallations(params: InstallationSearchParams = {}, disabled = false) {
  return useQuery({
    queryKey: ['installations', params],
    queryFn: () => InstallationService.getInstallations(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    enabled: !disabled,
  });
}

// Hook for fetching single installation
export function useInstallation(id: string) {
  return useQuery({
    queryKey: ['installation', id],
    queryFn: () => InstallationService.getInstallation(id),
    enabled: !!id,
  });
}

// Hook for installation CRUD mutations
export function useInstallationMutations() {
  const queryClient = useQueryClient();

  const createMutation = useMutation({
    mutationFn: ({ data, userId }: { data: CreateInstallationData; userId: string }) =>
      InstallationService.createInstallation(data, userId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['installations'] });
      queryClient.invalidateQueries({ queryKey: ['installation-stats'] });
    },
  });

  const updateMutation = useMutation({
    mutationFn: ({ id, data, userId }: { id: string; data: UpdateInstallationData; userId: string }) =>
      InstallationService.updateInstallation(id, data, userId),
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ['installation', id] });
      queryClient.invalidateQueries({ queryKey: ['installations'] });
      queryClient.invalidateQueries({ queryKey: ['installation-stats'] });
    },
  });

  const deleteMutation = useMutation({
    mutationFn: (id: string) => InstallationService.deleteInstallation(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['installations'] });
      queryClient.invalidateQueries({ queryKey: ['installation-stats'] });
    },
  });

  return {
    create: createMutation,
    update: updateMutation,
    delete: deleteMutation,
  };
}

// Hook for installation tracking
export function useInstallationTracking(installationId: string) {
  const queryClient = useQueryClient();

  const todayTracking = useQuery({
    queryKey: ['installation-tracking', installationId, 'today'],
    queryFn: () => InstallationService.getTodayTracking(installationId),
    enabled: !!installationId,
  });

  const trackingHistory = useQuery({
    queryKey: ['installation-tracking', installationId, 'history'],
    queryFn: () => InstallationService.getTrackingHistory(installationId),
    enabled: !!installationId,
  });

  const updateTrackingMutation = useMutation({
    mutationFn: ({ data, userId }: { data: InstallationTrackingData; userId: string }) =>
      InstallationService.updateDailyTracking(installationId, data, userId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['installation-tracking', installationId] });
      queryClient.invalidateQueries({ queryKey: ['installation', installationId] });
      queryClient.invalidateQueries({ queryKey: ['installations'] });
      queryClient.invalidateQueries({ queryKey: ['installation-stats'] });
    },
  });

  return {
    todayTracking,
    trackingHistory,
    updateTracking: updateTrackingMutation,
    isUpdating: updateTrackingMutation.isPending,
  };
}

// Hook for installation statistics and KPIs
export function useInstallationStats(teamLeaderId?: string, entityId?: string) {
  return useQuery({
    queryKey: ['installation-stats', teamLeaderId, entityId],
    queryFn: () => InstallationService.getInstallationStats(teamLeaderId, entityId),
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchInterval: 5 * 60 * 1000, // Refresh every 5 minutes
  });
}

// Hook for installation KPIs (dashboard data)
export function useInstallationKPIs(teamLeaderId?: string, entityId?: string) {
  return useQuery({
    queryKey: ['installation-kpis', teamLeaderId, entityId],
    queryFn: () => InstallationService.getInstallationKPIs(teamLeaderId, entityId),
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchInterval: 5 * 60 * 1000, // Refresh every 5 minutes
  });
}

// Hook for generating installation number
export function useGenerateInstallationNumber() {
  return useQuery({
    queryKey: ['installation-number', 'generate'],
    queryFn: () => InstallationService.generateInstallationNumber(),
    enabled: false, // Only fetch when explicitly called
  });
}

// Hook for real-time installation updates (if needed)
export function useInstallationRealtime(installationId: string) {
  const queryClient = useQueryClient();

  // This could be extended with Supabase real-time subscriptions
  // For now, we'll use polling for critical installations
  return useQuery({
    queryKey: ['installation-realtime', installationId],
    queryFn: () => InstallationService.getInstallation(installationId),
    enabled: !!installationId,
    refetchInterval: 30 * 1000, // Refresh every 30 seconds for active installations
    refetchIntervalInBackground: false,
  });
}

// Custom hook for installation form logic
export function useInstallationForm(installation?: Installation) {
  const { create, update } = useInstallationMutations();
  const { refetch: generateNumber } = useGenerateInstallationNumber();

  const handleGenerateNumber = async () => {
    const { data } = await generateNumber();
    return data;
  };

  const handleSubmit = async (data: CreateInstallationData | UpdateInstallationData, userId: string) => {
    if (installation) {
      return update.mutateAsync({ 
        id: installation.id, 
        data: data as UpdateInstallationData, 
        userId 
      });
    } else {
      return create.mutateAsync({ 
        data: data as CreateInstallationData, 
        userId 
      });
    }
  };

  return {
    handleSubmit,
    handleGenerateNumber,
    isSubmitting: create.isPending || update.isPending,
    error: create.error || update.error,
  };
}

// Hook for installation filters and search
export function useInstallationFilters() {
  // This could be extended with URL state management
  // For now, we'll use local state
  return {
    // Implementation would depend on your state management preference
    // Could use zustand, URL params, or local state
  };
}

// Hook for fetching clients
export function useClients() {
  return useQuery({
    queryKey: ['clients'],
    queryFn: async () => {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('clients')
        .select('*')
        .order('name');

      if (error) throw error;
      return data || [];
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Hook for fetching projects
export function useProjects() {
  return useQuery({
    queryKey: ['projects'],
    queryFn: async () => {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('projects')
        .select('*')
        .order('name');

      if (error) throw error;
      return data || [];
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}
