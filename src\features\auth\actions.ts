'use server';

import { createClient } from '@/utils/supabase/server';
import { revalidatePath } from 'next/cache';
import { redirect } from 'next/navigation';
import { z } from 'zod';
import { loginSchema, signupSchema } from './utils/validation';
import { AuditService } from '@/utils/services/audit-service';

export async function signIn(credentials: z.infer<typeof loginSchema>) {
  const supabase = await createClient();

  const validatedCredentials = loginSchema.safeParse(credentials);
  if (!validatedCredentials.success) {
    return { error: 'Invalid credentials provided.' };
  }

  const { email, password } = validatedCredentials.data;

  const { error: signInError } = await supabase.auth.signInWithPassword({
    email,
    password,
  });

  if (signInError) {
    console.error('Sign-in error:', signInError.message);
    return { error: 'Could not authenticate user.' };
  }

  const { data: { user } } = await supabase.auth.getUser();
  if (!user) {
    return { error: 'Failed to retrieve user after sign-in.' };
  }

  try {
    const { data: authUser } = await supabase
      .from('auth_users')
      .select('id, login_count')
      .eq('supabase_id', user.id)
      .single();

    if (authUser) {
      const newLoginCount = (authUser.login_count || 0) + 1;
      await supabase
        .from('auth_users')
        .update({
          last_login_at: new Date().toISOString(),
          login_count: newLoginCount,
        })
        .eq('id', authUser.id);
      await AuditService.log('LOGIN', 'AUTH', authUser.id, { userId: authUser.id });
    }
  } catch (error: any) {
    console.error('Error during user sync after login:', error.message);
  }

  revalidatePath('/', 'layout');
  redirect('/dashboard');
}

export async function signOut() {
  const supabase = await createClient();
  const { data: { user } } = await supabase.auth.getUser();
  
  if (user) {
      const { data: authUser } = await supabase
        .from('auth_users')
        .select('id')
        .eq('supabase_id', user.id)
        .single();
      
      if (authUser) {
        await AuditService.log('LOGOUT', 'AUTH', authUser.id, { userId: authUser.id });
      }
  }
  
  await supabase.auth.signOut();
  redirect('/login');
}

export async function signUp(values: z.infer<typeof signupSchema>) {
  const supabase = await createClient();
  const validatedFields = signupSchema.safeParse(values);

  if (!validatedFields.success) {
    return { error: 'Champs invalides.' };
  }

  const { email, password, roleId } = validatedFields.data;

  // 1. Create Supabase auth user
  const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
    email,
    password,
  });

  if (signUpError) {
    return { error: `La création du compte a échoué: ${signUpError.message}` };
  }
  if (!signUpData.user) {
    return { error: 'Aucun utilisateur retourné après la création.' };
  }

  const supabaseUserId = signUpData.user.id;

  // 2. Create a corresponding record in our public `auth_users` table
  const { data: authUser, error: authUserError } = await supabase
    .from('auth_users')
    .insert({
      supabase_id: supabaseUserId,
      email: email,
    })
    .select('id')
    .single();
  
  if (authUserError || !authUser) {
    return { error: `Erreur lors de la synchronisation de l'utilisateur: ${authUserError?.message}` };
  }
  const internalUserId = authUser.id;

  // 3. Create a default user profile, linking it to the new auth_user
  const { error: profileError } = await supabase
    .from('user_profiles')
    .insert({ auth_user_id: internalUserId });

  if (profileError) {
    // In a real app, we should probably clean up the created auth_user here.
    return { error: `Erreur lors de la création du profil: ${profileError.message}` };
  }
  
  // 4. Assign the selected role
  const { error: roleError } = await supabase
    .from('user_roles')
    .insert({
      user_id: internalUserId,
      role_id: roleId,
    });

  if (roleError) {
    // In a real app, we should probably clean up the created auth_user and profile here.
    return { error: `Erreur lors de l'assignation du rôle: ${roleError.message}` };
  }

  await AuditService.log('SIGNUP', 'AUTH', internalUserId, { userId: internalUserId });

  redirect('/login?message=Compte créé. Veuillez vérifier vos e-mails pour la confirmation.');
}
