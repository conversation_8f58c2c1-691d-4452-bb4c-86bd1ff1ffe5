'use client';

import 'leaflet/dist/leaflet.css';
import { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import {
  MapPin,
  Maximize2,
  Loader2
} from 'lucide-react';

// Dynamic import pour éviter les erreurs SSR avec Leaflet
const MapContainer = dynamic(() => import('react-leaflet').then(mod => mod.MapContainer), { ssr: false });
const TileLayer = dynamic(() => import('react-leaflet').then(mod => mod.TileLayer), { ssr: false });
const Marker = dynamic(() => import('react-leaflet').then(mod => mod.Marker), { ssr: false });
const Popup = dynamic(() => import('react-leaflet').then(mod => mod.Popup), { ssr: false });

// Plus besoin de clustering pour le moment

export interface MapMarker {
  id: string;
  latitude: number;
  longitude: number;
  title: string;
  description?: string;
  status: 'active' | 'completed' | 'pending' | 'error';
  type: 'installation' | 'maintenance' | 'inspection';
  metadata?: Record<string, any>;
}

interface MapViewProps {
  markers: MapMarker[];
  center?: { lat: number; lng: number };
  zoom?: number;
  height?: string;
  className?: string;
  onMarkerClick?: (marker: MapMarker) => void;
  showControls?: boolean;
  showLegend?: boolean;

  title?: string;
  noWrapper?: boolean;
}

const statusConfig = {
  active: { color: '#1ca18c', label: 'Actif' },
  completed: { color: '#10b981', label: 'Terminé' },
  pending: { color: '#f59e0b', label: 'En attente' },
  error: { color: '#ef4444', label: 'Erreur' },
};

const typeConfig = {
  installation: { icon: '🏗️', label: 'Installation' },
  maintenance: { icon: '🔧', label: 'Maintenance' },
  inspection: { icon: '🔍', label: 'Inspection' },
};

export function MapView({
  markers,
  center = { lat: 48.8566, lng: 2.3522 }, // Paris par défaut
  zoom = 10,
  height = '400px',
  className,
  onMarkerClick,
  showControls = true,
  showLegend = true,

  title,
  noWrapper = false,
}: MapViewProps) {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [selectedMarker, setSelectedMarker] = useState<MapMarker | null>(null);
  const [isClient, setIsClient] = useState(false);


  // Assurer que le composant ne s'affiche que côté client
  useEffect(() => {
    setIsClient(true);
  }, []);

  const handleMarkerClick = (marker: MapMarker) => {
    setSelectedMarker(marker);
    onMarkerClick?.(marker);
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  const createCustomIcon = (marker: MapMarker) => {
    if (typeof window === 'undefined') return null;

    const L = require('leaflet');
    const color = statusConfig[marker.status].color;

    return L.divIcon({
      html: `
        <div style="
          background-color: ${color};
          width: 24px;
          height: 24px;
          border-radius: 50%;
          border: 2px solid white;
          box-shadow: 0 2px 4px rgba(0,0,0,0.3);
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12px;
        ">
          ${typeConfig[marker.type].icon}
        </div>
      `,
      className: 'custom-marker',
      iconSize: [24, 24],
      iconAnchor: [12, 12],
    });
  };

  if (!isClient) {
    const Skeleton = () => (
      <div className="flex items-center justify-center bg-gray-100" style={{ height }}>
        <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
      </div>
    );

    if (noWrapper) {
      return <Skeleton />;
    }

    return (
      <Card className={cn('relative overflow-hidden', className)}>
        {title && (
          <CardHeader>
            <CardTitle>{title}</CardTitle>
          </CardHeader>
        )}
        <CardContent className="p-0">
          <Skeleton />
        </CardContent>
      </Card>
    );
  }

  const MapContent = (
    <div
      className="relative overflow-hidden z-0 h-full w-full"
      style={{ height: isFullscreen ? '100vh' : height }}
    >
      <MapContainer
        center={[center.lat, center.lng]}
        zoom={zoom}
        style={{ height: '100%', width: '100%' }}
      >
        <TileLayer
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
        />
        {markers.map((marker) => (
          <Marker
            key={marker.id}
            position={[marker.latitude, marker.longitude]}
            icon={createCustomIcon(marker)}
            eventHandlers={{
              click: () => handleMarkerClick(marker),
            }}
          >
            <Popup maxWidth={300} minWidth={250}>
              <div className="space-y-3 p-1">
                <div className="border-b pb-2">
                  <h3 className="font-semibold text-base text-gray-800">{marker.title}</h3>
                  {marker.description && <p className="text-sm text-gray-600 mt-1">{marker.description}</p>}
                </div>

                <div className="flex items-center space-x-2">
                  <Badge variant="outline" style={{ borderColor: statusConfig[marker.status].color, color: statusConfig[marker.status].color }}>
                    {statusConfig[marker.status].label}
                  </Badge>
                  <Badge variant="secondary">{typeConfig[marker.type].label}</Badge>
                </div>

                {marker.metadata && (
                  <div className="space-y-2">
                    <h4 className="font-medium text-sm text-gray-700">Informations détaillées</h4>
                    <div className="grid grid-cols-1 gap-1 text-xs">
                      {Object.entries(marker.metadata).map(([key, value]) => (
                        <div key={key} className="flex justify-between items-center py-1 border-b border-gray-100 last:border-b-0">
                          <span className="font-medium text-gray-600">{key}:</span>
                          <span className="text-gray-800 text-right max-w-[120px] truncate" title={String(value)}>
                            {String(value)}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                <div className="pt-2 border-t">
                  <p className="text-xs text-gray-500 text-center">
                    Cliquez sur le marqueur pour plus de détails
                  </p>
                </div>
              </div>
            </Popup>
          </Marker>
        ))}
      </MapContainer>
      {showControls && (
        <div className="absolute top-4 right-4 flex flex-col space-y-2 z-[1000]">
          <Button size="sm" variant="outline" onClick={toggleFullscreen}>
            <Maximize2 className="h-4 w-4" />
          </Button>
        </div>
      )}
      {showLegend && (
        <div className="absolute bottom-4 left-4 bg-white rounded-lg shadow-lg p-3 space-y-2 z-[1000]">
          <h4 className="font-semibold text-sm mb-2">Légende</h4>
          <div className="space-y-1">
            {Object.entries(statusConfig).map(([status, config]) => (
              <div key={status} className="flex items-center space-x-2 text-xs">
                <div className="w-3 h-3 rounded-full" style={{ backgroundColor: config.color }} />
                <span>{config.label}</span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );

  if (noWrapper) {
    return (
      <div
        className={cn('relative w-full', isFullscreen && 'fixed inset-0 z-50', className)}
        style={{ height }}
      >
        {MapContent}
      </div>
    );
  }

  return (
    <Card className={cn('relative overflow-hidden', isFullscreen && 'fixed inset-0 z-50', className)}>
      {title && (
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>{title}</span>
            <Badge variant="outline">{markers.length} point{markers.length > 1 ? 's' : ''}</Badge>
          </CardTitle>
        </CardHeader>
      )}
      <CardContent className="p-0 relative">
        {MapContent}
      </CardContent>
    </Card>
  );
}

// Composant pour afficher des statistiques géographiques
interface MapStatsProps {
  markers: MapMarker[];
  className?: string;
}

export function MapStats({ markers, className }: MapStatsProps) {
  const stats = {
    total: markers.length,
    byStatus: Object.keys(statusConfig).reduce((acc, status) => {
      acc[status] = markers.filter(m => m.status === status).length;
      return acc;
    }, {} as Record<string, number>),
    byType: Object.keys(typeConfig).reduce((acc, type) => {
      acc[type] = markers.filter(m => m.type === type).length;
      return acc;
    }, {} as Record<string, number>),
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="text-base">Statistiques Géographiques</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="text-center">
          <div className="text-2xl font-bold text-kya-primary">{stats.total}</div>
          <p className="text-sm text-gray-600">Points total</p>
        </div>
        
        <div className="space-y-2">
          <h4 className="font-medium text-sm">Par statut:</h4>
          {Object.entries(stats.byStatus).map(([status, count]) => (
            <div key={status} className="flex items-center justify-between text-sm">
              <div className="flex items-center space-x-2">
                <div
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: statusConfig[status as keyof typeof statusConfig].color }}
                />
                <span>{statusConfig[status as keyof typeof statusConfig].label}</span>
              </div>
              <span className="font-medium">{count}</span>
            </div>
          ))}
        </div>
        
        <div className="space-y-2 pt-2 border-t">
          <h4 className="font-medium text-sm">Par type:</h4>
          {Object.entries(stats.byType).map(([type, count]) => (
            <div key={type} className="flex items-center justify-between text-sm">
              <div className="flex items-center space-x-2">
                <span>{typeConfig[type as keyof typeof typeConfig].icon}</span>
                <span>{typeConfig[type as keyof typeof typeConfig].label}</span>
              </div>
              <span className="font-medium">{count}</span>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
