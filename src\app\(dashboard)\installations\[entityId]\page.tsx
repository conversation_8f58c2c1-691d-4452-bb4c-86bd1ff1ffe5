// Dynamic Installations Page - KYA Dashboards
import { Suspense } from 'react';
import { notFound } from 'next/navigation';
import { InstallationDashboard } from '@/features/installations/components/installation-dashboard';
import { getEntityWithMappingAction } from '@/features/entities/actions';
import { Card, CardContent } from '@/components/ui/card';
import type { Metadata } from 'next';

// Loading component for the dashboard
function DashboardLoading() {
  return (
    <div className="space-y-6">
      {/* Header skeleton */}
      <div className="space-y-2">
        <div className="h-8 bg-gray-200 rounded w-1/3 animate-pulse"></div>
        <div className="h-4 bg-gray-200 rounded w-1/2 animate-pulse"></div>
      </div>

      {/* Stats cards skeleton */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {[...Array(4)].map((_, i) => (
          <Card key={i}>
            <CardContent className="pt-6">
              <div className="animate-pulse space-y-2">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
                <div className="h-3 bg-gray-200 rounded w-full"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Main content skeleton */}
      <Card>
        <CardContent className="pt-6">
          <div className="animate-pulse space-y-4">
            <div className="h-10 bg-gray-200 rounded w-full"></div>
            <div className="h-64 bg-gray-200 rounded w-full"></div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

interface PageProps {
  params: {
    entityId: string;
  };
}

// Main dynamic installations page component
export default async function DynamicInstallationsPage({ params }: PageProps) {
  const { entityId } = params;

  // Validate entity and check if it has installations feature mapping
  const result = await getEntityWithMappingAction(entityId);

  if (!result.data || !result.data.featureMapping ||
      result.data.featureMapping.businessModule !== 'installations') {
    notFound();
  }

  const entityWithMapping = result.data;

  return (
    <div className="container mx-auto py-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">
          Installations - {entityWithMapping.name}
        </h1>
        <p className="text-muted-foreground">
          Gestion des installations pour {entityWithMapping.name}
        </p>
      </div>
      
      <Suspense fallback={<DashboardLoading />}>
        <InstallationDashboard entityId={entityId} />
      </Suspense>
    </div>
  );
}

// Generate metadata for the page
export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const { entityId } =  params;

  try {
    const result = await getEntityWithMappingAction(entityId);

    if (!result.data) {
      return {
        title: 'Entité non trouvée | KYA Dashboards',
        description: 'L\'entité demandée n\'existe pas.',
      };
    }

    return {
      title: `Installations - ${result.data.name} | KYA Dashboards`,
      description: `Gestion des installations pour ${result.data.name}`,
    };
  } catch (error) {
    return {
      title: 'Erreur | KYA Dashboards',
      description: 'Une erreur est survenue lors du chargement de la page.',
    };
  }
}
