'use client';

import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON><PERSON>eader,
  DialogTitle,
} from '@/components/ui/dialog';
import { UserForm } from './user-form';
import type { UserWithProfile } from '@/types/users';

interface ManageUserDialogProps {
  isOpen: boolean;
  onOpenChange: (isOpen: boolean) => void;
  user: UserWithProfile | null;
  onSubmit: (values: any) => void;
  isSubmitting: boolean;
}

export function ManageUserDialog({
  isOpen,
  onOpenChange,
  user,
  onSubmit,
  isSubmitting,
}: ManageUserDialogProps) {
  const title = user ? "Modifier l'utilisateur" : 'Ajouter un utilisateur';

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
        </DialogHeader>
        <UserForm
          user={user}
          onSubmit={onSubmit}
          isSubmitting={isSubmitting}
        />
      </DialogContent>
    </Dialog>
  );
}