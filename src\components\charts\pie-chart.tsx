import { useMemo } from 'react';
import { <PERSON><PERSON><PERSON> as Recha<PERSON><PERSON><PERSON><PERSON><PERSON>, Pie, Cell } from 'recharts';
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  ChartLegend,
  ChartLegendContent,
  type ChartConfig
} from '@/components/ui/chart';
import { cn } from '@/lib/utils';

export interface PieChartData {
  label: string;
  value: number;
  color?: string;
  fill?: string;
}

interface PieChartProps {
  data: PieChartData[];
  className?: string;
  showTooltip?: boolean;
  showLegend?: boolean;
  innerRadius?: number;
  outerRadius?: number;
  centerText?: string;
  centerSubtext?: string;
  config?: ChartConfig;
  onSegmentClick?: (data: PieChartData, index: number) => void;
}

const defaultColors = [
  'hsl(var(--chart-1))', // KYA Primary
  'hsl(var(--chart-2))', // KYA Secondary
  'hsl(var(--chart-3))', // KYA Accent
  'hsl(var(--chart-4))', // KYA Brown
  'hsl(var(--chart-5))', // Additional colors
];

export function PieChart({
  data,
  className,
  showTooltip = true,
  showLegend = true,
  innerRadius = 0,
  outerRadius = 80,
  centerText,
  centerSubtext,
  config,
  onSegmentClick,
}: PieChartProps) {
  const processedData = useMemo(() => {
    return data.map((item, index) => ({
      ...item,
      fill: item.fill || item.color || `var(--color-${item.label.toLowerCase().replace(/\s+/g, '-')})` || defaultColors[index % defaultColors.length],
    }));
  }, [data]);

  const chartConfig = useMemo(() => {
    if (config) return config;

    return data.reduce((acc, item, index) => {
      const key = item.label.toLowerCase().replace(/\s+/g, '-');
      acc[key] = {
        label: item.label,
        color: item.color || defaultColors[index % defaultColors.length],
      };
      return acc;
    }, {} as ChartConfig);
  }, [data, config]);

  const total = processedData.reduce((sum, item) => sum + item.value, 0);

  if (total === 0) {
    return (
      <div className={cn('flex items-center justify-center h-[300px]', className)}>
        <div className="text-center text-muted-foreground">
          <div className="text-4xl mb-2">📊</div>
          <p>Aucune donnée disponible</p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn('relative', className)}>
      <ChartContainer config={chartConfig} className="h-[300px]">
        <RechartsPieChart>
          <Pie
            data={processedData}
            cx="50%"
            cy="50%"
            innerRadius={innerRadius}
            outerRadius={outerRadius}
            paddingAngle={2}
            dataKey="value"
            onClick={onSegmentClick}
            className={onSegmentClick ? 'cursor-pointer' : ''}
          >
            {processedData.map((entry, index) => (
              <Cell
                key={`cell-${index}`}
                fill={entry.fill}
                className="hover:opacity-80 transition-opacity"
              />
            ))}
          </Pie>

          {showTooltip && (
            <ChartTooltip
              content={
                <ChartTooltipContent
                  formatter={(value, name) => [
                    `${Number(value).toLocaleString('fr-FR')}`,
                    name
                  ]}
                />
              }
            />
          )}

          {showLegend && (
            <ChartLegend content={<ChartLegendContent />} />
          )}
        </RechartsPieChart>
      </ChartContainer>

      {/* Texte central pour les donut charts */}
      {(centerText || centerSubtext) && innerRadius > 0 && (
        <div className="absolute inset-0 flex flex-col items-center justify-center text-center pointer-events-none">
          {centerText && (
            <div className="text-2xl font-bold text-foreground">
              {centerText}
            </div>
          )}
          {centerSubtext && (
            <div className="text-sm text-muted-foreground mt-1">
              {centerSubtext}
            </div>
          )}
        </div>
      )}
    </div>
  );
}

// Composant Donut Chart (variante du Pie Chart avec un trou au centre)
interface DonutChartProps extends Omit<PieChartProps, 'innerRadius'> {
  innerRadius?: number;
}

export function DonutChart({
  innerRadius = 60,
  ...props
}: DonutChartProps) {
  return (
    <PieChart
      {...props}
      innerRadius={innerRadius}
    />
  );
}

// Hook pour préparer les données du graphique
export function usePieChartData<T>(
  data: T[],
  config: {
    labelKey: keyof T;
    valueKey: keyof T;
    colorKey?: keyof T;
    colors?: string[];
  }
) {
  return useMemo(() => {
    return data.map((item, index) => ({
      label: String(item[config.labelKey]),
      value: Number(item[config.valueKey]),
      color: config.colorKey 
        ? String(item[config.colorKey])
        : config.colors?.[index % (config.colors?.length || defaultColors.length)] 
        || defaultColors[index % defaultColors.length],
    }));
  }, [data, config]);
}

// Composant de graphique en secteurs avec animation
interface AnimatedPieChartProps extends PieChartProps {
  isVisible?: boolean;
}

export function AnimatedPieChart({ 
  isVisible = true, 
  ...props 
}: AnimatedPieChartProps) {
  return (
    <div className={cn(
      'transition-all duration-500',
      isVisible ? 'opacity-100 scale-100' : 'opacity-0 scale-95'
    )}>
      <PieChart {...props} />
    </div>
  );
}

// Composant de mini graphique en secteurs
interface MiniPieChartProps {
  data: PieChartData[];
  size?: number;
  className?: string;
}

export function MiniPieChart({
  data,
  size = 60,
  className
}: MiniPieChartProps) {
  return (
    <div className={cn('w-fit', className)} style={{ width: size, height: size }}>
      <PieChart
        data={data}
        showTooltip={false}
        showLegend={false}
        outerRadius={size / 2 - 5}
      />
    </div>
  );
}

// Utilitaire pour générer des couleurs automatiquement
export function generatePieChartColors(count: number, baseHue = 160): string[] {
  const colors: string[] = [];
  const saturation = 60;
  const lightness = 50;
  
  for (let i = 0; i < count; i++) {
    const hue = (baseHue + (i * 360 / count)) % 360;
    colors.push(`hsl(${hue}, ${saturation}%, ${lightness}%)`);
  }
  
  return colors;
}
