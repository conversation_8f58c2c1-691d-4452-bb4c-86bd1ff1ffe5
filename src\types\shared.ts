// Shared Types - KYA Dashboards
// Types used across multiple domains (clients, projects, etc.)

// Client Types (shared across all domains) - Based on actual DB schema
export interface Client {
  id: string;
  name: string;
  type: ClientType;
  contact_info: Record<string, any>; // Phone, email, etc. (JSONB)
  address?: string; // TEXT
  gps_coordinates?: string; // TEXT - "(latitude, longitude)"
  is_active: boolean;
  created_at: Date;
  updated_at: Date;
}

export type ClientType = 'INDIVIDUAL' | 'INSTITUTION'; // Only these two types in DB CHECK constraint

// Project Types (shared across all domains) - Based on actual DB schema
export interface Project {
  id: string;
  name: string;
  client_id: string; // UUID NOT NULL REFERENCES clients(id)
  description?: string; // TEXT
  start_date?: Date; // DATE
  end_date?: Date; // DATE
  budget?: number; // DECIMAL(15,2)
  status: ProjectStatus; // VARCHAR(50) with CHECK constraint
  created_at: Date;
  updated_at: Date;

  // Relations (populated when needed)
  client?: Client;
}

export type ProjectStatus = 'ACTIVE' | 'COMPLETED' | 'CANCELLED' | 'ON_HOLD'; // Based on DB CHECK constraint

// Form Data Types for Client
export interface CreateClientData {
  name: string;
  type: ClientType;
  contact_info: Record<string, any>;
  address?: string;
  gps_coordinates?: string;
}

export interface UpdateClientData {
  name?: string;
  type?: ClientType;
  contact_info?: Record<string, any>;
  address?: string;
  gps_coordinates?: string;
  is_active?: boolean;
}

// Form Data Types for Project
export interface CreateProjectData {
  name: string;
  client_id: string;
  description?: string;
  start_date?: Date;
  end_date?: Date;
  budget?: number;
  status?: ProjectStatus;
}

export interface UpdateProjectData {
  name?: string;
  client_id?: string;
  description?: string;
  start_date?: Date;
  end_date?: Date;
  budget?: number;
  status?: ProjectStatus;
}

// Types partagés pour les personnes
export interface Person {
  id: string;
  first_name: string;
  last_name: string;
  email?: string;
  phone?: string;
  employee_id?: string;
  position?: string;
  entity_id?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  created_by: string;
  updated_by?: string;
}

export interface CreatePersonData {
  first_name: string;
  last_name: string;
  email?: string;
  phone?: string;
  employee_id?: string;
  position?: string;
  entity_id?: string;
  is_active?: boolean;
}

export interface UpdatePersonData {
  first_name?: string;
  last_name?: string;
  email?: string;
  phone?: string;
  employee_id?: string;
  position?: string;
  entity_id?: string;
  is_active?: boolean;
}
