// RBAC (Role-Based Access Control) Types
export interface Role {
  id: string;
  name: string;
  description?: string;
  isSystemRole: boolean;
  createdAt: Date;
  updatedAt: Date;
  
  // Relations (populated when needed)
  permissions?: RolePermission[];
  users?: import('./users').UserRole[];
}

export interface Permission {
  id: string;
  name: string;
  displayName: string;
  description?: string;
  resource: string;
  action: PermissionAction;
  isSystemPermission: boolean;
  createdAt: Date;
  updatedAt: Date;
  
  // Relations (populated when needed)
  roles?: RolePermission[];
  users?: import('./users').UserPermission[];
}

export type PermissionAction = 'create' | 'read' | 'update' | 'delete' | 'manage' | 'execute' | 'approve';

export interface RolePermission {
  id: string;
  roleId: string;
  permissionId: string;
  grantedAt: Date;
  grantedBy?: string;
  
  // Relations (populated when needed)
  role?: Role;
  permission?: Permission;
  grantedByUser?: import('./users').User;
}

// Permission Context Types
export interface PermissionContext {
  userId: string;
  entityId?: string;
  resource: string;
  action: PermissionAction;
  metadata?: Record<string, any>;
}

export interface PermissionCheck {
  hasPermission: boolean;
  reason?: string;
  requiredPermissions: string[];
  userPermissions: string[];
  inheritedFrom?: 'role' | 'direct' | 'entity';
}

export interface RoleHierarchy {
  role: Role;
  level: number;
  inheritsFrom: Role[];
  inheritsTo: Role[];
  effectivePermissions: Permission[];
}

// Form and Input Types
export interface CreateRoleData {
  name: string;
  description?: string;
}

export interface UpdateRoleData {
  name?: string;
  description?: string;
}

export interface CreatePermissionData {
  name: string;
  displayName: string;
  description?: string;
  resource: string;
  action: PermissionAction;
}

export interface UpdatePermissionData {
  name?: string;
  displayName?: string;
  description?: string;
  resource?: string;
  action?: PermissionAction;
}

export interface AssignPermissionToRoleData {
  roleId: string;
  permissionId: string;
}

export interface RevokePermissionFromRoleData {
  roleId: string;
  permissionId: string;
}

// Query and Filter Types
export interface RoleFilters {
  isActive?: boolean;
  isSystemRole?: boolean;
  level?: number;
  levelRange?: {
    min?: number;
    max?: number;
  };
  hasPermission?: string;
  searchTerm?: string;
}

export interface PermissionFilters {
  resource?: string;
  action?: PermissionAction;
  isSystemPermission?: boolean;
  hasRole?: string;
  searchTerm?: string;
}

// Matrix and UI Types
export interface PermissionMatrix {
  roles: Role[];
  permissions: Permission[];
  assignments: Map<string, Set<string>>; // roleId -> Set<permissionId>
}

export interface RolePermissionMatrix {
  roleId: string;
  roleName: string;
  permissions: Array<{
    permissionId: string;
    permissionName: string;
    resource: string;
    action: PermissionAction;
    hasPermission: boolean;
    grantedAt?: Date;
  }>;
}

export interface ResourcePermissions {
  resource: string;
  actions: Array<{
    action: PermissionAction;
    permissionId: string;
    permissionName: string;
    roles: Array<{
      roleId: string;
      roleName: string;
      hasPermission: boolean;
    }>;
  }>;
}

// Statistics and Analytics Types
export interface RBACStats {
  totalRoles: number;
  activeRoles: number;
  systemRoles: number;
  customRoles: number;
  totalPermissions: number;
  systemPermissions: number;
  customPermissions: number;
  averagePermissionsPerRole: number;
  rolesWithoutPermissions: number;
  permissionsWithoutRoles: number;
}

export interface RoleUsageStats {
  roleId: string;
  roleName: string;
  userCount: number;
  permissionCount: number;
  entityCount: number;
  lastAssigned?: Date;
  isActive: boolean;
}

export interface PermissionUsageStats {
  permissionId: string;
  permissionName: string;
  resource: string;
  action: PermissionAction;
  roleCount: number;
  userCount: number;
  lastGranted?: Date;
}

// Audit and History Types
export interface RoleAuditLog {
  id: string;
  roleId: string;
  action: 'created' | 'updated' | 'deleted' | 'activated' | 'deactivated';
  changes: Record<string, any>;
  performedBy: string;
  performedAt: Date;
  reason?: string;
  
  // Relations (populated when needed)
  role?: Role;
  performedByUser?: import('./users').User;
}

export interface PermissionAuditLog {
  id: string;
  permissionId: string;
  action: 'created' | 'updated' | 'deleted' | 'granted' | 'revoked';
  changes: Record<string, any>;
  performedBy: string;
  performedAt: Date;
  reason?: string;
  targetId?: string; // roleId or userId
  targetType?: 'role' | 'user';
  
  // Relations (populated when needed)
  permission?: Permission;
  performedByUser?: import('./users').User;
}

// Guard and Middleware Types
export interface RBACGuardConfig {
  requiredPermissions?: string[];
  requiredRoles?: string[];
  requireAll?: boolean; // true = AND logic, false = OR logic
  entityContext?: boolean;
  fallbackBehavior?: 'deny' | 'redirect' | 'error';
  redirectTo?: string;
}

export interface RBACMiddlewareContext {
  userId: string;
  entityId?: string;
  userRoles: string[];
  userPermissions: string[];
  effectivePermissions: string[];
}

// Bulk Operations Types
export interface BulkRoleOperation {
  operation: 'create' | 'update' | 'delete' | 'activate' | 'deactivate';
  roleIds?: string[];
  data?: any;
  filters?: RoleFilters;
}

export interface BulkPermissionOperation {
  operation: 'create' | 'update' | 'delete' | 'assign' | 'revoke';
  permissionIds?: string[];
  roleIds?: string[];
  data?: any;
  filters?: PermissionFilters;
}

export interface BulkRBACResult {
  success: boolean;
  processedCount: number;
  successCount: number;
  errorCount: number;
  errors: Array<{
    id?: string;
    error: string;
  }>;
}
