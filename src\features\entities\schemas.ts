import { z } from 'zod';
import type { EntityType } from '@/types/entities';

// This should match the EntityType from @/types/entities
export const ENTITY_TYPES: EntityType[] = ['DIRECTION', 'EQUIPE', 'SOUS_EQUIPE', 'DEPARTEMENT'];

export const entitySchema = z.object({
  name: z.string().min(3, { message: "Le nom doit contenir au moins 3 caractères." }),
  type: z
    .string()
    .min(1, { message: "Le type est requis." })
    .refine((val): val is EntityType => ENTITY_TYPES.includes(val as EntityType), {
      message: "Veuillez sélectionner un type valide.",
    }),
  parentId: z.string().optional().nullable().transform(val => val || undefined),
  code: z.string().min(1, { message: "Le code est requis." }),
  description: z.string().optional().nullable(),
});

export type EntitySchema = z.infer<typeof entitySchema>;