// Entity-Feature Mapping Service - KYA Dashboards
import type { Entity, EntityFeatureMapping, BusinessModule, EntityNavigationItem } from '@/types/entities';

/**
 * Configuration mapping entity codes to business modules
 * This can be extended or moved to a database table for dynamic configuration
 */
const ENTITY_FEATURE_MAPPINGS: EntityFeatureMapping[] = [
  // Installation teams
  {
    entityCode: 'INST',
    businessModule: 'installations',
    featurePath: '/installations',
    displayName: 'Installations',
    icon: 'Zap',
    permissions: ['installations.access'],
  },
  {
    entityCode: 'INSTALL',
    businessModule: 'installations',
    featurePath: '/installations',
    displayName: 'Installations',
    icon: 'Zap',
    permissions: ['installations.access'],
  },
  // Maintenance teams
  {
    entityCode: 'MAINT',
    businessModule: 'maintenance',
    featurePath: '/maintenance',
    displayName: 'Maintenance',
    icon: 'Wrench',
    permissions: ['maintenance.access'],
  },
  {
    entityCode: 'MAINTENANCE',
    businessModule: 'maintenance',
    featurePath: '/maintenance',
    displayName: 'Maintenance',
    icon: 'Wrench',
    permissions: ['maintenance.access'],
  },
  // Commercial teams
  {
    entityCode: 'COMM',
    businessModule: 'commercial',
    featurePath: '/commercial',
    displayName: 'Commercial',
    icon: 'TrendingUp',
    permissions: ['commercial.access'],
  },
  {
    entityCode: 'COMMERCIAL',
    businessModule: 'commercial',
    featurePath: '/commercial',
    displayName: 'Commercial',
    icon: 'TrendingUp',
    permissions: ['commercial.access'],
  },
  // Project management teams
  {
    entityCode: 'PROJ',
    businessModule: 'projects',
    featurePath: '/projects',
    displayName: 'Projets',
    icon: 'FolderOpen',
    permissions: ['projects.access'],
  },
  {
    entityCode: 'PROJECT',
    businessModule: 'projects',
    featurePath: '/projects',
    displayName: 'Projets',
    icon: 'FolderOpen',
    permissions: ['projects.access'],
  },
  // Support teams
  {
    entityCode: 'SUPP',
    businessModule: 'support',
    featurePath: '/support',
    displayName: 'Support',
    icon: 'HelpCircle',
    permissions: ['support.access'],
  },
  {
    entityCode: 'SUPPORT',
    businessModule: 'support',
    featurePath: '/support',
    displayName: 'Support',
    icon: 'HelpCircle',
    permissions: ['support.access'],
  },
];

export class EntityFeatureMappingService {
  /**
   * Get feature mapping for an entity code
   */
  static getFeatureMappingByCode(entityCode: string): EntityFeatureMapping | null {
    // Try exact match first
    let mapping = ENTITY_FEATURE_MAPPINGS.find(m => m.entityCode === entityCode.toUpperCase());
    
    // If no exact match, try partial match (entity code contains mapping code)
    if (!mapping) {
      mapping = ENTITY_FEATURE_MAPPINGS.find(m => 
        entityCode.toUpperCase().includes(m.entityCode)
      );
    }
    
    return mapping || null;
  }

  /**
   * Get feature mapping for an entity
   */
  static getFeatureMappingByEntity(entity: Entity): EntityFeatureMapping | null {
    return this.getFeatureMappingByCode(entity.code);
  }

  /**
   * Get business module for an entity code
   */
  static getBusinessModuleByCode(entityCode: string): BusinessModule | null {
    const mapping = this.getFeatureMappingByCode(entityCode);
    return mapping?.businessModule || null;
  }

  /**
   * Get feature path for an entity
   */
  static getFeaturePathByEntity(entity: Entity): string | null {
    const mapping = this.getFeatureMappingByEntity(entity);
    return mapping?.featurePath || null;
  }

  /**
   * Transform entity hierarchy into navigation items with feature mappings
   */
  static transformToNavigationItems(entities: Entity[]): EntityNavigationItem[] {
    return entities.map(entity => {
      const mapping = this.getFeatureMappingByEntity(entity);
      
      return {
        id: entity.id,
        name: entity.name,
        code: entity.code,
        type: entity.type,
        businessModule: mapping?.businessModule,
        featurePath: mapping?.featurePath,
        displayName: mapping?.displayName,
        icon: mapping?.icon,
        children: entity.children ? this.transformToNavigationItems(entity.children) : [],
      };
    });
  }

  /**
   * Get all available business modules
   */
  static getAllBusinessModules(): BusinessModule[] {
    return Array.from(new Set(ENTITY_FEATURE_MAPPINGS.map(m => m.businessModule)));
  }

  /**
   * Get all mappings for a specific business module
   */
  static getMappingsByBusinessModule(businessModule: BusinessModule): EntityFeatureMapping[] {
    return ENTITY_FEATURE_MAPPINGS.filter(m => m.businessModule === businessModule);
  }

  /**
   * Check if an entity has a feature mapping
   */
  static hasFeatureMapping(entity: Entity): boolean {
    return this.getFeatureMappingByEntity(entity) !== null;
  }

  /**
   * Get default feature path for entities without specific mappings
   */
  static getDefaultFeaturePath(entity: Entity): string {
    // Default routing based on entity type
    switch (entity.type) {
      case 'DIRECTION':
        return `/dashboard/direction/${entity.id}`;
      case 'EQUIPE':
        return `/dashboard/team/${entity.id}`;
      case 'SOUS_EQUIPE':
        return `/dashboard/subteam/${entity.id}`;
      case 'DEPARTEMENT':
        return `/dashboard/department/${entity.id}`;
      default:
        return `/dashboard/entity/${entity.id}`;
    }
  }

  /**
   * Get complete navigation path for an entity (with fallback)
   */
  static getNavigationPath(entity: Entity): string {
    const featurePath = this.getFeaturePathByEntity(entity);
    return featurePath || this.getDefaultFeaturePath(entity);
  }

  /**
   * Add or update a feature mapping (for dynamic configuration)
   */
  static addFeatureMapping(mapping: EntityFeatureMapping): void {
    const existingIndex = ENTITY_FEATURE_MAPPINGS.findIndex(m => m.entityCode === mapping.entityCode);
    
    if (existingIndex >= 0) {
      ENTITY_FEATURE_MAPPINGS[existingIndex] = mapping;
    } else {
      ENTITY_FEATURE_MAPPINGS.push(mapping);
    }
  }

  /**
   * Remove a feature mapping
   */
  static removeFeatureMapping(entityCode: string): boolean {
    const index = ENTITY_FEATURE_MAPPINGS.findIndex(m => m.entityCode === entityCode);
    
    if (index >= 0) {
      ENTITY_FEATURE_MAPPINGS.splice(index, 1);
      return true;
    }
    
    return false;
  }
}
