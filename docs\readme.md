# KYA Dashboards

Application de tableau de bord d'entreprise pour digitaliser et automatiser les processus de suivi journalier actuellement gérés dans Excel.

## Vision du Projet

KYA Dashboards transforme complètement la façon dont votre entreprise gère le suivi des travaux journaliers. Fini les classeurs Excel envoyés par email au directeur chaque soir - place à une solution centralisée, temps réel et intelligente.

### Problématique Actuelle
- **Processus manuel chronophage** : Saisie dans Excel, compilation, envoi par email
- **Manque de visibilité** : Le directeur doit lire tous les classeurs sans statistiques consolidées
- **Perte de temps** : Pas de vue d'ensemble, pas d'alertes automatiques
- **Erreurs humaines** : Risques liés à la manipulation manuelle des données

### Solution KYA Dashboards
- **Interface intuitive** : Système de double sidebar pour navigation hiérarchique
- **Dashboards temps réel** : Statistiques automatiques et consolidées
- **Accès sécurisé** : Système RBAC avec permissions granulaires
- **Suivi intelligent** : Versioning automatique des données journalières

## Interface Utilisateur Innovante

### Système de Double Sidebar

L'application utilise une navigation hiérarchique révolutionnaire avec deux sidebars complémentaires :

```
Sidebar Principal          Sidebar Secondaire         Contenu Central
├─ Dashboard              │                          │
│  ├─ Direction Tech ──→  │ ├─ Équipe Installation   │ ├─ Aperçu global
│  ├─ Direction Comm ──→  │ ├─ Équipe Maintenance    │ ├─ Suivi journalier  
│  └─ Direction Support   │ └─ Équipe Support        │ ├─ Alertes urgentes
├─ Saisie Données         │                          │ ├─ Responsables
│  ├─ Direction Tech ──→  │ ├─ Équipe Installation   │ ├─ Historique
│  └─ ...                 │ └─ ...                   │ └─ Performance
└─ Administration         │                          │
   ├─ Utilisateurs        │                          │
   ├─ Rôles               │                          │
   └─ Permissions         │                          │
```

### Navigation Contextuelle
- **Sidebar Principal** : Sections principales (Dashboard, Saisie, Admin)
- **Sidebar Secondaire** : Navigation contextuelle (Directions → Équipes)
- **Contenu Central** : Dashboards riches avec tabs contextuels
- **Filtres Visuels** : Marquage intelligent des éléments filtrés

### Tabs Contextuels par Équipe

Chaque équipe dispose de tabs spécialisés, par exemple pour l'équipe Installation :

1. **Aperçu global du projet** - Vue d'ensemble avec KPIs clés
2. **Suivi journalier** - Formulaires de saisie et progression
3. **Alertes et décisions urgentes** - Notifications critiques
4. **Responsables & équipes** - Gestion des assignations
5. **Historique & performance** - Analyses et tendances

## Architecture Organisationnelle

L'application modélise fidèlement votre structure hiérarchique :

```
Entreprise
├── Direction Technique
│   ├── Équipe Installation
│   ├── Équipe Maintenance
│   └── Équipe Support Technique
├── Direction Commerciale
│   ├── Équipe Ventes
│   └── Équipe Marketing
└── Direction Services Supports
    ├── Équipe RH
    └── Équipe Finance
```

## Gestion Intelligente des Données

### Suivi Journalier avec Versioning

Le système gère intelligemment les modifications des données de suivi :

- **Même jour** : Mise à jour directe de la saisie courante
- **Jour différent** : Création automatique d'une nouvelle version
- **Audit complet** : Traçabilité de toutes les modifications
- **Historique préservé** : Aucune perte de données historiques

### Exemple de Données Métier (Installation)
```
Projets → Installations → Suivi Journalier
├─ Taux d'avancement par composant
├─ Remarques et observations
├─ Validation par responsable
└─ Historique des modifications
```

## Stack Technologique

### Frontend
- **Next.js 15.4.3** - Framework React avec App Router
- **React 19.1.0** - Interface utilisateur moderne
- **TypeScript 5** - Typage strict pour la robustesse
- **TailwindCSS 4** - Styling moderne et responsive
- **Shadcn/ui** - Composants UI élégants et accessibles
- **Lucide React** - Icônes cohérentes

### Backend & Base de Données
- **Supabase** - Backend-as-a-Service avec PostgreSQL
- **Server Actions** - Logique serveur intégrée (pas d'API routes)
- **RBAC Applicatif** - Sécurité gérée au niveau application

### Architecture
- **Features Autonomes** - Chaque fonctionnalité complètement indépendante
- **Services Métier** - Logique complexe séparée des actions
- **Providers React** - Gestion d'état globale avec contextes
- **Validation Zod** - Validation robuste des données

## Système de Permissions (RBAC)

### Rôles Principaux
- **DIRECTEUR** : Accès complet à tous les dashboards et statistiques
- **MANAGER_DIRECTION** : Gestion de sa direction et équipes
- **MANAGER_EQUIPE** : Gestion de son équipe et saisie de données
- **SAISIE_DONNEES** : Saisie de données pour son équipe
- **LECTURE_SEULE** : Consultation des données autorisées

### Permissions Granulaires
- Gestion des utilisateurs et attribution des rôles
- Création et modification des structures organisationnelles
- Saisie et validation des données de suivi
- Consultation des dashboards selon les autorisations
- Export et partage de données

## Structure du Projet

```
src/
├── app/                          # Next.js App Router (minimal)
│   ├── (auth)/                   # Authentification
│   ├── (dashboard)/              # Dashboards avec double sidebar
│   └── layout.tsx                # Layout racine avec providers
├── features/                     # Fonctionnalités métier autonomes
│   ├── auth/                     # Authentification et sessions
│   ├── entities/                 # Gestion organisationnelle
│   ├── users/                    # Gestion des utilisateurs
│   ├── rbac/                     # Système de permissions
│   ├── dashboard/                # Dashboards et visualisations
│   └── data-entry/               # Saisie de données
├── components/                   # Composants UI réutilisables
│   ├── ui/                       # Composants Shadcn/ui
│   └── providers/                # Providers React (Auth, Query, Theme)
├── utils/                        # Services et utilitaires partagés
│   ├── supabase/                 # Configuration Supabase
│   ├── rbac/                     # Guards et middleware RBAC
│   └── validations/              # Schémas de validation
└── types/                        # Types TypeScript globaux
```

## Plan de Développement

### Phase 1 : Infrastructure Critique (5-6 jours)
- Types TypeScript et services de base
- Système d'authentification avec AuthProvider
- RBAC complet avec guards et permissions
- Base de données et audit trail

### Phase 2 : Structure Dashboard (4-5 jours)
- Système de double sidebar
- Navigation hiérarchique (Directions → Équipes)
- Layout principal et routing contextuel
- Gestion des entités organisationnelles

### Phase 3 : Administration (4-5 jours)
- Interface de gestion des utilisateurs
- Administration des entités et hiérarchie
- Gestion des rôles et permissions
- Interface d'administration complète

### Phase 4 : Dashboards et Visualisation (5-6 jours)
- Dashboards adaptatifs par rôle
- Système de tabs contextuels
- Filtres visuels avec marquage
- Composants de statistiques et graphiques

### Phase 5+ : Features Métier (Itératif)
- Développement par équipe des dashboards spécialisés
- Formulaires de saisie adaptés aux métiers
- Workflows de validation et d'approbation
- Intégration des données historiques

## Avantages Métier

### Pour le Directeur
- **Vision consolidée** : Tous les KPIs en un coup d'œil
- **Temps réel** : Données actualisées automatiquement
- **Alertes intelligentes** : Notification des situations critiques
- **Analyses avancées** : Tendances et comparaisons automatiques

### Pour les Managers
- **Interface dédiée** : Vue adaptée à leur périmètre
- **Gestion d'équipe** : Outils de suivi et d'assignation
- **Validation simplifiée** : Workflow d'approbation intégré
- **Historique complet** : Traçabilité de toutes les actions

### Pour les Équipes
- **Saisie intuitive** : Formulaires adaptés à leur métier
- **Feedback immédiat** : Validation en temps réel
- **Historique personnel** : Suivi de leurs contributions
- **Interface mobile** : Saisie possible depuis le terrain

## Sécurité et Conformité

- **Authentification robuste** : Supabase Auth avec sessions sécurisées
- **Autorisation granulaire** : RBAC avec permissions par entité
- **Audit complet** : Traçabilité de toutes les actions sensibles
- **Validation stricte** : Contrôles côté client et serveur
- **Données chiffrées** : Protection des données en transit et au repos

## Déploiement et Maintenance

### Environnements
- **Développement** : Local avec Supabase local
- **Staging** : Environnement de test avec données anonymisées
- **Production** : Déploiement Vercel avec Supabase Cloud

### Monitoring
- **Performance** : Surveillance des temps de réponse
- **Erreurs** : Tracking automatique des erreurs
- **Usage** : Métriques d'utilisation par fonctionnalité
- **Sécurité** : Monitoring des tentatives d'accès non autorisées

---

**KYA Dashboards** : La solution complète pour digitaliser et optimiser vos processus de suivi journalier.

*Version 0.1.0 - Architecture finalisée le 2025-07-23*
