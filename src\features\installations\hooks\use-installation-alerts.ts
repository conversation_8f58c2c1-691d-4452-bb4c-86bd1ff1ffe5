// Installation Alerts Hooks - KYA Dashboards
'use client';

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { createClient } from '@/utils/supabase/client';
import type { Installation } from '../types';

export interface InstallationAlert {
  id: string;
  installation_id: string;
  alert_type: 'DELAY' | 'TECHNICAL_ISSUE' | 'MAINTENANCE_DUE' | 'QUALITY_CONCERN' | 'RESOURCE_SHORTAGE';
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  title: string;
  description: string;
  status: 'ACTIVE' | 'ACKNOWLEDGED' | 'RESOLVED' | 'DISMISSED';
  created_at: string;
  updated_at: string;
  created_by: string;
  acknowledged_by?: string;
  acknowledged_at?: string;
  resolved_by?: string;
  resolved_at?: string;
  
  // Relations
  installation?: Installation;
  creator?: {
    id: string;
    display_name: string;
  };
}

export interface AlertFilters {
  installation_ids?: string[];
  alert_types?: string[];
  severities?: string[];
  statuses?: string[];
  team_leader_id?: string;
  date_range?: {
    from: Date;
    to: Date;
  };
}

export interface AlertStats {
  total: number;
  active: number;
  critical: number;
  high: number;
  medium: number;
  low: number;
  by_type: Record<string, number>;
  by_installation: Record<string, number>;
  recent_alerts: InstallationAlert[];
}

// Hook for fetching installation alerts
export function useInstallationAlerts(filters?: AlertFilters) {
  return useQuery({
    queryKey: ['installation-alerts', filters],
    queryFn: async () => {
      const supabase = createClient();
      let query = supabase
        .from('installation_alerts')
        .select(`
          *,
          installation:installations(id, name, installation_number, site_location),
          creator:auth_users(id, display_name)
        `);

      // Apply filters
      if (filters?.installation_ids && filters.installation_ids.length > 0) {
        query = query.in('installation_id', filters.installation_ids);
      }

      if (filters?.alert_types && filters.alert_types.length > 0) {
        query = query.in('alert_type', filters.alert_types);
      }

      if (filters?.severities && filters.severities.length > 0) {
        query = query.in('severity', filters.severities);
      }

      if (filters?.statuses && filters.statuses.length > 0) {
        query = query.in('status', filters.statuses);
      }

      if (filters?.date_range) {
        query = query
          .gte('created_at', filters.date_range.from.toISOString())
          .lte('created_at', filters.date_range.to.toISOString());
      }

      const { data, error } = await query.order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

// Hook for alert statistics
export function useAlertStats(teamLeaderId?: string) {
  return useQuery({
    queryKey: ['alert-stats', teamLeaderId],
    queryFn: async (): Promise<AlertStats> => {
      const supabase = createClient();
      
      let query = supabase
        .from('installation_alerts')
        .select(`
          *,
          installation:installations(id, name, team_leader_id)
        `);

      if (teamLeaderId) {
        query = query.eq('installation.team_leader_id', teamLeaderId);
      }

      const { data: alerts, error } = await query;

      if (error) throw error;

      const alertData = alerts || [];
      
      // Calculate statistics
      const total = alertData.length;
      const active = alertData.filter(a => a.status === 'ACTIVE').length;
      const critical = alertData.filter(a => a.severity === 'CRITICAL').length;
      const high = alertData.filter(a => a.severity === 'HIGH').length;
      const medium = alertData.filter(a => a.severity === 'MEDIUM').length;
      const low = alertData.filter(a => a.severity === 'LOW').length;

      // Group by type
      const by_type = alertData.reduce((acc, alert) => {
        acc[alert.alert_type] = (acc[alert.alert_type] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      // Group by installation
      const by_installation = alertData.reduce((acc, alert) => {
        const installationName = alert.installation?.name || 'Unknown';
        acc[installationName] = (acc[installationName] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      // Recent alerts (last 7 days)
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
      const recent_alerts = alertData
        .filter(a => new Date(a.created_at) >= sevenDaysAgo)
        .slice(0, 10);

      return {
        total,
        active,
        critical,
        high,
        medium,
        low,
        by_type,
        by_installation,
        recent_alerts,
      };
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
    refetchInterval: 5 * 60 * 1000, // Refresh every 5 minutes
  });
}

// Hook for alert mutations
export function useAlertMutations() {
  const queryClient = useQueryClient();

  const createAlert = useMutation({
    mutationFn: async (alertData: Omit<InstallationAlert, 'id' | 'created_at' | 'updated_at'>) => {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('installation_alerts')
        .insert([alertData])
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['installation-alerts'] });
      queryClient.invalidateQueries({ queryKey: ['alert-stats'] });
    },
  });

  const acknowledgeAlert = useMutation({
    mutationFn: async ({ id, userId }: { id: string; userId: string }) => {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('installation_alerts')
        .update({
          status: 'ACKNOWLEDGED',
          acknowledged_by: userId,
          acknowledged_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        })
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['installation-alerts'] });
      queryClient.invalidateQueries({ queryKey: ['alert-stats'] });
    },
  });

  const resolveAlert = useMutation({
    mutationFn: async ({ id, userId }: { id: string; userId: string }) => {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('installation_alerts')
        .update({
          status: 'RESOLVED',
          resolved_by: userId,
          resolved_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        })
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['installation-alerts'] });
      queryClient.invalidateQueries({ queryKey: ['alert-stats'] });
    },
  });

  const dismissAlert = useMutation({
    mutationFn: async ({ id, userId }: { id: string; userId: string }) => {
      const supabase = createClient();
      const { data, error } = await supabase
        .from('installation_alerts')
        .update({
          status: 'DISMISSED',
          updated_at: new Date().toISOString(),
        })
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['installation-alerts'] });
      queryClient.invalidateQueries({ queryKey: ['alert-stats'] });
    },
  });

  return {
    createAlert,
    acknowledgeAlert,
    resolveAlert,
    dismissAlert,
  };
}

// Hook for automatic alert detection
export function useAlertDetection() {
  const { createAlert } = useAlertMutations();

  const detectDelayAlerts = async (installations: Installation[], userId: string) => {
    const now = new Date();
    const overdueInstallations = installations.filter(installation => {
      if (!installation.planned_end_date || installation.status === 'COMPLETED') return false;
      return new Date(installation.planned_end_date) < now;
    });

    for (const installation of overdueInstallations) {
      const daysPastDue = Math.floor(
        (now.getTime() - new Date(installation.planned_end_date!).getTime()) / (1000 * 60 * 60 * 24)
      );

      await createAlert.mutateAsync({
        installation_id: installation.id,
        alert_type: 'DELAY',
        severity: daysPastDue > 7 ? 'HIGH' : daysPastDue > 3 ? 'MEDIUM' : 'LOW',
        title: `Installation en retard`,
        description: `L'installation "${installation.name}" est en retard de ${daysPastDue} jour(s).`,
        status: 'ACTIVE',
        created_by: userId,
      });
    }
  };

  return {
    detectDelayAlerts,
  };
}

// Hook for alert notifications
export function useAlertNotifications(userId: string) {
  return useQuery({
    queryKey: ['alert-notifications', userId],
    queryFn: async () => {
      const supabase = createClient();
      
      // Get unread alerts for installations where user is team leader
      const { data, error } = await supabase
        .from('installation_alerts')
        .select(`
          *,
          installation:installations(id, name, team_leader_id)
        `)
        .eq('status', 'ACTIVE')
        .eq('installation.team_leader_id', userId)
        .order('created_at', { ascending: false })
        .limit(10);

      if (error) throw error;
      return data || [];
    },
    staleTime: 1 * 60 * 1000, // 1 minute
    refetchInterval: 2 * 60 * 1000, // Refresh every 2 minutes
  });
}
