import { createClient } from '@/utils/supabase/server';
import { keysToCamel, keysToSnake } from '@/utils/case-converter';
import type { UserWithProfile } from '@/types/users';
import type { CreateUserFormValues, UpdateUserFormValues } from '../schemas';

/**
 * UserService provides static methods for managing users and their profiles.
 * This class is intended for server-side use only.
 */
export class UserService {
  /**
   * Retrieves a list of all users with their associated profiles and roles.
   */
  static async getUsers(): Promise<UserWithProfile[]> {
    const supabase = await createClient();

    // Query users with their profiles and roles using joins
    const { data, error } = await supabase
      .from('auth_users')
      .select(`
        *,
        user_profiles!auth_user_id (
          id,
          display_name,
          avatar_url,
          bio,
          preferences,
          notification_settings,
          dashboard_config,
          created_at,
          updated_at
        ),
        user_roles!user_id (
          id,
          granted_at,
          expires_at,
          is_active,
          roles (
            id,
            name,
            display_name,
            description,
            level,
            is_system_role
          )
        )
      `)
      .eq('is_active', true)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching users:', error);
      throw new Error('Failed to fetch users.');
    }

    // Transform the data to match UserWithProfile type
    const transformedData = data.map(user => ({
      ...user,
      profile: user.user_profiles || null,
      roles: user.user_roles
        ?.filter((ur: any) => ur.is_active)
        ?.map((ur: any) => ur.roles) || []
    }));

    return keysToCamel(transformedData);
  }

  /**
   * Retrieves a single user by their internal ID.
   */
  static async getUserById(id: string): Promise<UserWithProfile | null> {
    const supabase = await createClient();

    // Query single user with profile and roles using joins
    const { data, error } = await supabase
      .from('auth_users')
      .select(`
        *,
        user_profiles!auth_user_id (
          id,
          display_name,
          avatar_url,
          bio,
          preferences,
          notification_settings,
          dashboard_config,
          created_at,
          updated_at
        ),
        user_roles!user_id (
          id,
          granted_at,
          expires_at,
          is_active,
          roles (
            id,
            name,
            display_name,
            description,
            level,
            is_system_role
          )
        )
      `)
      .eq('id', id)
      .single();

    if (error) {
      console.error(`Error fetching user ${id}:`, error);
      return null;
    }

    // Transform the data to match UserWithProfile type
    const transformedData = {
      ...data,
      profile: data.user_profiles || null,
      roles: data.user_roles
        ?.filter((ur: any) => ur.is_active)
        ?.map((ur: any) => ur.roles) || []
    };

    return keysToCamel(transformedData);
  }

  /**
   * Creates a new user, their profile, and assigns roles.
   */
  static async createUser(
    userData: CreateUserFormValues
  ): Promise<UserWithProfile> {
    const supabase = await createClient();
    const { email, password, displayName, roleIds } = userData;

    // 1. Create user in Supabase Auth
    const { data: authData, error: authError } =
      await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            display_name: displayName,
          },
        },
      });

    if (authError || !authData.user) {
      console.error('Error creating auth user:', authError);
      throw new Error('Failed to create user in authentication system.');
    }

    const supabaseUserId = authData.user.id;

    try {
      // 2. Create corresponding record in auth_users table
      const { data: authUser, error: authUserError } = await supabase
        .from('auth_users')
        .insert({
          supabase_id: supabaseUserId,
          email: email,
        })
        .select('id')
        .single();

      if (authUserError || !authUser) {
        throw new Error(`Failed to create auth_users record: ${authUserError?.message}`);
      }

      const internalUserId = authUser.id;

      // 3. Create user profile
      const { error: profileError } = await supabase
        .from('user_profiles')
        .insert({
          auth_user_id: internalUserId,
          display_name: displayName,
        });

      if (profileError) {
        throw new Error(`Failed to create user profile: ${profileError.message}`);
      }

      // 4. Assign roles using internal user ID
      const userRoles = roleIds.map((roleId) => ({
        user_id: internalUserId,
        role_id: roleId,
      }));

      const { error: rolesError } = await supabase
        .from('user_roles')
        .insert(userRoles);

      if (rolesError) {
        throw new Error(`Failed to assign roles: ${rolesError.message}`);
      }

      // 5. Fetch the newly created user with all details
      const newUser = await this.getUserById(internalUserId);
      if (!newUser) {
        throw new Error('Failed to fetch newly created user.');
      }

      return newUser;

    } catch (error) {
      // Clean up created auth user if anything fails
      console.error('Error in user creation, cleaning up:', error);
      await supabase.auth.admin.deleteUser(supabaseUserId);
      throw error;
    }
  }

  /**
   * Updates an existing user's profile, password, and roles.
   */
  static async updateUser(
    id: string,
    userData: UpdateUserFormValues
  ): Promise<UserWithProfile> {
    const supabase = await createClient();
    const { displayName, email, password, roleIds } = userData;

    // First, get the user's supabase_id for auth updates
    const { data: authUser, error: getUserError } = await supabase
      .from('auth_users')
      .select('supabase_id')
      .eq('id', id)
      .single();

    if (getUserError || !authUser) {
      throw new Error('User not found.');
    }

    // 1. Update user in Supabase Auth (email, password if provided)
    const { error: authError } = await supabase.auth.admin.updateUserById(authUser.supabase_id, {
      email,
      ...(password && { password }),
    });

    if (authError) {
      console.error('Error updating auth user:', authError);
      throw new Error('Failed to update user in authentication system.');
    }

    // 2. Update auth_users table
    const { error: authUsersError } = await supabase
      .from('auth_users')
      .update({ email })
      .eq('id', id);

    if (authUsersError) {
      console.error('Error updating auth_users:', authUsersError);
      throw new Error('Failed to update user email.');
    }

    // 3. Update user profile
    const { error: profileError } = await supabase
      .from('user_profiles')
      .update({ display_name: displayName })
      .eq('auth_user_id', id);

    if (profileError) {
      console.error('Error updating user profile:', profileError);
      throw new Error('Failed to update user profile.');
    }

    // 4. Update roles (delete existing, then insert new ones)
    const { error: deleteRolesError } = await supabase
      .from('user_roles')
      .delete()
      .eq('user_id', id);

    if (deleteRolesError) {
      console.error('Error removing old roles:', deleteRolesError);
      throw new Error('Failed to update user roles (step 1).');
    }

    if (roleIds && roleIds.length > 0) {
      const newUserRoles = roleIds.map((roleId) => ({
        user_id: id,
        role_id: roleId,
      }));

      const { error: insertRolesError } = await supabase
        .from('user_roles')
        .insert(newUserRoles);

      if (insertRolesError) {
        console.error('Error inserting new roles:', insertRolesError);
        throw new Error('Failed to update user roles (step 2).');
      }
    }

    // 5. Fetch the updated user with all details
    const updatedUser = await this.getUserById(id);
    if (!updatedUser) {
      throw new Error('Failed to fetch updated user.');
    }

    return updatedUser;
  }

  /**
   * Deletes a user from the system.
   */
  static async deleteUser(id: string): Promise<void> {
    const supabase = await createClient();

    // First, get the user's supabase_id for auth deletion
    const { data: authUser, error: getUserError } = await supabase
      .from('auth_users')
      .select('supabase_id')
      .eq('id', id)
      .single();

    if (getUserError || !authUser) {
      throw new Error('User not found.');
    }

    // Delete from Supabase Auth (this will cascade to our tables via triggers/policies)
    const { error } = await supabase.auth.admin.deleteUser(authUser.supabase_id);

    if (error) {
      console.error(`Error deleting user ${id}:`, error);
      throw new Error('Failed to delete user.');
    }
  }
}