'use client';

import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { getRolesAction } from '@/features/rbac/actions';
import type { Role } from '@/types/rbac';
import type { UserWithProfile } from '@/types/users';
import {
  createUserFormSchema,
  updateUserFormSchema,
} from '@/features/users/schemas';

interface UserFormProps {
  user?: UserWithProfile | null;
  onSubmit: (
    values: z.infer<typeof createUserFormSchema | typeof updateUserFormSchema>
  ) => void;
  isSubmitting: boolean;
}

export function UserForm({ user, onSubmit, isSubmitting }: UserFormProps) {
  const [roles, setRoles] = useState<Role[]>([]);
  const isEditMode = !!user;

  const formSchema = isEditMode ? updateUserFormSchema : createUserFormSchema;

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      displayName: user?.profile?.displayName || '',
      email: user?.email || '',
      password: '',
      roleIds: user?.roles?.map((r) => r.id) || [],
    },
  });

  useEffect(() => {
    async function loadRoles() {
      const result = await getRolesAction();
      if (result.data) {
        setRoles(result.data);
      }
    }
    loadRoles();
  }, []);
  
  // This is a workaround to allow multiple selection on a native select element.
  // For a better user experience, a multi-select component from a library like `react-select` or a custom one would be better.
  const handleRoleChange = (roleId: string) => {
    const currentRoleIds = form.getValues('roleIds') || [];
    const newRoleIds = currentRoleIds.includes(roleId)
      ? currentRoleIds.filter((id) => id !== roleId)
      : [...currentRoleIds, roleId];
    form.setValue('roleIds', newRoleIds, { shouldValidate: true });
  };


  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="displayName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Nom d'affichage</FormLabel>
              <FormControl>
                <Input placeholder="John Doe" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input
                  type="email"
                  placeholder="<EMAIL>"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Mot de passe</FormLabel>
              <FormControl>
                <Input
                  type="password"
                  placeholder={
                    isEditMode ? 'Laisser vide pour ne pas changer' : '********'
                  }
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="roleIds"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Rôles</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value.join(',')}>
                <FormControl>
                    {/* This is a simplified multi-select. A proper component should be used in a real app. */}
                    <div className="flex flex-wrap gap-2">
                        {roles.map((role) => (
                            <Button
                                key={role.id}
                                type="button"
                                variant={field.value.includes(role.id) ? 'default' : 'outline'}
                                onClick={() => handleRoleChange(role.id)}
                            >
                                {role.name}
                            </Button>
                        ))}
                    </div>
                </FormControl>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting
            ? 'Enregistrement...'
            : isEditMode
            ? 'Mettre à jour'
            : 'Créer'}
        </Button>
      </form>
    </Form>
  );
}