'use client';

import { useState } from 'react';
import { Check, ChevronsUpDown, Plus, Building, User } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '@/components/ui/command';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Badge } from '@/components/ui/badge';
import { useClients } from '@/hooks/use-clients';
import { CreateClientModal } from './create-client-modal';
import type { Client } from '@/types/shared';

interface ClientSelectorProps {
  value: string;
  onValueChange: (value: string) => void;
  disabled?: boolean;
}

export function ClientSelector({ value, onValueChange, disabled }: ClientSelectorProps) {
  const [open, setOpen] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const { data: clients = [] } = useClients();

  const selectedClient = clients.find((client) => client.id === value);

  const getClientIcon = (type: string) => {
    switch (type) {
      case 'INDIVIDUAL':
        return <User className="h-4 w-4" />;
      case 'INSTITUTION':
        return <Building className="h-4 w-4" />;
      default:
        return <User className="h-4 w-4" />;
    }
  };

  const getClientTypeBadge = (type: string) => {
    const variants = {
      INDIVIDUAL: { variant: 'secondary' as const, color: 'bg-blue-100 text-blue-700', label: 'Particulier' },
      INSTITUTION: { variant: 'secondary' as const, color: 'bg-purple-100 text-purple-700', label: 'Institution' },
    };

    const config = variants[type as keyof typeof variants] || variants.INDIVIDUAL;
    
    return (
      <Badge variant={config.variant} className={config.color}>
        {config.label}
      </Badge>
    );
  };

  const handleClientCreated = (newClient: Client) => {
    onValueChange(newClient.id);
    setShowCreateModal(false);
  };

  return (
    <>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between bg-white"
            disabled={disabled}
          >
            {selectedClient ? (
              <div className="flex items-center gap-2 flex-1 min-w-0">
                {getClientIcon(selectedClient.type)}
                <span className="truncate">{selectedClient.name}</span>
                {getClientTypeBadge(selectedClient.type)}
              </div>
            ) : (
              <span className="text-muted-foreground">Sélectionner un client...</span>
            )}
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0" align="start">
          <Command>
            <CommandInput placeholder="Rechercher un client..." />
            <CommandList>
              <CommandEmpty>
                <div className="text-center py-4">
                  <p className="text-sm text-muted-foreground mb-3">
                    Aucun client trouvé
                  </p>
                  <Button
                    size="sm"
                    onClick={() => {
                      setOpen(false);
                      setShowCreateModal(true);
                    }}
                    className="bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Créer un nouveau client
                  </Button>
                </div>
              </CommandEmpty>
              
              <CommandGroup>
                <CommandItem
                  onSelect={() => {
                    setOpen(false);
                    setShowCreateModal(true);
                  }}
                  className="border-b"
                >
                  <Plus className="mr-2 h-4 w-4 text-blue-500" />
                  <span className="text-blue-600 font-medium">Créer un nouveau client</span>
                </CommandItem>
                
                {clients.map((client) => (
                  <CommandItem
                    key={client.id}
                    value={`${client.name} ${client.type}`}
                    onSelect={() => {
                      onValueChange(client.id === value ? '' : client.id);
                      setOpen(false);
                    }}
                  >
                    <Check
                      className={cn(
                        'mr-2 h-4 w-4',
                        value === client.id ? 'opacity-100' : 'opacity-0'
                      )}
                    />
                    <div className="flex items-center gap-2 flex-1 min-w-0">
                      {getClientIcon(client.type)}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2">
                          <span className="font-medium truncate">{client.name}</span>
                          {getClientTypeBadge(client.type)}
                        </div>
                        {client.address && (
                          <p className="text-xs text-muted-foreground truncate mt-1">
                            {client.address}
                          </p>
                        )}
                      </div>
                    </div>
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>

      <CreateClientModal
        open={showCreateModal}
        onOpenChange={setShowCreateModal}
        onClientCreated={handleClientCreated}
      />
    </>
  );
}
