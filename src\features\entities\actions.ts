'use server';

import { EntityService } from './services/entity-service';
import { requireRBACPermission } from '@/utils/rbac/guards';
import { revalidatePath } from 'next/cache';
import type { UpdateEntityData } from '@/types/entities';
import { entitySchema, type EntitySchema } from './schemas';

export async function getHierarchyAction() {
  try {
    // This action might not need a specific permission if the hierarchy is public
    // or filtered by user role in the service. For now, let's allow it.
    const hierarchy = await EntityService.getHierarchy();
    return { data: hierarchy };
  } catch (error: any) {
    console.error('Error in getHierarchyAction:', error.message);
    return { error: 'Failed to fetch organizational hierarchy.' };
  }
}

export async function getNavigationHierarchyAction() {
  try {
    const navigationHierarchy = await EntityService.getNavigationHierarchy();
    return { data: navigationHierarchy };
  } catch (error: any) {
    console.error('Error in getNavigationHierarchyAction:', error.message);
    return { error: 'Failed to fetch navigation hierarchy.' };
  }
}

/**
 * Get entity by ID with feature mapping information
 */
export async function getEntityWithMappingAction(id: string) {
  try {
    const entity = await EntityService.getEntityWithMapping(id);
    if (!entity) {
      return { error: 'Entity not found' };
    }
    return { data: entity };
  } catch (error: any) {
    console.error('Error in getEntityWithMappingAction:', error.message);
    return { error: 'Failed to fetch entity with mapping.' };
  }
}

/**
 * Get all active entities for selection purposes
 */
export async function getEntitiesForSelectionAction() {
  try {
    const { userId } = await requireRBACPermission('admin.persons.read');

    const entities = await EntityService.getAllEntities({ isActive: true });
    return { data: entities };
  } catch (error: any) {
    console.error('Error in getEntitiesForSelectionAction:', error);
    return { error: 'Failed to fetch entities.' };
  }
}

export async function createEntityAction(values: EntitySchema) {
  try {
    const { userId } = await requireRBACPermission('admin.entities.manage');

    const validatedData = entitySchema.safeParse(values);
    if (!validatedData.success) {
      return { error: 'Invalid data provided.' };
    }

    const newEntity = await EntityService.createEntity(validatedData.data, userId);
    
    revalidatePath('/admin/entities');

    return { data: newEntity };
  } catch (error: any) {
    console.error('Error in createEntityAction:', error.message);
    return { error: 'Failed to create entity.' };
  }
}

export async function updateEntityAction(id: string, values: Partial<EntitySchema>) {
  try {
    const { userId } = await requireRBACPermission('admin.entities.manage');

    const validatedData = entitySchema.partial().safeParse(values);
    if (!validatedData.success) {
      return { error: 'Invalid data provided.' };
    }

    const updatedEntity = await EntityService.updateEntity(id, validatedData.data, userId);

    revalidatePath('/admin/entities');

    return { data: updatedEntity };
  } catch (error: any) {
    console.error('Error in updateEntityAction:', error.message);
    return { error: 'Failed to update entity.' };
  }
}

export async function deleteEntityAction(id: string) {
  try {
    const { userId } = await requireRBACPermission('admin.entities.manage');
    revalidatePath('/admin/entities');
    await EntityService.deleteEntity(id, userId);
    return { success: true };
  } catch (error: any) {
    return { error: error.message };
  }
}