// Hook pour la gestion des personnes - KYA Dashboards
'use client';

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { PersonService } from '@/services/person-service';
import type { Person, CreatePersonData, UpdatePersonData } from '@/types/shared';

// Hook pour récupérer toutes les personnes
export function usePersons() {
  return useQuery({
    queryKey: ['persons'],
    queryFn: () => PersonService.getPersons(),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook pour récupérer une personne par ID
export function usePerson(id: string) {
  return useQuery({
    queryKey: ['persons', id],
    queryFn: () => PersonService.getPerson(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  });
}

// Hook pour récupérer les personnes actives seulement
export function useActivePersons() {
  return useQuery({
    queryKey: ['persons', 'active'],
    queryFn: () => PersonService.getActivePersons(),
    staleTime: 5 * 60 * 1000,
  });
}

// Hook pour créer une personne
export function useCreatePerson() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreatePersonData) => PersonService.createPerson(data),
    onSuccess: (newPerson) => {
      // Invalider et refetch les queries des personnes
      queryClient.invalidateQueries({ queryKey: ['persons'] });
      toast.success('Personne créée avec succès !');
    },
    onError: (error: Error) => {
      console.error('Error creating person:', error);
      toast.error('Erreur lors de la création de la personne');
    },
  });
}

// Hook pour mettre à jour une personne
export function useUpdatePerson() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdatePersonData }) =>
      PersonService.updatePerson(id, data),
    onSuccess: (updatedPerson) => {
      // Mettre à jour le cache
      queryClient.setQueryData(['persons', updatedPerson.id], updatedPerson);
      queryClient.invalidateQueries({ queryKey: ['persons'] });
      toast.success('Personne mise à jour avec succès !');
    },
    onError: (error: Error) => {
      console.error('Error updating person:', error);
      toast.error('Erreur lors de la mise à jour de la personne');
    },
  });
}

// Hook pour supprimer une personne
export function useDeletePerson() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => PersonService.deletePerson(id),
    onSuccess: (_, deletedId) => {
      // Supprimer du cache
      queryClient.removeQueries({ queryKey: ['persons', deletedId] });
      queryClient.invalidateQueries({ queryKey: ['persons'] });
      toast.success('Personne supprimée avec succès !');
    },
    onError: (error: Error) => {
      console.error('Error deleting person:', error);
      toast.error('Erreur lors de la suppression de la personne');
    },
  });
}

// Hook pour rechercher des personnes
export function useSearchPersons(searchTerm: string) {
  return useQuery({
    queryKey: ['persons', 'search', searchTerm],
    queryFn: () => PersonService.searchPersons(searchTerm),
    enabled: searchTerm.length >= 2, // Recherche seulement si au moins 2 caractères
    staleTime: 2 * 60 * 1000, // 2 minutes pour les recherches
  });
}
