'use client';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Loader2, Save, User, Users } from 'lucide-react';

import { useCreateClient } from '@/hooks/use-clients';
import type { Client } from '@/types/shared';

// Form validation schema
const clientSchema = z.object({
  name: z.string().min(1, 'Le nom est requis').max(255, 'Le nom est trop long'),
  type: z.enum(['INDIVIDUAL', 'INSTITUTION'], {
    message: 'Le type de client est requis',
  }),
  email: z.string().email('Email invalide').optional().or(z.literal('')),
  phone: z.string().optional(),
  address: z.string().optional(),
  gps_coordinates: z.string().optional(),
});

type ClientFormData = z.infer<typeof clientSchema>;

interface CreateClientModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onClientCreated: (client: Client) => void;
}

export function CreateClientModal({ open, onOpenChange, onClientCreated }: CreateClientModalProps) {
  const createClientMutation = useCreateClient();

  const form = useForm<ClientFormData>({
    resolver: zodResolver(clientSchema),
    defaultValues: {
      name: '',
      type: 'INDIVIDUAL',
      email: '',
      phone: '',
      address: '',
      gps_coordinates: '',
    },
  });

  const onSubmit = async (data: ClientFormData) => {
    try {
      // Prepare contact_info object
      const contactInfo: Record<string, any> = {};
      if (data.email) contactInfo.email = data.email;
      if (data.phone) contactInfo.phone = data.phone;

      const clientData = {
        name: data.name,
        type: data.type,
        contact_info: contactInfo,
        address: data.address || undefined,
        gps_coordinates: data.gps_coordinates || undefined,
      };

      const newClient = await createClientMutation.mutateAsync(clientData);
      onClientCreated(newClient);
      form.reset();
    } catch (error) {
      // Error handling is done in the mutation
      console.error('Error creating client:', error);
    }
  };

  const handleClose = () => {
    if (!createClientMutation.isPending) {
      form.reset();
      onOpenChange(false);
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'INDIVIDUAL':
        return <User className="h-4 w-4" />;
      case 'INSTITUTION':
        return <Users className="h-4 w-4" />;
      default:
        return <User className="h-4 w-4" />;
    }
  };

  const getTypeBadge = (type: string) => {
    const variants = {
      INDIVIDUAL: { color: 'bg-blue-100 text-blue-700', label: 'Particulier' },
      INSTITUTION: { color: 'bg-purple-100 text-purple-700', label: 'Institution' },
    };

    const config = variants[type as keyof typeof variants] || variants.INDIVIDUAL;
    
    return (
      <Badge variant="secondary" className={config.color}>
        {config.label}
      </Badge>
    );
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <div className="p-2 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg">
              <User className="h-4 w-4 text-white" />
            </div>
            Créer un Nouveau Client
          </DialogTitle>
          <DialogDescription>
            Ajoutez les informations du client pour l'associer à l'installation.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nom du Client *</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="Ex: Jean Dupont ou Entreprise ABC"
                        className="bg-white"
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Type de Client *</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger className="bg-white">
                          <SelectValue placeholder="Sélectionner le type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="INDIVIDUAL">
                          <div className="flex items-center gap-2">
                            <User className="h-4 w-4" />
                            <span>Particulier</span>
                          </div>
                        </SelectItem>
                        <SelectItem value="INSTITUTION">
                          <div className="flex items-center gap-2">
                            <Users className="h-4 w-4" />
                            <span>Institution</span>
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input 
                        type="email"
                        placeholder="<EMAIL>"
                        className="bg-white"
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Téléphone</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="+221 XX XXX XX XX"
                        className="bg-white"
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="address"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Adresse</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="Adresse complète du client"
                      className="bg-white min-h-[80px]"
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="gps_coordinates"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Coordonnées GPS</FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="Ex: 14.6928, -17.4467"
                      className="bg-white"
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={createClientMutation.isPending}
              >
                Annuler
              </Button>
              <Button
                type="submit"
                disabled={createClientMutation.isPending}
                className="bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700"
              >
                {createClientMutation.isPending ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Création...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Créer le Client
                  </>
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
