'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Plus, Edit, Trash2, Building, Crown, Calendar, X } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { Calendar as CalendarComponent } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import { cn } from '@/lib/utils';
import { personEntitySchema, updatePersonEntitySchema } from '../schemas';
import { usePersonMutations } from '../hooks/use-person-mutations';
import { getEntitiesForSelectionAction } from '@/features/entities/actions';
import type { 
  PersonWithEntities, 
  PersonEntity,
  PersonEntityFormValues,
  UpdatePersonEntityFormValues
} from '../types';
import type { Entity } from '@/types/entities';

interface PersonEntityManagerProps {
  person?: PersonWithEntities;
  onClose: () => void;
  onUpdate?: () => void;
}

export function PersonEntityManager({ person, onClose, onUpdate }: PersonEntityManagerProps) {
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingAssignment, setEditingAssignment] = useState<PersonEntity | null>(null);
  const [availableEntities, setAvailableEntities] = useState<Entity[]>([]);
  const [isLoadingEntities, setIsLoadingEntities] = useState(true);

  const {
    assignToEntity,
    updateAssignment,
    removeFromEntity,
    isAssigning,
    isUpdatingAssignment,
    isRemovingAssignment
  } = usePersonMutations();

  // Form for adding new assignment
  const addForm = useForm<PersonEntityFormValues>({
    resolver: zodResolver(personEntitySchema),
    defaultValues: {
      entityId: '',
      roleInEntity: '',
      startDate: new Date(),
      isPrimary: false,
    },
  });

  // Form for editing existing assignment
  const editForm = useForm<UpdatePersonEntityFormValues>({
    resolver: zodResolver(updatePersonEntitySchema),
  });

  // Load entities
  useEffect(() => {
    const loadEntities = async () => {
      try {
        const result = await getEntitiesForSelectionAction();
        if (result.data) {
          setAvailableEntities(result.data);
        }
      } catch (error) {
        console.error('Error loading entities:', error);
      } finally {
        setIsLoadingEntities(false);
      }
    };

    loadEntities();
  }, []);

  const formatDate = (date: Date | string) => {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return format(dateObj, 'dd MMM yyyy', { locale: fr });
  };

  const getEntityTypeLabel = (type: string) => {
    const labels: Record<string, string> = {
      'DIRECTION': 'Direction',
      'DEPARTEMENT': 'Département',
      'EQUIPE': 'Équipe',
      'SOUS_EQUIPE': 'Sous-équipe',
    };
    return labels[type] || type;
  };

  const handleAddAssignment = async (values: PersonEntityFormValues) => {
    if (!person) return;

    try {
      const result = await assignToEntity(person.id, values);
      if (result.success) {
        addForm.reset();
        setShowAddForm(false);
        onUpdate?.();
      }
    } catch (error) {
      console.error('Error adding assignment:', error);
    }
  };

  const handleEditAssignment = async (values: UpdatePersonEntityFormValues) => {
    if (!editingAssignment) return;

    try {
      const result = await updateAssignment(editingAssignment.id, values);
      if (result.success) {
        setEditingAssignment(null);
        editForm.reset();
        onUpdate?.();
      }
    } catch (error) {
      console.error('Error updating assignment:', error);
    }
  };

  const handleRemoveAssignment = async (assignmentId: string) => {
    try {
      const result = await removeFromEntity(assignmentId);
      if (result.success) {
        onUpdate?.();
      }
    } catch (error) {
      console.error('Error removing assignment:', error);
    }
  };

  const startEdit = (assignment: PersonEntity) => {
    setEditingAssignment(assignment);
    editForm.reset({
      entityId: assignment.entityId,
      roleInEntity: assignment.roleInEntity || '',
      startDate: assignment.startDate ? new Date(assignment.startDate) : undefined,
      endDate: assignment.endDate ? new Date(assignment.endDate) : undefined,
      isPrimary: assignment.isPrimary || false,
    });
  };

  if (!person) {
    return null;
  }

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Building className="h-5 w-5" />
            Gestion des assignations - {person.fullName}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Add new assignment */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">Assignations d'entités</CardTitle>
                <Button
                  onClick={() => setShowAddForm(!showAddForm)}
                  size="sm"
                  variant={showAddForm ? "outline" : "default"}
                >
                  {showAddForm ? (
                    <>
                      <X className="h-4 w-4 mr-2" />
                      Annuler
                    </>
                  ) : (
                    <>
                      <Plus className="h-4 w-4 mr-2" />
                      Ajouter une entité
                    </>
                  )}
                </Button>
              </div>
            </CardHeader>

            {showAddForm && (
              <CardContent>
                <Form {...addForm}>
                  <form onSubmit={addForm.handleSubmit(handleAddAssignment)} className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={addForm.control}
                        name="entityId"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Entité *</FormLabel>
                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Sélectionner une entité" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {isLoadingEntities ? (
                                  <SelectItem value="loading" disabled>Chargement...</SelectItem>
                                ) : (
                                  availableEntities.map((entity) => (
                                    <SelectItem key={entity.id} value={entity.id}>
                                      <div className="flex items-center gap-2">
                                        <span>{entity.name}</span>
                                        <Badge variant="outline" className="text-xs">
                                          {entity.code}
                                        </Badge>
                                      </div>
                                    </SelectItem>
                                  ))
                                )}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={addForm.control}
                        name="roleInEntity"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Rôle dans l'entité</FormLabel>
                            <FormControl>
                              <Input placeholder="Ex: Développeur Senior" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={addForm.control}
                        name="startDate"
                        render={({ field }) => (
                          <FormItem className="flex flex-col">
                            <FormLabel>Date de début</FormLabel>
                            <Popover>
                              <PopoverTrigger asChild>
                                <FormControl>
                                  <Button
                                    variant="outline"
                                    className={cn(
                                      "pl-3 text-left font-normal",
                                      !field.value && "text-muted-foreground"
                                    )}
                                  >
                                    {field.value ? (
                                      format(field.value, "PPP", { locale: fr })
                                    ) : (
                                      <span>Sélectionner une date</span>
                                    )}
                                    <Calendar className="ml-auto h-4 w-4 opacity-50" />
                                  </Button>
                                </FormControl>
                              </PopoverTrigger>
                              <PopoverContent className="w-auto p-0" align="start">
                                <CalendarComponent
                                  mode="single"
                                  selected={field.value}
                                  onSelect={field.onChange}
                                  disabled={(date) => date > new Date()}
                                  initialFocus
                                  locale={fr}
                                />
                              </PopoverContent>
                            </Popover>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={addForm.control}
                        name="isPrimary"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                            <div className="space-y-0.5">
                              <FormLabel className="text-base">Entité principale</FormLabel>
                              <div className="text-sm text-muted-foreground">
                                Cette entité sera l'entité principale de la personne
                              </div>
                            </div>
                            <FormControl>
                              <Switch
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="flex justify-end gap-2">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => setShowAddForm(false)}
                      >
                        Annuler
                      </Button>
                      <Button type="submit" disabled={isAssigning}>
                        {isAssigning ? 'Ajout...' : 'Ajouter l\'assignation'}
                      </Button>
                    </div>
                  </form>
                </Form>
              </CardContent>
            )}
          </Card>

          {/* Edit assignment modal */}
          {editingAssignment && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Edit className="h-5 w-5" />
                  Modifier l'assignation - {editingAssignment.entity?.name}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Form {...editForm}>
                  <form onSubmit={editForm.handleSubmit(handleEditAssignment)} className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={editForm.control}
                        name="entityId"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Entité *</FormLabel>
                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Sélectionner une entité" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {availableEntities.map((entity) => (
                                  <SelectItem key={entity.id} value={entity.id}>
                                    <div className="flex items-center gap-2">
                                      <span>{entity.name}</span>
                                      <Badge variant="outline" className="text-xs">
                                        {entity.code}
                                      </Badge>
                                    </div>
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={editForm.control}
                        name="roleInEntity"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Rôle dans l'entité</FormLabel>
                            <FormControl>
                              <Input placeholder="Ex: Développeur Senior" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={editForm.control}
                        name="startDate"
                        render={({ field }) => (
                          <FormItem className="flex flex-col">
                            <FormLabel>Date de début</FormLabel>
                            <Popover>
                              <PopoverTrigger asChild>
                                <FormControl>
                                  <Button
                                    variant="outline"
                                    className={cn(
                                      "pl-3 text-left font-normal",
                                      !field.value && "text-muted-foreground"
                                    )}
                                  >
                                    {field.value ? (
                                      format(field.value, "PPP", { locale: fr })
                                    ) : (
                                      <span>Sélectionner une date</span>
                                    )}
                                    <Calendar className="ml-auto h-4 w-4 opacity-50" />
                                  </Button>
                                </FormControl>
                              </PopoverTrigger>
                              <PopoverContent className="w-auto p-0" align="start">
                                <CalendarComponent
                                  mode="single"
                                  selected={field.value}
                                  onSelect={field.onChange}
                                  disabled={(date) => date > new Date()}
                                  initialFocus
                                  locale={fr}
                                />
                              </PopoverContent>
                            </Popover>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={editForm.control}
                        name="endDate"
                        render={({ field }) => (
                          <FormItem className="flex flex-col">
                            <FormLabel>Date de fin</FormLabel>
                            <Popover>
                              <PopoverTrigger asChild>
                                <FormControl>
                                  <Button
                                    variant="outline"
                                    className={cn(
                                      "pl-3 text-left font-normal",
                                      !field.value && "text-muted-foreground"
                                    )}
                                  >
                                    {field.value ? (
                                      format(field.value, "PPP", { locale: fr })
                                    ) : (
                                      <span>Sélectionner une date (optionnel)</span>
                                    )}
                                    <Calendar className="ml-auto h-4 w-4 opacity-50" />
                                  </Button>
                                </FormControl>
                              </PopoverTrigger>
                              <PopoverContent className="w-auto p-0" align="start">
                                <CalendarComponent
                                  mode="single"
                                  selected={field.value}
                                  onSelect={field.onChange}
                                  initialFocus
                                  locale={fr}
                                />
                              </PopoverContent>
                            </Popover>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={editForm.control}
                        name="isPrimary"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4 md:col-span-2">
                            <div className="space-y-0.5">
                              <FormLabel className="text-base">Entité principale</FormLabel>
                              <div className="text-sm text-muted-foreground">
                                Cette entité sera l'entité principale de la personne
                              </div>
                            </div>
                            <FormControl>
                              <Switch
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="flex justify-end gap-2">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => setEditingAssignment(null)}
                      >
                        Annuler
                      </Button>
                      <Button type="submit" disabled={isUpdatingAssignment}>
                        {isUpdatingAssignment ? 'Mise à jour...' : 'Mettre à jour'}
                      </Button>
                    </div>
                  </form>
                </Form>
              </CardContent>
            </Card>
          )}

          {/* Current assignments */}
          <div className="space-y-4">
            {person.entities && person.entities.length > 0 ? (
              person.entities.map((assignment) => (
                <Card key={assignment.id} className="relative">
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <Building className="h-5 w-5 text-muted-foreground" />
                        <div>
                          <CardTitle className="text-base">
                            {assignment.entity?.name || 'Entité inconnue'}
                          </CardTitle>
                          <div className="flex items-center gap-2 mt-1">
                            <Badge variant="outline" className="text-xs">
                              {getEntityTypeLabel(assignment.entity?.type || '')}
                            </Badge>
                            {assignment.entity?.code && (
                              <Badge variant="secondary" className="text-xs">
                                {assignment.entity.code}
                              </Badge>
                            )}
                            {assignment.isPrimary && (
                              <Badge variant="default" className="text-xs flex items-center gap-1">
                                <Crown className="h-3 w-3" />
                                Principale
                              </Badge>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => startEdit(assignment)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleRemoveAssignment(assignment.id)}
                          disabled={isRemovingAssignment}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardHeader>

                  <CardContent className="pt-0">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                      {assignment.roleInEntity && (
                        <div>
                          <span className="font-medium text-muted-foreground">Rôle:</span>
                          <p className="mt-1">{assignment.roleInEntity}</p>
                        </div>
                      )}

                      <div>
                        <span className="font-medium text-muted-foreground">Date de début:</span>
                        <p className="mt-1 flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          {assignment.startDate ? formatDate(assignment.startDate) : 'Non définie'}
                        </p>
                      </div>

                      {assignment.endDate && (
                        <div>
                          <span className="font-medium text-muted-foreground">Date de fin:</span>
                          <p className="mt-1 flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            {formatDate(assignment.endDate)}
                          </p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))
            ) : (
              <Card>
                <CardContent className="py-8 text-center text-muted-foreground">
                  <Building className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>Aucune assignation d'entité trouvée.</p>
                  <p className="text-sm mt-1">
                    Cliquez sur "Ajouter une entité" pour commencer.
                  </p>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
