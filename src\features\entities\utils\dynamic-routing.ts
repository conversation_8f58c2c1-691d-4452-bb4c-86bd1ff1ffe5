// Dynamic Routing Utilities - KYA Dashboards
import type { Entity, BusinessModule } from '@/types/entities';
import { EntityFeatureMappingService } from '../services/entity-feature-mapping';

/**
 * Route configuration for dynamic business modules
 */
export interface DynamicRouteConfig {
  businessModule: BusinessModule;
  component: string;
  basePath: string;
  requiresEntityId: boolean;
  permissions?: string[];
}

/**
 * Configuration for dynamic routes
 */
const DYNAMIC_ROUTE_CONFIGS: Record<BusinessModule, DynamicRouteConfig> = {
  installations: {
    businessModule: 'installations',
    component: 'InstallationDashboard',
    basePath: '/installations',
    requiresEntityId: true,
    permissions: ['installations.access'],
  },
  maintenance: {
    businessModule: 'maintenance',
    component: 'MaintenanceDashboard',
    basePath: '/maintenance',
    requiresEntityId: true,
    permissions: ['maintenance.access'],
  },
  commercial: {
    businessModule: 'commercial',
    component: 'CommercialDashboard',
    basePath: '/commercial',
    requiresEntityId: true,
    permissions: ['commercial.access'],
  },
  projects: {
    businessModule: 'projects',
    component: 'ProjectsDashboard',
    basePath: '/projects',
    requiresEntityId: true,
    permissions: ['projects.access'],
  },
  support: {
    businessModule: 'support',
    component: 'SupportDashboard',
    basePath: '/support',
    requiresEntityId: true,
    permissions: ['support.access'],
  },
};

export class DynamicRoutingService {
  /**
   * Generate dynamic route for an entity
   */
  static generateEntityRoute(entity: Entity): string {
    const businessModule = EntityFeatureMappingService.getBusinessModuleByCode(entity.code);
    
    if (businessModule) {
      const config = DYNAMIC_ROUTE_CONFIGS[businessModule];
      if (config.requiresEntityId) {
        return `${config.basePath}/${entity.id}`;
      }
      return config.basePath;
    }
    
    // Fallback to default entity routing
    return EntityFeatureMappingService.getDefaultFeaturePath(entity);
  }

  /**
   * Get route configuration for a business module
   */
  static getRouteConfig(businessModule: BusinessModule): DynamicRouteConfig | null {
    return DYNAMIC_ROUTE_CONFIGS[businessModule] || null;
  }

  /**
   * Check if a business module exists
   */
  static isValidBusinessModule(module: string): module is BusinessModule {
    return Object.keys(DYNAMIC_ROUTE_CONFIGS).includes(module);
  }

  /**
   * Get component name for a business module
   */
  static getComponentName(businessModule: BusinessModule): string | null {
    const config = DYNAMIC_ROUTE_CONFIGS[businessModule];
    return config?.component || null;
  }

  /**
   * Generate breadcrumb path for entity navigation
   */
  static generateBreadcrumbs(entity: Entity, hierarchy: Entity[] = []): Array<{name: string, path: string}> {
    const breadcrumbs: Array<{name: string, path: string}> = [
      { name: 'Dashboard', path: '/dashboard' }
    ];

    // Add parent entities to breadcrumbs
    hierarchy.forEach(parent => {
      breadcrumbs.push({
        name: parent.name,
        path: this.generateEntityRoute(parent)
      });
    });

    // Add current entity
    breadcrumbs.push({
      name: entity.name,
      path: this.generateEntityRoute(entity)
    });

    return breadcrumbs;
  }

  /**
   * Parse entity ID from dynamic route
   */
  static parseEntityIdFromRoute(pathname: string, businessModule: BusinessModule): string | null {
    const config = DYNAMIC_ROUTE_CONFIGS[businessModule];
    if (!config.requiresEntityId) return null;

    const basePath = config.basePath;
    const regex = new RegExp(`^${basePath}/([a-f0-9-]+)$`);
    const match = pathname.match(regex);
    
    return match ? match[1] : null;
  }

  /**
   * Check if a route matches a business module pattern
   */
  static matchesBusinessModuleRoute(pathname: string, businessModule: BusinessModule): boolean {
    const config = DYNAMIC_ROUTE_CONFIGS[businessModule];
    return pathname.startsWith(config.basePath);
  }

  /**
   * Get all available dynamic routes
   */
  static getAllDynamicRoutes(): DynamicRouteConfig[] {
    return Object.values(DYNAMIC_ROUTE_CONFIGS);
  }

  /**
   * Generate route parameters for Next.js dynamic routing
   */
  static generateRouteParams(entity: Entity): Record<string, string> {
    const businessModule = EntityFeatureMappingService.getBusinessModuleByCode(entity.code);
    
    const params: Record<string, string> = {
      entityId: entity.id,
      entityCode: entity.code,
      entityType: entity.type,
    };

    if (businessModule) {
      params.businessModule = businessModule;
    }

    return params;
  }

  /**
   * Validate route permissions for an entity
   */
  static validateRoutePermissions(entity: Entity, userPermissions: string[]): boolean {
    const mapping = EntityFeatureMappingService.getFeatureMappingByEntity(entity);
    
    if (!mapping?.permissions) return true; // No specific permissions required
    
    return mapping.permissions.some(permission => userPermissions.includes(permission));
  }

  /**
   * Get feature import path for dynamic loading
   */
  static getFeatureImportPath(businessModule: BusinessModule): string {
    return `@/features/${businessModule}`;
  }

  /**
   * Generate metadata for dynamic routes
   */
  static generateRouteMetadata(entity: Entity, businessModule?: BusinessModule) {
    const module = businessModule || EntityFeatureMappingService.getBusinessModuleByCode(entity.code);
    const mapping = EntityFeatureMappingService.getFeatureMappingByEntity(entity);
    
    return {
      title: `${mapping?.displayName || entity.name} | KYA Dashboards`,
      description: `Gestion ${mapping?.displayName?.toLowerCase() || 'entité'} - ${entity.name}`,
      entityId: entity.id,
      entityCode: entity.code,
      businessModule: module,
    };
  }
}
