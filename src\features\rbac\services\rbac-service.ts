import { createClient } from '@/utils/supabase/client';
import { AuditService } from '@/utils/services/audit-service';
import { keysToCamel } from '@/utils/case-converter';
import type { 
  Role, 
  Permission,
  CreateRoleData,
  UpdateRoleData,
  PermissionMatrix
} from '@/types/rbac';

export class RBACService {

  static async createRole(data: CreateRoleData, createdBy: string): Promise<Role> {
    const supabase = createClient();
    
    const { data: role, error } = await supabase
      .from('roles')
      .insert({
        name: data.name,
        description: data.description,
      })
      .select()
      .single();

    if (error) throw new Error(`Failed to create role: ${error.message}`);

    await AuditService.log('CREATE', 'ROLE', role.id, {
      userId: createdBy,
      newValues: role,
    });

    return keysToCamel(role);
  }

  static async updateRole(roleId: string, data: UpdateRoleData, updatedBy: string): Promise<Role> {
    const supabase = createClient();
    
    const { data: role, error } = await supabase
      .from('roles')
      .update({
        name: data.name,
        description: data.description,
      })
      .eq('id', roleId)
      .select()
      .single();

    if (error) throw new Error(`Failed to update role: ${error.message}`);

    await AuditService.log('UPDATE', 'ROLE', roleId, {
      userId: updatedBy,
      newValues: role,
    });

    return keysToCamel(role);
  }

  static async deleteRole(roleId: string, deletedBy: string): Promise<void> {
    const supabase = createClient();

    await AuditService.log('DELETE', 'ROLE', roleId, {
      userId: deletedBy,
    });

    const { error } = await supabase.from('roles').delete().eq('id', roleId);
    if (error) throw new Error(`Failed to delete role: ${error.message}`);
  }

  static async getRoles(): Promise<Role[]> {
    const supabase = createClient();
    const { data: roles, error } = await supabase
      .from('roles')
      .select('*')
      .order('level', { ascending: true });

    if (error) throw new Error(`Failed to fetch roles: ${error.message}`);
    
    return keysToCamel(roles);
  }

  static async getPermissions(): Promise<Permission[]> {
    const supabase = createClient();
    const { data: permissions, error } = await supabase
      .from('permissions')
      .select('*')
      .order('resource', { ascending: true });

    if (error) throw new Error(`Failed to fetch permissions: ${error.message}`);

    return keysToCamel(permissions);
  }
  
  static async getUserPermissions(userId: string): Promise<string[]> {
    const supabase = createClient();
    
    // This query joins user_roles, roles_permissions, and permissions
    // to get all permission names for a given user.
    const { data, error } = await supabase
      .from('user_roles')
      .select(`
        roles (
          roles_permissions (
            permissions (name)
          )
        )
      `)
      .eq('user_id', userId);

    if (error) {
      console.error('Error fetching user permissions:', error);
      return [];
    }

    const permissionSet = new Set<string>();
    data.forEach((userRole: any) => {
      userRole.roles?.roles_permissions?.forEach((rp: any) => {
        if (rp.permissions?.name) {
          permissionSet.add(rp.permissions.name);
        }
      });
    });

    return Array.from(permissionSet);
  }

  static async updateRolePermissions(roleId: string, permissionIds: string[], assignedBy: string): Promise<void> {
    const supabase = createClient();

    const { error: deleteError } = await supabase
      .from('roles_permissions')
      .delete()
      .eq('role_id', roleId);

    if (deleteError) throw new Error(`Failed to clear existing permissions: ${deleteError.message}`);

    if (permissionIds.length > 0) {
      const assignments = permissionIds.map(permissionId => ({
        role_id: roleId,
        permission_id: permissionId,
        granted_by: assignedBy,
      }));

      const { error: insertError } = await supabase
        .from('roles_permissions')
        .insert(assignments);

      if (insertError) throw new Error(`Failed to assign new permissions: ${insertError.message}`);
    }

    await AuditService.log('UPDATE_PERMISSIONS', 'ROLE', roleId, {
      userId: assignedBy,
      newValues: { permissionIds },
    });
  }

  static async getPermissionMatrix(): Promise<PermissionMatrix> {
    const supabase = createClient();
    
    const [roles, permissions, assignmentsResult] = await Promise.all([
      this.getRoles(),
      this.getPermissions(),
      supabase.from('roles_permissions').select('*')
    ]);

    if (assignmentsResult.error) throw assignmentsResult.error;

    const assignmentMap = new Map<string, Set<string>>();
    assignmentsResult.data.forEach(a => {
      if (!assignmentMap.has(a.role_id)) {
        assignmentMap.set(a.role_id, new Set());
      }
      assignmentMap.get(a.role_id)!.add(a.permission_id);
    });

    return {
      roles,
      permissions,
      assignments: assignmentMap,
    };
  }
  
  static async checkUserPermission(userId: string, permissionName: string): Promise<boolean> {
    const supabase = createClient();

    // First check if user is SUPER_ADMIN - they have all permissions
    const { data: userRoles } = await supabase
      .from('user_roles')
      .select(`
        roles (
          name
        )
      `)
      .eq('user_id', userId)
      .eq('is_active', true);

    const isSuperAdmin = userRoles?.some((ur: any) => ur.roles?.name === 'SUPER_ADMIN');

    if (isSuperAdmin) {
      console.log(`SUPER_ADMIN ${userId} has permission: ${permissionName} (bypassed)`);
      return true;
    }

    // Normal permission check using RPC function
    const { data, error } = await supabase.rpc('check_user_permission', {
      p_user_id: userId,
      p_permission_name: permissionName
    });

    if (error) {
      console.error("Error checking permission:", error);
      return false;
    }

    return data || false;
  }
}
